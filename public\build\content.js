var __getOwnPropNames = Object.getOwnPropertyNames;
var __require = /* @__PURE__ */ ((x2) => typeof require !== "undefined" ? require : typeof Proxy !== "undefined" ? new Proxy(x2, {
  get: (a2, b2) => (typeof require !== "undefined" ? require : a2)[b2]
}) : x2)(function(x2) {
  if (typeof require !== "undefined") return require.apply(this, arguments);
  throw Error('Dynamic require of "' + x2 + '" is not supported');
});
var __commonJS = (cb, mod) => function __require2() {
  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
};

// src/assets/aec.js
var require_aec = __commonJS({
  "src/assets/aec.js"(exports, module) {
    var createAecModule = (() => {
      var _scriptName = typeof document != "undefined" ? document.currentScript?.src : void 0;
      return async function(moduleArg = {}) {
        var moduleRtn;
        var Module = moduleArg;
        var ENVIRONMENT_IS_WEB = typeof window == "object";
        var ENVIRONMENT_IS_WORKER = typeof WorkerGlobalScope != "undefined";
        var ENVIRONMENT_IS_NODE = typeof process == "object" && process.versions?.node && process.type != "renderer";
        var arguments_ = [];
        var thisProgram = "./this.program";
        var quit_ = (status, toThrow) => {
          throw toThrow;
        };
        if (ENVIRONMENT_IS_WORKER) {
          _scriptName = self.location.href;
        }
        var scriptDirectory = "";
        var readAsync, readBinary;
        if (ENVIRONMENT_IS_WEB || ENVIRONMENT_IS_WORKER) {
          try {
            scriptDirectory = new URL(".", _scriptName).href;
          } catch {
          }
          {
            if (ENVIRONMENT_IS_WORKER) {
              readBinary = (url) => {
                var xhr = new XMLHttpRequest();
                xhr.open("GET", url, false);
                xhr.responseType = "arraybuffer";
                xhr.send(null);
                return new Uint8Array(xhr.response);
              };
            }
            readAsync = async (url) => {
              var response = await fetch(url, { credentials: "same-origin" });
              if (response.ok) {
                return response.arrayBuffer();
              }
              throw new Error(response.status + " : " + response.url);
            };
          }
        } else {
        }
        var out = console.log.bind(console);
        var err = console.error.bind(console);
        var wasmBinary;
        var ABORT = false;
        var EXITSTATUS;
        var readyPromiseResolve, readyPromiseReject;
        var wasmMemory;
        var HEAP8, HEAPU8, HEAP16, HEAPU16, HEAP32, HEAPU32, HEAPF32, HEAPF64;
        var HEAP64, HEAPU64;
        var runtimeInitialized = false;
        function updateMemoryViews() {
          var b2 = wasmMemory.buffer;
          HEAP8 = new Int8Array(b2);
          HEAP16 = new Int16Array(b2);
          HEAPU8 = new Uint8Array(b2);
          HEAPU16 = new Uint16Array(b2);
          HEAP32 = new Int32Array(b2);
          HEAPU32 = new Uint32Array(b2);
          HEAPF32 = new Float32Array(b2);
          HEAPF64 = new Float64Array(b2);
          HEAP64 = new BigInt64Array(b2);
          HEAPU64 = new BigUint64Array(b2);
        }
        function preRun() {
          if (Module["preRun"]) {
            if (typeof Module["preRun"] == "function") Module["preRun"] = [Module["preRun"]];
            while (Module["preRun"].length) {
              addOnPreRun(Module["preRun"].shift());
            }
          }
          callRuntimeCallbacks(onPreRuns);
        }
        function initRuntime() {
          runtimeInitialized = true;
          if (!Module["noFSInit"] && !FS.initialized) FS.init();
          TTY.init();
          wasmExports["v"]();
          FS.ignorePermissions = false;
        }
        function postRun() {
          if (Module["postRun"]) {
            if (typeof Module["postRun"] == "function") Module["postRun"] = [Module["postRun"]];
            while (Module["postRun"].length) {
              addOnPostRun(Module["postRun"].shift());
            }
          }
          callRuntimeCallbacks(onPostRuns);
        }
        var runDependencies = 0;
        var dependenciesFulfilled = null;
        function addRunDependency(id) {
          runDependencies++;
          Module["monitorRunDependencies"]?.(runDependencies);
        }
        function removeRunDependency(id) {
          runDependencies--;
          Module["monitorRunDependencies"]?.(runDependencies);
          if (runDependencies == 0) {
            if (dependenciesFulfilled) {
              var callback = dependenciesFulfilled;
              dependenciesFulfilled = null;
              callback();
            }
          }
        }
        function abort(what) {
          Module["onAbort"]?.(what);
          what = "Aborted(" + what + ")";
          err(what);
          ABORT = true;
          what += ". Build with -sASSERTIONS for more info.";
          var e2 = new WebAssembly.RuntimeError(what);
          readyPromiseReject?.(e2);
          throw e2;
        }
        var wasmBinaryFile;
        function findWasmBinary() {
          return base64Decode("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");
        }
        function getBinarySync(file) {
          if (ArrayBuffer.isView(file)) {
            return file;
          }
          if (file == wasmBinaryFile && wasmBinary) {
            return new Uint8Array(wasmBinary);
          }
          if (readBinary) {
            return readBinary(file);
          }
          throw "both async and sync fetching of the wasm failed";
        }
        async function getWasmBinary(binaryFile) {
          return getBinarySync(binaryFile);
        }
        async function instantiateArrayBuffer(binaryFile, imports) {
          try {
            var binary = await getWasmBinary(binaryFile);
            var instance = await WebAssembly.instantiate(binary, imports);
            return instance;
          } catch (reason) {
            err(`failed to asynchronously prepare wasm: ${reason}`);
            abort(reason);
          }
        }
        async function instantiateAsync(binary, binaryFile, imports) {
          return instantiateArrayBuffer(binaryFile, imports);
        }
        function getWasmImports() {
          return { a: wasmImports };
        }
        async function createWasm() {
          function receiveInstance(instance, module2) {
            wasmExports = instance.exports;
            wasmMemory = wasmExports["u"];
            updateMemoryViews();
            wasmTable = wasmExports["H"];
            assignWasmExports(wasmExports);
            removeRunDependency("wasm-instantiate");
            return wasmExports;
          }
          addRunDependency("wasm-instantiate");
          function receiveInstantiationResult(result2) {
            return receiveInstance(result2["instance"]);
          }
          var info = getWasmImports();
          if (Module["instantiateWasm"]) {
            return new Promise((resolve, reject) => {
              Module["instantiateWasm"](info, (mod, inst) => {
                resolve(receiveInstance(mod, inst));
              });
            });
          }
          wasmBinaryFile ??= findWasmBinary();
          var result = await instantiateAsync(wasmBinary, wasmBinaryFile, info);
          var exports2 = receiveInstantiationResult(result);
          return exports2;
        }
        class ExitStatus {
          name = "ExitStatus";
          constructor(status) {
            this.message = `Program terminated with exit(${status})`;
            this.status = status;
          }
        }
        var callRuntimeCallbacks = (callbacks) => {
          while (callbacks.length > 0) {
            callbacks.shift()(Module);
          }
        };
        var onPostRuns = [];
        var addOnPostRun = (cb) => onPostRuns.push(cb);
        var onPreRuns = [];
        var addOnPreRun = (cb) => onPreRuns.push(cb);
        var base64Decode = (b64) => {
          var b1, b2, i3 = 0, j2 = 0, bLength = b64.length;
          var output = new Uint8Array((bLength * 3 >> 2) - (b64[bLength - 2] == "=") - (b64[bLength - 1] == "="));
          for (; i3 < bLength; i3 += 4, j2 += 3) {
            b1 = base64ReverseLookup[b64.charCodeAt(i3 + 1)];
            b2 = base64ReverseLookup[b64.charCodeAt(i3 + 2)];
            output[j2] = base64ReverseLookup[b64.charCodeAt(i3)] << 2 | b1 >> 4;
            output[j2 + 1] = b1 << 4 | b2 >> 2;
            output[j2 + 2] = b2 << 6 | base64ReverseLookup[b64.charCodeAt(i3 + 3)];
          }
          return output;
        };
        var noExitRuntime = true;
        var stackRestore = (val) => __emscripten_stack_restore(val);
        var stackSave = () => _emscripten_stack_get_current();
        var exceptionLast = 0;
        class ExceptionInfo {
          constructor(excPtr) {
            this.excPtr = excPtr;
            this.ptr = excPtr - 24;
          }
          set_type(type) {
            HEAPU32[this.ptr + 4 >> 2] = type;
          }
          get_type() {
            return HEAPU32[this.ptr + 4 >> 2];
          }
          set_destructor(destructor) {
            HEAPU32[this.ptr + 8 >> 2] = destructor;
          }
          get_destructor() {
            return HEAPU32[this.ptr + 8 >> 2];
          }
          set_caught(caught) {
            caught = caught ? 1 : 0;
            HEAP8[this.ptr + 12] = caught;
          }
          get_caught() {
            return HEAP8[this.ptr + 12] != 0;
          }
          set_rethrown(rethrown) {
            rethrown = rethrown ? 1 : 0;
            HEAP8[this.ptr + 13] = rethrown;
          }
          get_rethrown() {
            return HEAP8[this.ptr + 13] != 0;
          }
          init(type, destructor) {
            this.set_adjusted_ptr(0);
            this.set_type(type);
            this.set_destructor(destructor);
          }
          set_adjusted_ptr(adjustedPtr) {
            HEAPU32[this.ptr + 16 >> 2] = adjustedPtr;
          }
          get_adjusted_ptr() {
            return HEAPU32[this.ptr + 16 >> 2];
          }
        }
        var setTempRet0 = (val) => __emscripten_tempret_set(val);
        var findMatchingCatch = (args) => {
          var thrown = exceptionLast;
          if (!thrown) {
            setTempRet0(0);
            return 0;
          }
          var info = new ExceptionInfo(thrown);
          info.set_adjusted_ptr(thrown);
          var thrownType = info.get_type();
          if (!thrownType) {
            setTempRet0(0);
            return thrown;
          }
          for (var caughtType of args) {
            if (caughtType === 0 || caughtType === thrownType) {
              break;
            }
            var adjusted_ptr_addr = info.ptr + 16;
            if (___cxa_can_catch(caughtType, thrownType, adjusted_ptr_addr)) {
              setTempRet0(caughtType);
              return thrown;
            }
          }
          setTempRet0(thrownType);
          return thrown;
        };
        var ___cxa_find_matching_catch_2 = () => findMatchingCatch([]);
        var uncaughtExceptionCount = 0;
        var ___cxa_throw = (ptr, type, destructor) => {
          var info = new ExceptionInfo(ptr);
          info.init(type, destructor);
          exceptionLast = ptr;
          uncaughtExceptionCount++;
          throw exceptionLast;
        };
        var ___resumeException = (ptr) => {
          if (!exceptionLast) {
            exceptionLast = ptr;
          }
          throw exceptionLast;
        };
        var lengthBytesUTF8 = (str) => {
          var len = 0;
          for (var i3 = 0; i3 < str.length; ++i3) {
            var c2 = str.charCodeAt(i3);
            if (c2 <= 127) {
              len++;
            } else if (c2 <= 2047) {
              len += 2;
            } else if (c2 >= 55296 && c2 <= 57343) {
              len += 4;
              ++i3;
            } else {
              len += 3;
            }
          }
          return len;
        };
        var stringToUTF8Array = (str, heap, outIdx, maxBytesToWrite) => {
          if (!(maxBytesToWrite > 0)) return 0;
          var startIdx = outIdx;
          var endIdx = outIdx + maxBytesToWrite - 1;
          for (var i3 = 0; i3 < str.length; ++i3) {
            var u2 = str.codePointAt(i3);
            if (u2 <= 127) {
              if (outIdx >= endIdx) break;
              heap[outIdx++] = u2;
            } else if (u2 <= 2047) {
              if (outIdx + 1 >= endIdx) break;
              heap[outIdx++] = 192 | u2 >> 6;
              heap[outIdx++] = 128 | u2 & 63;
            } else if (u2 <= 65535) {
              if (outIdx + 2 >= endIdx) break;
              heap[outIdx++] = 224 | u2 >> 12;
              heap[outIdx++] = 128 | u2 >> 6 & 63;
              heap[outIdx++] = 128 | u2 & 63;
            } else {
              if (outIdx + 3 >= endIdx) break;
              heap[outIdx++] = 240 | u2 >> 18;
              heap[outIdx++] = 128 | u2 >> 12 & 63;
              heap[outIdx++] = 128 | u2 >> 6 & 63;
              heap[outIdx++] = 128 | u2 & 63;
              i3++;
            }
          }
          heap[outIdx] = 0;
          return outIdx - startIdx;
        };
        var stringToUTF8 = (str, outPtr, maxBytesToWrite) => stringToUTF8Array(str, HEAPU8, outPtr, maxBytesToWrite);
        function ___syscall_getcwd(buf, size) {
          try {
            if (size === 0) return -28;
            var cwd = FS.cwd();
            var cwdLengthInBytes = lengthBytesUTF8(cwd) + 1;
            if (size < cwdLengthInBytes) return -68;
            stringToUTF8(cwd, buf, size);
            return cwdLengthInBytes;
          } catch (e2) {
            if (typeof FS == "undefined" || !(e2.name === "ErrnoError")) throw e2;
            return -e2.errno;
          }
        }
        var __abort_js = () => abort("");
        var abortOnCannotGrowMemory = (requestedSize) => {
          abort("OOM");
        };
        var _emscripten_resize_heap = (requestedSize) => {
          var oldSize = HEAPU8.length;
          requestedSize >>>= 0;
          abortOnCannotGrowMemory(requestedSize);
        };
        var ENV = {};
        var getExecutableName = () => thisProgram || "./this.program";
        var getEnvStrings = () => {
          if (!getEnvStrings.strings) {
            var lang = (typeof navigator == "object" && navigator.language || "C").replace("-", "_") + ".UTF-8";
            var env = { USER: "web_user", LOGNAME: "web_user", PATH: "/", PWD: "/", HOME: "/home/<USER>", LANG: lang, _: getExecutableName() };
            for (var x2 in ENV) {
              if (ENV[x2] === void 0) delete env[x2];
              else env[x2] = ENV[x2];
            }
            var strings = [];
            for (var x2 in env) {
              strings.push(`${x2}=${env[x2]}`);
            }
            getEnvStrings.strings = strings;
          }
          return getEnvStrings.strings;
        };
        var _environ_get = (__environ, environ_buf) => {
          var bufSize = 0;
          var envp = 0;
          for (var string of getEnvStrings()) {
            var ptr = environ_buf + bufSize;
            HEAPU32[__environ + envp >> 2] = ptr;
            bufSize += stringToUTF8(string, ptr, Infinity) + 1;
            envp += 4;
          }
          return 0;
        };
        var _environ_sizes_get = (penviron_count, penviron_buf_size) => {
          var strings = getEnvStrings();
          HEAPU32[penviron_count >> 2] = strings.length;
          var bufSize = 0;
          for (var string of strings) {
            bufSize += lengthBytesUTF8(string) + 1;
          }
          HEAPU32[penviron_buf_size >> 2] = bufSize;
          return 0;
        };
        var runtimeKeepaliveCounter = 0;
        var keepRuntimeAlive = () => noExitRuntime || runtimeKeepaliveCounter > 0;
        var _proc_exit = (code) => {
          EXITSTATUS = code;
          if (!keepRuntimeAlive()) {
            Module["onExit"]?.(code);
            ABORT = true;
          }
          quit_(code, new ExitStatus(code));
        };
        var exitJS = (status, implicit) => {
          EXITSTATUS = status;
          _proc_exit(status);
        };
        var _exit = exitJS;
        var PATH = { isAbs: (path) => path.charAt(0) === "/", splitPath: (filename) => {
          var splitPathRe = /^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/;
          return splitPathRe.exec(filename).slice(1);
        }, normalizeArray: (parts, allowAboveRoot) => {
          var up = 0;
          for (var i3 = parts.length - 1; i3 >= 0; i3--) {
            var last = parts[i3];
            if (last === ".") {
              parts.splice(i3, 1);
            } else if (last === "..") {
              parts.splice(i3, 1);
              up++;
            } else if (up) {
              parts.splice(i3, 1);
              up--;
            }
          }
          if (allowAboveRoot) {
            for (; up; up--) {
              parts.unshift("..");
            }
          }
          return parts;
        }, normalize: (path) => {
          var isAbsolute = PATH.isAbs(path), trailingSlash = path.slice(-1) === "/";
          path = PATH.normalizeArray(path.split("/").filter((p2) => !!p2), !isAbsolute).join("/");
          if (!path && !isAbsolute) {
            path = ".";
          }
          if (path && trailingSlash) {
            path += "/";
          }
          return (isAbsolute ? "/" : "") + path;
        }, dirname: (path) => {
          var result = PATH.splitPath(path), root = result[0], dir = result[1];
          if (!root && !dir) {
            return ".";
          }
          if (dir) {
            dir = dir.slice(0, -1);
          }
          return root + dir;
        }, basename: (path) => path && path.match(/([^\/]+|\/)\/*$/)[1], join: (...paths) => PATH.normalize(paths.join("/")), join2: (l2, r2) => PATH.normalize(l2 + "/" + r2) };
        var initRandomFill = () => (view) => crypto.getRandomValues(view);
        var randomFill = (view) => {
          (randomFill = initRandomFill())(view);
        };
        var PATH_FS = { resolve: (...args) => {
          var resolvedPath = "", resolvedAbsolute = false;
          for (var i3 = args.length - 1; i3 >= -1 && !resolvedAbsolute; i3--) {
            var path = i3 >= 0 ? args[i3] : FS.cwd();
            if (typeof path != "string") {
              throw new TypeError("Arguments to path.resolve must be strings");
            } else if (!path) {
              return "";
            }
            resolvedPath = path + "/" + resolvedPath;
            resolvedAbsolute = PATH.isAbs(path);
          }
          resolvedPath = PATH.normalizeArray(resolvedPath.split("/").filter((p2) => !!p2), !resolvedAbsolute).join("/");
          return (resolvedAbsolute ? "/" : "") + resolvedPath || ".";
        }, relative: (from, to) => {
          from = PATH_FS.resolve(from).slice(1);
          to = PATH_FS.resolve(to).slice(1);
          function trim(arr) {
            var start = 0;
            for (; start < arr.length; start++) {
              if (arr[start] !== "") break;
            }
            var end = arr.length - 1;
            for (; end >= 0; end--) {
              if (arr[end] !== "") break;
            }
            if (start > end) return [];
            return arr.slice(start, end - start + 1);
          }
          var fromParts = trim(from.split("/"));
          var toParts = trim(to.split("/"));
          var length = Math.min(fromParts.length, toParts.length);
          var samePartsLength = length;
          for (var i3 = 0; i3 < length; i3++) {
            if (fromParts[i3] !== toParts[i3]) {
              samePartsLength = i3;
              break;
            }
          }
          var outputParts = [];
          for (var i3 = samePartsLength; i3 < fromParts.length; i3++) {
            outputParts.push("..");
          }
          outputParts = outputParts.concat(toParts.slice(samePartsLength));
          return outputParts.join("/");
        } };
        var UTF8Decoder = typeof TextDecoder != "undefined" ? new TextDecoder() : void 0;
        var UTF8ArrayToString = (heapOrArray, idx = 0, maxBytesToRead = NaN) => {
          var endIdx = idx + maxBytesToRead;
          var endPtr = idx;
          while (heapOrArray[endPtr] && !(endPtr >= endIdx)) ++endPtr;
          if (endPtr - idx > 16 && heapOrArray.buffer && UTF8Decoder) {
            return UTF8Decoder.decode(heapOrArray.subarray(idx, endPtr));
          }
          var str = "";
          while (idx < endPtr) {
            var u0 = heapOrArray[idx++];
            if (!(u0 & 128)) {
              str += String.fromCharCode(u0);
              continue;
            }
            var u1 = heapOrArray[idx++] & 63;
            if ((u0 & 224) == 192) {
              str += String.fromCharCode((u0 & 31) << 6 | u1);
              continue;
            }
            var u2 = heapOrArray[idx++] & 63;
            if ((u0 & 240) == 224) {
              u0 = (u0 & 15) << 12 | u1 << 6 | u2;
            } else {
              u0 = (u0 & 7) << 18 | u1 << 12 | u2 << 6 | heapOrArray[idx++] & 63;
            }
            if (u0 < 65536) {
              str += String.fromCharCode(u0);
            } else {
              var ch = u0 - 65536;
              str += String.fromCharCode(55296 | ch >> 10, 56320 | ch & 1023);
            }
          }
          return str;
        };
        var FS_stdin_getChar_buffer = [];
        var intArrayFromString = (stringy, dontAddNull, length) => {
          var len = length > 0 ? length : lengthBytesUTF8(stringy) + 1;
          var u8array = new Array(len);
          var numBytesWritten = stringToUTF8Array(stringy, u8array, 0, u8array.length);
          if (dontAddNull) u8array.length = numBytesWritten;
          return u8array;
        };
        var FS_stdin_getChar = () => {
          if (!FS_stdin_getChar_buffer.length) {
            var result = null;
            if (typeof window != "undefined" && typeof window.prompt == "function") {
              result = window.prompt("Input: ");
              if (result !== null) {
                result += "\n";
              }
            } else {
            }
            if (!result) {
              return null;
            }
            FS_stdin_getChar_buffer = intArrayFromString(result, true);
          }
          return FS_stdin_getChar_buffer.shift();
        };
        var TTY = { ttys: [], init() {
        }, shutdown() {
        }, register(dev, ops) {
          TTY.ttys[dev] = { input: [], output: [], ops };
          FS.registerDevice(dev, TTY.stream_ops);
        }, stream_ops: { open(stream) {
          var tty = TTY.ttys[stream.node.rdev];
          if (!tty) {
            throw new FS.ErrnoError(43);
          }
          stream.tty = tty;
          stream.seekable = false;
        }, close(stream) {
          stream.tty.ops.fsync(stream.tty);
        }, fsync(stream) {
          stream.tty.ops.fsync(stream.tty);
        }, read(stream, buffer, offset, length, pos) {
          if (!stream.tty || !stream.tty.ops.get_char) {
            throw new FS.ErrnoError(60);
          }
          var bytesRead = 0;
          for (var i3 = 0; i3 < length; i3++) {
            var result;
            try {
              result = stream.tty.ops.get_char(stream.tty);
            } catch (e2) {
              throw new FS.ErrnoError(29);
            }
            if (result === void 0 && bytesRead === 0) {
              throw new FS.ErrnoError(6);
            }
            if (result === null || result === void 0) break;
            bytesRead++;
            buffer[offset + i3] = result;
          }
          if (bytesRead) {
            stream.node.atime = Date.now();
          }
          return bytesRead;
        }, write(stream, buffer, offset, length, pos) {
          if (!stream.tty || !stream.tty.ops.put_char) {
            throw new FS.ErrnoError(60);
          }
          try {
            for (var i3 = 0; i3 < length; i3++) {
              stream.tty.ops.put_char(stream.tty, buffer[offset + i3]);
            }
          } catch (e2) {
            throw new FS.ErrnoError(29);
          }
          if (length) {
            stream.node.mtime = stream.node.ctime = Date.now();
          }
          return i3;
        } }, default_tty_ops: { get_char(tty) {
          return FS_stdin_getChar();
        }, put_char(tty, val) {
          if (val === null || val === 10) {
            out(UTF8ArrayToString(tty.output));
            tty.output = [];
          } else {
            if (val != 0) tty.output.push(val);
          }
        }, fsync(tty) {
          if (tty.output?.length > 0) {
            out(UTF8ArrayToString(tty.output));
            tty.output = [];
          }
        }, ioctl_tcgets(tty) {
          return { c_iflag: 25856, c_oflag: 5, c_cflag: 191, c_lflag: 35387, c_cc: [3, 28, 127, 21, 4, 0, 1, 0, 17, 19, 26, 0, 18, 15, 23, 22, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0] };
        }, ioctl_tcsets(tty, optional_actions, data) {
          return 0;
        }, ioctl_tiocgwinsz(tty) {
          return [24, 80];
        } }, default_tty1_ops: { put_char(tty, val) {
          if (val === null || val === 10) {
            err(UTF8ArrayToString(tty.output));
            tty.output = [];
          } else {
            if (val != 0) tty.output.push(val);
          }
        }, fsync(tty) {
          if (tty.output?.length > 0) {
            err(UTF8ArrayToString(tty.output));
            tty.output = [];
          }
        } } };
        var mmapAlloc = (size) => {
          abort();
        };
        var MEMFS = { ops_table: null, mount(mount) {
          return MEMFS.createNode(null, "/", 16895, 0);
        }, createNode(parent, name, mode, dev) {
          if (FS.isBlkdev(mode) || FS.isFIFO(mode)) {
            throw new FS.ErrnoError(63);
          }
          MEMFS.ops_table ||= { dir: { node: { getattr: MEMFS.node_ops.getattr, setattr: MEMFS.node_ops.setattr, lookup: MEMFS.node_ops.lookup, mknod: MEMFS.node_ops.mknod, rename: MEMFS.node_ops.rename, unlink: MEMFS.node_ops.unlink, rmdir: MEMFS.node_ops.rmdir, readdir: MEMFS.node_ops.readdir, symlink: MEMFS.node_ops.symlink }, stream: { llseek: MEMFS.stream_ops.llseek } }, file: { node: { getattr: MEMFS.node_ops.getattr, setattr: MEMFS.node_ops.setattr }, stream: { llseek: MEMFS.stream_ops.llseek, read: MEMFS.stream_ops.read, write: MEMFS.stream_ops.write, mmap: MEMFS.stream_ops.mmap, msync: MEMFS.stream_ops.msync } }, link: { node: { getattr: MEMFS.node_ops.getattr, setattr: MEMFS.node_ops.setattr, readlink: MEMFS.node_ops.readlink }, stream: {} }, chrdev: { node: { getattr: MEMFS.node_ops.getattr, setattr: MEMFS.node_ops.setattr }, stream: FS.chrdev_stream_ops } };
          var node = FS.createNode(parent, name, mode, dev);
          if (FS.isDir(node.mode)) {
            node.node_ops = MEMFS.ops_table.dir.node;
            node.stream_ops = MEMFS.ops_table.dir.stream;
            node.contents = {};
          } else if (FS.isFile(node.mode)) {
            node.node_ops = MEMFS.ops_table.file.node;
            node.stream_ops = MEMFS.ops_table.file.stream;
            node.usedBytes = 0;
            node.contents = null;
          } else if (FS.isLink(node.mode)) {
            node.node_ops = MEMFS.ops_table.link.node;
            node.stream_ops = MEMFS.ops_table.link.stream;
          } else if (FS.isChrdev(node.mode)) {
            node.node_ops = MEMFS.ops_table.chrdev.node;
            node.stream_ops = MEMFS.ops_table.chrdev.stream;
          }
          node.atime = node.mtime = node.ctime = Date.now();
          if (parent) {
            parent.contents[name] = node;
            parent.atime = parent.mtime = parent.ctime = node.atime;
          }
          return node;
        }, getFileDataAsTypedArray(node) {
          if (!node.contents) return new Uint8Array(0);
          if (node.contents.subarray) return node.contents.subarray(0, node.usedBytes);
          return new Uint8Array(node.contents);
        }, expandFileStorage(node, newCapacity) {
          var prevCapacity = node.contents ? node.contents.length : 0;
          if (prevCapacity >= newCapacity) return;
          var CAPACITY_DOUBLING_MAX = 1024 * 1024;
          newCapacity = Math.max(newCapacity, prevCapacity * (prevCapacity < CAPACITY_DOUBLING_MAX ? 2 : 1.125) >>> 0);
          if (prevCapacity != 0) newCapacity = Math.max(newCapacity, 256);
          var oldContents = node.contents;
          node.contents = new Uint8Array(newCapacity);
          if (node.usedBytes > 0) node.contents.set(oldContents.subarray(0, node.usedBytes), 0);
        }, resizeFileStorage(node, newSize) {
          if (node.usedBytes == newSize) return;
          if (newSize == 0) {
            node.contents = null;
            node.usedBytes = 0;
          } else {
            var oldContents = node.contents;
            node.contents = new Uint8Array(newSize);
            if (oldContents) {
              node.contents.set(oldContents.subarray(0, Math.min(newSize, node.usedBytes)));
            }
            node.usedBytes = newSize;
          }
        }, node_ops: { getattr(node) {
          var attr = {};
          attr.dev = FS.isChrdev(node.mode) ? node.id : 1;
          attr.ino = node.id;
          attr.mode = node.mode;
          attr.nlink = 1;
          attr.uid = 0;
          attr.gid = 0;
          attr.rdev = node.rdev;
          if (FS.isDir(node.mode)) {
            attr.size = 4096;
          } else if (FS.isFile(node.mode)) {
            attr.size = node.usedBytes;
          } else if (FS.isLink(node.mode)) {
            attr.size = node.link.length;
          } else {
            attr.size = 0;
          }
          attr.atime = new Date(node.atime);
          attr.mtime = new Date(node.mtime);
          attr.ctime = new Date(node.ctime);
          attr.blksize = 4096;
          attr.blocks = Math.ceil(attr.size / attr.blksize);
          return attr;
        }, setattr(node, attr) {
          for (const key of ["mode", "atime", "mtime", "ctime"]) {
            if (attr[key] != null) {
              node[key] = attr[key];
            }
          }
          if (attr.size !== void 0) {
            MEMFS.resizeFileStorage(node, attr.size);
          }
        }, lookup(parent, name) {
          throw MEMFS.doesNotExistError;
        }, mknod(parent, name, mode, dev) {
          return MEMFS.createNode(parent, name, mode, dev);
        }, rename(old_node, new_dir, new_name) {
          var new_node;
          try {
            new_node = FS.lookupNode(new_dir, new_name);
          } catch (e2) {
          }
          if (new_node) {
            if (FS.isDir(old_node.mode)) {
              for (var i3 in new_node.contents) {
                throw new FS.ErrnoError(55);
              }
            }
            FS.hashRemoveNode(new_node);
          }
          delete old_node.parent.contents[old_node.name];
          new_dir.contents[new_name] = old_node;
          old_node.name = new_name;
          new_dir.ctime = new_dir.mtime = old_node.parent.ctime = old_node.parent.mtime = Date.now();
        }, unlink(parent, name) {
          delete parent.contents[name];
          parent.ctime = parent.mtime = Date.now();
        }, rmdir(parent, name) {
          var node = FS.lookupNode(parent, name);
          for (var i3 in node.contents) {
            throw new FS.ErrnoError(55);
          }
          delete parent.contents[name];
          parent.ctime = parent.mtime = Date.now();
        }, readdir(node) {
          return [".", "..", ...Object.keys(node.contents)];
        }, symlink(parent, newname, oldpath) {
          var node = MEMFS.createNode(parent, newname, 511 | 40960, 0);
          node.link = oldpath;
          return node;
        }, readlink(node) {
          if (!FS.isLink(node.mode)) {
            throw new FS.ErrnoError(28);
          }
          return node.link;
        } }, stream_ops: { read(stream, buffer, offset, length, position) {
          var contents = stream.node.contents;
          if (position >= stream.node.usedBytes) return 0;
          var size = Math.min(stream.node.usedBytes - position, length);
          if (size > 8 && contents.subarray) {
            buffer.set(contents.subarray(position, position + size), offset);
          } else {
            for (var i3 = 0; i3 < size; i3++) buffer[offset + i3] = contents[position + i3];
          }
          return size;
        }, write(stream, buffer, offset, length, position, canOwn) {
          if (!length) return 0;
          var node = stream.node;
          node.mtime = node.ctime = Date.now();
          if (buffer.subarray && (!node.contents || node.contents.subarray)) {
            if (canOwn) {
              node.contents = buffer.subarray(offset, offset + length);
              node.usedBytes = length;
              return length;
            } else if (node.usedBytes === 0 && position === 0) {
              node.contents = buffer.slice(offset, offset + length);
              node.usedBytes = length;
              return length;
            } else if (position + length <= node.usedBytes) {
              node.contents.set(buffer.subarray(offset, offset + length), position);
              return length;
            }
          }
          MEMFS.expandFileStorage(node, position + length);
          if (node.contents.subarray && buffer.subarray) {
            node.contents.set(buffer.subarray(offset, offset + length), position);
          } else {
            for (var i3 = 0; i3 < length; i3++) {
              node.contents[position + i3] = buffer[offset + i3];
            }
          }
          node.usedBytes = Math.max(node.usedBytes, position + length);
          return length;
        }, llseek(stream, offset, whence) {
          var position = offset;
          if (whence === 1) {
            position += stream.position;
          } else if (whence === 2) {
            if (FS.isFile(stream.node.mode)) {
              position += stream.node.usedBytes;
            }
          }
          if (position < 0) {
            throw new FS.ErrnoError(28);
          }
          return position;
        }, mmap(stream, length, position, prot, flags) {
          if (!FS.isFile(stream.node.mode)) {
            throw new FS.ErrnoError(43);
          }
          var ptr;
          var allocated;
          var contents = stream.node.contents;
          if (!(flags & 2) && contents && contents.buffer === HEAP8.buffer) {
            allocated = false;
            ptr = contents.byteOffset;
          } else {
            allocated = true;
            ptr = mmapAlloc(length);
            if (!ptr) {
              throw new FS.ErrnoError(48);
            }
            if (contents) {
              if (position > 0 || position + length < contents.length) {
                if (contents.subarray) {
                  contents = contents.subarray(position, position + length);
                } else {
                  contents = Array.prototype.slice.call(contents, position, position + length);
                }
              }
              HEAP8.set(contents, ptr);
            }
          }
          return { ptr, allocated };
        }, msync(stream, buffer, offset, length, mmapFlags) {
          MEMFS.stream_ops.write(stream, buffer, 0, length, offset, false);
          return 0;
        } } };
        var asyncLoad = async (url) => {
          var arrayBuffer = await readAsync(url);
          return new Uint8Array(arrayBuffer);
        };
        var FS_createDataFile = (...args) => FS.createDataFile(...args);
        var getUniqueRunDependency = (id) => id;
        var preloadPlugins = [];
        var FS_handledByPreloadPlugin = (byteArray, fullname, finish, onerror) => {
          if (typeof Browser != "undefined") Browser.init();
          var handled = false;
          preloadPlugins.forEach((plugin) => {
            if (handled) return;
            if (plugin["canHandle"](fullname)) {
              plugin["handle"](byteArray, fullname, finish, onerror);
              handled = true;
            }
          });
          return handled;
        };
        var FS_createPreloadedFile = (parent, name, url, canRead, canWrite, onload, onerror, dontCreateFile, canOwn, preFinish) => {
          var fullname = name ? PATH_FS.resolve(PATH.join2(parent, name)) : parent;
          var dep = getUniqueRunDependency(`cp ${fullname}`);
          function processData(byteArray) {
            function finish(byteArray2) {
              preFinish?.();
              if (!dontCreateFile) {
                FS_createDataFile(parent, name, byteArray2, canRead, canWrite, canOwn);
              }
              onload?.();
              removeRunDependency(dep);
            }
            if (FS_handledByPreloadPlugin(byteArray, fullname, finish, () => {
              onerror?.();
              removeRunDependency(dep);
            })) {
              return;
            }
            finish(byteArray);
          }
          addRunDependency(dep);
          if (typeof url == "string") {
            asyncLoad(url).then(processData, onerror);
          } else {
            processData(url);
          }
        };
        var FS_modeStringToFlags = (str) => {
          var flagModes = { r: 0, "r+": 2, w: 512 | 64 | 1, "w+": 512 | 64 | 2, a: 1024 | 64 | 1, "a+": 1024 | 64 | 2 };
          var flags = flagModes[str];
          if (typeof flags == "undefined") {
            throw new Error(`Unknown file open mode: ${str}`);
          }
          return flags;
        };
        var FS_getMode = (canRead, canWrite) => {
          var mode = 0;
          if (canRead) mode |= 292 | 73;
          if (canWrite) mode |= 146;
          return mode;
        };
        var FS = { root: null, mounts: [], devices: {}, streams: [], nextInode: 1, nameTable: null, currentPath: "/", initialized: false, ignorePermissions: true, filesystems: null, syncFSRequests: 0, readFiles: {}, ErrnoError: class {
          name = "ErrnoError";
          constructor(errno) {
            this.errno = errno;
          }
        }, FSStream: class {
          shared = {};
          get object() {
            return this.node;
          }
          set object(val) {
            this.node = val;
          }
          get isRead() {
            return (this.flags & 2097155) !== 1;
          }
          get isWrite() {
            return (this.flags & 2097155) !== 0;
          }
          get isAppend() {
            return this.flags & 1024;
          }
          get flags() {
            return this.shared.flags;
          }
          set flags(val) {
            this.shared.flags = val;
          }
          get position() {
            return this.shared.position;
          }
          set position(val) {
            this.shared.position = val;
          }
        }, FSNode: class {
          node_ops = {};
          stream_ops = {};
          readMode = 292 | 73;
          writeMode = 146;
          mounted = null;
          constructor(parent, name, mode, rdev) {
            if (!parent) {
              parent = this;
            }
            this.parent = parent;
            this.mount = parent.mount;
            this.id = FS.nextInode++;
            this.name = name;
            this.mode = mode;
            this.rdev = rdev;
            this.atime = this.mtime = this.ctime = Date.now();
          }
          get read() {
            return (this.mode & this.readMode) === this.readMode;
          }
          set read(val) {
            val ? this.mode |= this.readMode : this.mode &= ~this.readMode;
          }
          get write() {
            return (this.mode & this.writeMode) === this.writeMode;
          }
          set write(val) {
            val ? this.mode |= this.writeMode : this.mode &= ~this.writeMode;
          }
          get isFolder() {
            return FS.isDir(this.mode);
          }
          get isDevice() {
            return FS.isChrdev(this.mode);
          }
        }, lookupPath(path, opts = {}) {
          if (!path) {
            throw new FS.ErrnoError(44);
          }
          opts.follow_mount ??= true;
          if (!PATH.isAbs(path)) {
            path = FS.cwd() + "/" + path;
          }
          linkloop: for (var nlinks = 0; nlinks < 40; nlinks++) {
            var parts = path.split("/").filter((p2) => !!p2);
            var current = FS.root;
            var current_path = "/";
            for (var i3 = 0; i3 < parts.length; i3++) {
              var islast = i3 === parts.length - 1;
              if (islast && opts.parent) {
                break;
              }
              if (parts[i3] === ".") {
                continue;
              }
              if (parts[i3] === "..") {
                current_path = PATH.dirname(current_path);
                if (FS.isRoot(current)) {
                  path = current_path + "/" + parts.slice(i3 + 1).join("/");
                  continue linkloop;
                } else {
                  current = current.parent;
                }
                continue;
              }
              current_path = PATH.join2(current_path, parts[i3]);
              try {
                current = FS.lookupNode(current, parts[i3]);
              } catch (e2) {
                if (e2?.errno === 44 && islast && opts.noent_okay) {
                  return { path: current_path };
                }
                throw e2;
              }
              if (FS.isMountpoint(current) && (!islast || opts.follow_mount)) {
                current = current.mounted.root;
              }
              if (FS.isLink(current.mode) && (!islast || opts.follow)) {
                if (!current.node_ops.readlink) {
                  throw new FS.ErrnoError(52);
                }
                var link = current.node_ops.readlink(current);
                if (!PATH.isAbs(link)) {
                  link = PATH.dirname(current_path) + "/" + link;
                }
                path = link + "/" + parts.slice(i3 + 1).join("/");
                continue linkloop;
              }
            }
            return { path: current_path, node: current };
          }
          throw new FS.ErrnoError(32);
        }, getPath(node) {
          var path;
          while (true) {
            if (FS.isRoot(node)) {
              var mount = node.mount.mountpoint;
              if (!path) return mount;
              return mount[mount.length - 1] !== "/" ? `${mount}/${path}` : mount + path;
            }
            path = path ? `${node.name}/${path}` : node.name;
            node = node.parent;
          }
        }, hashName(parentid, name) {
          var hash = 0;
          for (var i3 = 0; i3 < name.length; i3++) {
            hash = (hash << 5) - hash + name.charCodeAt(i3) | 0;
          }
          return (parentid + hash >>> 0) % FS.nameTable.length;
        }, hashAddNode(node) {
          var hash = FS.hashName(node.parent.id, node.name);
          node.name_next = FS.nameTable[hash];
          FS.nameTable[hash] = node;
        }, hashRemoveNode(node) {
          var hash = FS.hashName(node.parent.id, node.name);
          if (FS.nameTable[hash] === node) {
            FS.nameTable[hash] = node.name_next;
          } else {
            var current = FS.nameTable[hash];
            while (current) {
              if (current.name_next === node) {
                current.name_next = node.name_next;
                break;
              }
              current = current.name_next;
            }
          }
        }, lookupNode(parent, name) {
          var errCode = FS.mayLookup(parent);
          if (errCode) {
            throw new FS.ErrnoError(errCode);
          }
          var hash = FS.hashName(parent.id, name);
          for (var node = FS.nameTable[hash]; node; node = node.name_next) {
            var nodeName = node.name;
            if (node.parent.id === parent.id && nodeName === name) {
              return node;
            }
          }
          return FS.lookup(parent, name);
        }, createNode(parent, name, mode, rdev) {
          var node = new FS.FSNode(parent, name, mode, rdev);
          FS.hashAddNode(node);
          return node;
        }, destroyNode(node) {
          FS.hashRemoveNode(node);
        }, isRoot(node) {
          return node === node.parent;
        }, isMountpoint(node) {
          return !!node.mounted;
        }, isFile(mode) {
          return (mode & 61440) === 32768;
        }, isDir(mode) {
          return (mode & 61440) === 16384;
        }, isLink(mode) {
          return (mode & 61440) === 40960;
        }, isChrdev(mode) {
          return (mode & 61440) === 8192;
        }, isBlkdev(mode) {
          return (mode & 61440) === 24576;
        }, isFIFO(mode) {
          return (mode & 61440) === 4096;
        }, isSocket(mode) {
          return (mode & 49152) === 49152;
        }, flagsToPermissionString(flag) {
          var perms = ["r", "w", "rw"][flag & 3];
          if (flag & 512) {
            perms += "w";
          }
          return perms;
        }, nodePermissions(node, perms) {
          if (FS.ignorePermissions) {
            return 0;
          }
          if (perms.includes("r") && !(node.mode & 292)) {
            return 2;
          } else if (perms.includes("w") && !(node.mode & 146)) {
            return 2;
          } else if (perms.includes("x") && !(node.mode & 73)) {
            return 2;
          }
          return 0;
        }, mayLookup(dir) {
          if (!FS.isDir(dir.mode)) return 54;
          var errCode = FS.nodePermissions(dir, "x");
          if (errCode) return errCode;
          if (!dir.node_ops.lookup) return 2;
          return 0;
        }, mayCreate(dir, name) {
          if (!FS.isDir(dir.mode)) {
            return 54;
          }
          try {
            var node = FS.lookupNode(dir, name);
            return 20;
          } catch (e2) {
          }
          return FS.nodePermissions(dir, "wx");
        }, mayDelete(dir, name, isdir) {
          var node;
          try {
            node = FS.lookupNode(dir, name);
          } catch (e2) {
            return e2.errno;
          }
          var errCode = FS.nodePermissions(dir, "wx");
          if (errCode) {
            return errCode;
          }
          if (isdir) {
            if (!FS.isDir(node.mode)) {
              return 54;
            }
            if (FS.isRoot(node) || FS.getPath(node) === FS.cwd()) {
              return 10;
            }
          } else {
            if (FS.isDir(node.mode)) {
              return 31;
            }
          }
          return 0;
        }, mayOpen(node, flags) {
          if (!node) {
            return 44;
          }
          if (FS.isLink(node.mode)) {
            return 32;
          } else if (FS.isDir(node.mode)) {
            if (FS.flagsToPermissionString(flags) !== "r" || flags & (512 | 64)) {
              return 31;
            }
          }
          return FS.nodePermissions(node, FS.flagsToPermissionString(flags));
        }, checkOpExists(op, err2) {
          if (!op) {
            throw new FS.ErrnoError(err2);
          }
          return op;
        }, MAX_OPEN_FDS: 4096, nextfd() {
          for (var fd = 0; fd <= FS.MAX_OPEN_FDS; fd++) {
            if (!FS.streams[fd]) {
              return fd;
            }
          }
          throw new FS.ErrnoError(33);
        }, getStreamChecked(fd) {
          var stream = FS.getStream(fd);
          if (!stream) {
            throw new FS.ErrnoError(8);
          }
          return stream;
        }, getStream: (fd) => FS.streams[fd], createStream(stream, fd = -1) {
          stream = Object.assign(new FS.FSStream(), stream);
          if (fd == -1) {
            fd = FS.nextfd();
          }
          stream.fd = fd;
          FS.streams[fd] = stream;
          return stream;
        }, closeStream(fd) {
          FS.streams[fd] = null;
        }, dupStream(origStream, fd = -1) {
          var stream = FS.createStream(origStream, fd);
          stream.stream_ops?.dup?.(stream);
          return stream;
        }, doSetAttr(stream, node, attr) {
          var setattr = stream?.stream_ops.setattr;
          var arg = setattr ? stream : node;
          setattr ??= node.node_ops.setattr;
          FS.checkOpExists(setattr, 63);
          setattr(arg, attr);
        }, chrdev_stream_ops: { open(stream) {
          var device = FS.getDevice(stream.node.rdev);
          stream.stream_ops = device.stream_ops;
          stream.stream_ops.open?.(stream);
        }, llseek() {
          throw new FS.ErrnoError(70);
        } }, major: (dev) => dev >> 8, minor: (dev) => dev & 255, makedev: (ma, mi) => ma << 8 | mi, registerDevice(dev, ops) {
          FS.devices[dev] = { stream_ops: ops };
        }, getDevice: (dev) => FS.devices[dev], getMounts(mount) {
          var mounts = [];
          var check = [mount];
          while (check.length) {
            var m2 = check.pop();
            mounts.push(m2);
            check.push(...m2.mounts);
          }
          return mounts;
        }, syncfs(populate, callback) {
          if (typeof populate == "function") {
            callback = populate;
            populate = false;
          }
          FS.syncFSRequests++;
          if (FS.syncFSRequests > 1) {
            err(`warning: ${FS.syncFSRequests} FS.syncfs operations in flight at once, probably just doing extra work`);
          }
          var mounts = FS.getMounts(FS.root.mount);
          var completed = 0;
          function doCallback(errCode) {
            FS.syncFSRequests--;
            return callback(errCode);
          }
          function done(errCode) {
            if (errCode) {
              if (!done.errored) {
                done.errored = true;
                return doCallback(errCode);
              }
              return;
            }
            if (++completed >= mounts.length) {
              doCallback(null);
            }
          }
          mounts.forEach((mount) => {
            if (!mount.type.syncfs) {
              return done(null);
            }
            mount.type.syncfs(mount, populate, done);
          });
        }, mount(type, opts, mountpoint) {
          var root = mountpoint === "/";
          var pseudo = !mountpoint;
          var node;
          if (root && FS.root) {
            throw new FS.ErrnoError(10);
          } else if (!root && !pseudo) {
            var lookup = FS.lookupPath(mountpoint, { follow_mount: false });
            mountpoint = lookup.path;
            node = lookup.node;
            if (FS.isMountpoint(node)) {
              throw new FS.ErrnoError(10);
            }
            if (!FS.isDir(node.mode)) {
              throw new FS.ErrnoError(54);
            }
          }
          var mount = { type, opts, mountpoint, mounts: [] };
          var mountRoot = type.mount(mount);
          mountRoot.mount = mount;
          mount.root = mountRoot;
          if (root) {
            FS.root = mountRoot;
          } else if (node) {
            node.mounted = mount;
            if (node.mount) {
              node.mount.mounts.push(mount);
            }
          }
          return mountRoot;
        }, unmount(mountpoint) {
          var lookup = FS.lookupPath(mountpoint, { follow_mount: false });
          if (!FS.isMountpoint(lookup.node)) {
            throw new FS.ErrnoError(28);
          }
          var node = lookup.node;
          var mount = node.mounted;
          var mounts = FS.getMounts(mount);
          Object.keys(FS.nameTable).forEach((hash) => {
            var current = FS.nameTable[hash];
            while (current) {
              var next = current.name_next;
              if (mounts.includes(current.mount)) {
                FS.destroyNode(current);
              }
              current = next;
            }
          });
          node.mounted = null;
          var idx = node.mount.mounts.indexOf(mount);
          node.mount.mounts.splice(idx, 1);
        }, lookup(parent, name) {
          return parent.node_ops.lookup(parent, name);
        }, mknod(path, mode, dev) {
          var lookup = FS.lookupPath(path, { parent: true });
          var parent = lookup.node;
          var name = PATH.basename(path);
          if (!name) {
            throw new FS.ErrnoError(28);
          }
          if (name === "." || name === "..") {
            throw new FS.ErrnoError(20);
          }
          var errCode = FS.mayCreate(parent, name);
          if (errCode) {
            throw new FS.ErrnoError(errCode);
          }
          if (!parent.node_ops.mknod) {
            throw new FS.ErrnoError(63);
          }
          return parent.node_ops.mknod(parent, name, mode, dev);
        }, statfs(path) {
          return FS.statfsNode(FS.lookupPath(path, { follow: true }).node);
        }, statfsStream(stream) {
          return FS.statfsNode(stream.node);
        }, statfsNode(node) {
          var rtn = { bsize: 4096, frsize: 4096, blocks: 1e6, bfree: 5e5, bavail: 5e5, files: FS.nextInode, ffree: FS.nextInode - 1, fsid: 42, flags: 2, namelen: 255 };
          if (node.node_ops.statfs) {
            Object.assign(rtn, node.node_ops.statfs(node.mount.opts.root));
          }
          return rtn;
        }, create(path, mode = 438) {
          mode &= 4095;
          mode |= 32768;
          return FS.mknod(path, mode, 0);
        }, mkdir(path, mode = 511) {
          mode &= 511 | 512;
          mode |= 16384;
          return FS.mknod(path, mode, 0);
        }, mkdirTree(path, mode) {
          var dirs = path.split("/");
          var d2 = "";
          for (var dir of dirs) {
            if (!dir) continue;
            if (d2 || PATH.isAbs(path)) d2 += "/";
            d2 += dir;
            try {
              FS.mkdir(d2, mode);
            } catch (e2) {
              if (e2.errno != 20) throw e2;
            }
          }
        }, mkdev(path, mode, dev) {
          if (typeof dev == "undefined") {
            dev = mode;
            mode = 438;
          }
          mode |= 8192;
          return FS.mknod(path, mode, dev);
        }, symlink(oldpath, newpath) {
          if (!PATH_FS.resolve(oldpath)) {
            throw new FS.ErrnoError(44);
          }
          var lookup = FS.lookupPath(newpath, { parent: true });
          var parent = lookup.node;
          if (!parent) {
            throw new FS.ErrnoError(44);
          }
          var newname = PATH.basename(newpath);
          var errCode = FS.mayCreate(parent, newname);
          if (errCode) {
            throw new FS.ErrnoError(errCode);
          }
          if (!parent.node_ops.symlink) {
            throw new FS.ErrnoError(63);
          }
          return parent.node_ops.symlink(parent, newname, oldpath);
        }, rename(old_path, new_path) {
          var old_dirname = PATH.dirname(old_path);
          var new_dirname = PATH.dirname(new_path);
          var old_name = PATH.basename(old_path);
          var new_name = PATH.basename(new_path);
          var lookup, old_dir, new_dir;
          lookup = FS.lookupPath(old_path, { parent: true });
          old_dir = lookup.node;
          lookup = FS.lookupPath(new_path, { parent: true });
          new_dir = lookup.node;
          if (!old_dir || !new_dir) throw new FS.ErrnoError(44);
          if (old_dir.mount !== new_dir.mount) {
            throw new FS.ErrnoError(75);
          }
          var old_node = FS.lookupNode(old_dir, old_name);
          var relative = PATH_FS.relative(old_path, new_dirname);
          if (relative.charAt(0) !== ".") {
            throw new FS.ErrnoError(28);
          }
          relative = PATH_FS.relative(new_path, old_dirname);
          if (relative.charAt(0) !== ".") {
            throw new FS.ErrnoError(55);
          }
          var new_node;
          try {
            new_node = FS.lookupNode(new_dir, new_name);
          } catch (e2) {
          }
          if (old_node === new_node) {
            return;
          }
          var isdir = FS.isDir(old_node.mode);
          var errCode = FS.mayDelete(old_dir, old_name, isdir);
          if (errCode) {
            throw new FS.ErrnoError(errCode);
          }
          errCode = new_node ? FS.mayDelete(new_dir, new_name, isdir) : FS.mayCreate(new_dir, new_name);
          if (errCode) {
            throw new FS.ErrnoError(errCode);
          }
          if (!old_dir.node_ops.rename) {
            throw new FS.ErrnoError(63);
          }
          if (FS.isMountpoint(old_node) || new_node && FS.isMountpoint(new_node)) {
            throw new FS.ErrnoError(10);
          }
          if (new_dir !== old_dir) {
            errCode = FS.nodePermissions(old_dir, "w");
            if (errCode) {
              throw new FS.ErrnoError(errCode);
            }
          }
          FS.hashRemoveNode(old_node);
          try {
            old_dir.node_ops.rename(old_node, new_dir, new_name);
            old_node.parent = new_dir;
          } catch (e2) {
            throw e2;
          } finally {
            FS.hashAddNode(old_node);
          }
        }, rmdir(path) {
          var lookup = FS.lookupPath(path, { parent: true });
          var parent = lookup.node;
          var name = PATH.basename(path);
          var node = FS.lookupNode(parent, name);
          var errCode = FS.mayDelete(parent, name, true);
          if (errCode) {
            throw new FS.ErrnoError(errCode);
          }
          if (!parent.node_ops.rmdir) {
            throw new FS.ErrnoError(63);
          }
          if (FS.isMountpoint(node)) {
            throw new FS.ErrnoError(10);
          }
          parent.node_ops.rmdir(parent, name);
          FS.destroyNode(node);
        }, readdir(path) {
          var lookup = FS.lookupPath(path, { follow: true });
          var node = lookup.node;
          var readdir = FS.checkOpExists(node.node_ops.readdir, 54);
          return readdir(node);
        }, unlink(path) {
          var lookup = FS.lookupPath(path, { parent: true });
          var parent = lookup.node;
          if (!parent) {
            throw new FS.ErrnoError(44);
          }
          var name = PATH.basename(path);
          var node = FS.lookupNode(parent, name);
          var errCode = FS.mayDelete(parent, name, false);
          if (errCode) {
            throw new FS.ErrnoError(errCode);
          }
          if (!parent.node_ops.unlink) {
            throw new FS.ErrnoError(63);
          }
          if (FS.isMountpoint(node)) {
            throw new FS.ErrnoError(10);
          }
          parent.node_ops.unlink(parent, name);
          FS.destroyNode(node);
        }, readlink(path) {
          var lookup = FS.lookupPath(path);
          var link = lookup.node;
          if (!link) {
            throw new FS.ErrnoError(44);
          }
          if (!link.node_ops.readlink) {
            throw new FS.ErrnoError(28);
          }
          return link.node_ops.readlink(link);
        }, stat(path, dontFollow) {
          var lookup = FS.lookupPath(path, { follow: !dontFollow });
          var node = lookup.node;
          var getattr = FS.checkOpExists(node.node_ops.getattr, 63);
          return getattr(node);
        }, fstat(fd) {
          var stream = FS.getStreamChecked(fd);
          var node = stream.node;
          var getattr = stream.stream_ops.getattr;
          var arg = getattr ? stream : node;
          getattr ??= node.node_ops.getattr;
          FS.checkOpExists(getattr, 63);
          return getattr(arg);
        }, lstat(path) {
          return FS.stat(path, true);
        }, doChmod(stream, node, mode, dontFollow) {
          FS.doSetAttr(stream, node, { mode: mode & 4095 | node.mode & ~4095, ctime: Date.now(), dontFollow });
        }, chmod(path, mode, dontFollow) {
          var node;
          if (typeof path == "string") {
            var lookup = FS.lookupPath(path, { follow: !dontFollow });
            node = lookup.node;
          } else {
            node = path;
          }
          FS.doChmod(null, node, mode, dontFollow);
        }, lchmod(path, mode) {
          FS.chmod(path, mode, true);
        }, fchmod(fd, mode) {
          var stream = FS.getStreamChecked(fd);
          FS.doChmod(stream, stream.node, mode, false);
        }, doChown(stream, node, dontFollow) {
          FS.doSetAttr(stream, node, { timestamp: Date.now(), dontFollow });
        }, chown(path, uid, gid, dontFollow) {
          var node;
          if (typeof path == "string") {
            var lookup = FS.lookupPath(path, { follow: !dontFollow });
            node = lookup.node;
          } else {
            node = path;
          }
          FS.doChown(null, node, dontFollow);
        }, lchown(path, uid, gid) {
          FS.chown(path, uid, gid, true);
        }, fchown(fd, uid, gid) {
          var stream = FS.getStreamChecked(fd);
          FS.doChown(stream, stream.node, false);
        }, doTruncate(stream, node, len) {
          if (FS.isDir(node.mode)) {
            throw new FS.ErrnoError(31);
          }
          if (!FS.isFile(node.mode)) {
            throw new FS.ErrnoError(28);
          }
          var errCode = FS.nodePermissions(node, "w");
          if (errCode) {
            throw new FS.ErrnoError(errCode);
          }
          FS.doSetAttr(stream, node, { size: len, timestamp: Date.now() });
        }, truncate(path, len) {
          if (len < 0) {
            throw new FS.ErrnoError(28);
          }
          var node;
          if (typeof path == "string") {
            var lookup = FS.lookupPath(path, { follow: true });
            node = lookup.node;
          } else {
            node = path;
          }
          FS.doTruncate(null, node, len);
        }, ftruncate(fd, len) {
          var stream = FS.getStreamChecked(fd);
          if (len < 0 || (stream.flags & 2097155) === 0) {
            throw new FS.ErrnoError(28);
          }
          FS.doTruncate(stream, stream.node, len);
        }, utime(path, atime, mtime) {
          var lookup = FS.lookupPath(path, { follow: true });
          var node = lookup.node;
          var setattr = FS.checkOpExists(node.node_ops.setattr, 63);
          setattr(node, { atime, mtime });
        }, open(path, flags, mode = 438) {
          if (path === "") {
            throw new FS.ErrnoError(44);
          }
          flags = typeof flags == "string" ? FS_modeStringToFlags(flags) : flags;
          if (flags & 64) {
            mode = mode & 4095 | 32768;
          } else {
            mode = 0;
          }
          var node;
          var isDirPath;
          if (typeof path == "object") {
            node = path;
          } else {
            isDirPath = path.endsWith("/");
            var lookup = FS.lookupPath(path, { follow: !(flags & 131072), noent_okay: true });
            node = lookup.node;
            path = lookup.path;
          }
          var created = false;
          if (flags & 64) {
            if (node) {
              if (flags & 128) {
                throw new FS.ErrnoError(20);
              }
            } else if (isDirPath) {
              throw new FS.ErrnoError(31);
            } else {
              node = FS.mknod(path, mode | 511, 0);
              created = true;
            }
          }
          if (!node) {
            throw new FS.ErrnoError(44);
          }
          if (FS.isChrdev(node.mode)) {
            flags &= ~512;
          }
          if (flags & 65536 && !FS.isDir(node.mode)) {
            throw new FS.ErrnoError(54);
          }
          if (!created) {
            var errCode = FS.mayOpen(node, flags);
            if (errCode) {
              throw new FS.ErrnoError(errCode);
            }
          }
          if (flags & 512 && !created) {
            FS.truncate(node, 0);
          }
          flags &= ~(128 | 512 | 131072);
          var stream = FS.createStream({ node, path: FS.getPath(node), flags, seekable: true, position: 0, stream_ops: node.stream_ops, ungotten: [], error: false });
          if (stream.stream_ops.open) {
            stream.stream_ops.open(stream);
          }
          if (created) {
            FS.chmod(node, mode & 511);
          }
          if (Module["logReadFiles"] && !(flags & 1)) {
            if (!(path in FS.readFiles)) {
              FS.readFiles[path] = 1;
            }
          }
          return stream;
        }, close(stream) {
          if (FS.isClosed(stream)) {
            throw new FS.ErrnoError(8);
          }
          if (stream.getdents) stream.getdents = null;
          try {
            if (stream.stream_ops.close) {
              stream.stream_ops.close(stream);
            }
          } catch (e2) {
            throw e2;
          } finally {
            FS.closeStream(stream.fd);
          }
          stream.fd = null;
        }, isClosed(stream) {
          return stream.fd === null;
        }, llseek(stream, offset, whence) {
          if (FS.isClosed(stream)) {
            throw new FS.ErrnoError(8);
          }
          if (!stream.seekable || !stream.stream_ops.llseek) {
            throw new FS.ErrnoError(70);
          }
          if (whence != 0 && whence != 1 && whence != 2) {
            throw new FS.ErrnoError(28);
          }
          stream.position = stream.stream_ops.llseek(stream, offset, whence);
          stream.ungotten = [];
          return stream.position;
        }, read(stream, buffer, offset, length, position) {
          if (length < 0 || position < 0) {
            throw new FS.ErrnoError(28);
          }
          if (FS.isClosed(stream)) {
            throw new FS.ErrnoError(8);
          }
          if ((stream.flags & 2097155) === 1) {
            throw new FS.ErrnoError(8);
          }
          if (FS.isDir(stream.node.mode)) {
            throw new FS.ErrnoError(31);
          }
          if (!stream.stream_ops.read) {
            throw new FS.ErrnoError(28);
          }
          var seeking = typeof position != "undefined";
          if (!seeking) {
            position = stream.position;
          } else if (!stream.seekable) {
            throw new FS.ErrnoError(70);
          }
          var bytesRead = stream.stream_ops.read(stream, buffer, offset, length, position);
          if (!seeking) stream.position += bytesRead;
          return bytesRead;
        }, write(stream, buffer, offset, length, position, canOwn) {
          if (length < 0 || position < 0) {
            throw new FS.ErrnoError(28);
          }
          if (FS.isClosed(stream)) {
            throw new FS.ErrnoError(8);
          }
          if ((stream.flags & 2097155) === 0) {
            throw new FS.ErrnoError(8);
          }
          if (FS.isDir(stream.node.mode)) {
            throw new FS.ErrnoError(31);
          }
          if (!stream.stream_ops.write) {
            throw new FS.ErrnoError(28);
          }
          if (stream.seekable && stream.flags & 1024) {
            FS.llseek(stream, 0, 2);
          }
          var seeking = typeof position != "undefined";
          if (!seeking) {
            position = stream.position;
          } else if (!stream.seekable) {
            throw new FS.ErrnoError(70);
          }
          var bytesWritten = stream.stream_ops.write(stream, buffer, offset, length, position, canOwn);
          if (!seeking) stream.position += bytesWritten;
          return bytesWritten;
        }, mmap(stream, length, position, prot, flags) {
          if ((prot & 2) !== 0 && (flags & 2) === 0 && (stream.flags & 2097155) !== 2) {
            throw new FS.ErrnoError(2);
          }
          if ((stream.flags & 2097155) === 1) {
            throw new FS.ErrnoError(2);
          }
          if (!stream.stream_ops.mmap) {
            throw new FS.ErrnoError(43);
          }
          if (!length) {
            throw new FS.ErrnoError(28);
          }
          return stream.stream_ops.mmap(stream, length, position, prot, flags);
        }, msync(stream, buffer, offset, length, mmapFlags) {
          if (!stream.stream_ops.msync) {
            return 0;
          }
          return stream.stream_ops.msync(stream, buffer, offset, length, mmapFlags);
        }, ioctl(stream, cmd, arg) {
          if (!stream.stream_ops.ioctl) {
            throw new FS.ErrnoError(59);
          }
          return stream.stream_ops.ioctl(stream, cmd, arg);
        }, readFile(path, opts = {}) {
          opts.flags = opts.flags || 0;
          opts.encoding = opts.encoding || "binary";
          if (opts.encoding !== "utf8" && opts.encoding !== "binary") {
            throw new Error(`Invalid encoding type "${opts.encoding}"`);
          }
          var stream = FS.open(path, opts.flags);
          var stat = FS.stat(path);
          var length = stat.size;
          var buf = new Uint8Array(length);
          FS.read(stream, buf, 0, length, 0);
          if (opts.encoding === "utf8") {
            buf = UTF8ArrayToString(buf);
          }
          FS.close(stream);
          return buf;
        }, writeFile(path, data, opts = {}) {
          opts.flags = opts.flags || 577;
          var stream = FS.open(path, opts.flags, opts.mode);
          if (typeof data == "string") {
            data = new Uint8Array(intArrayFromString(data, true));
          }
          if (ArrayBuffer.isView(data)) {
            FS.write(stream, data, 0, data.byteLength, void 0, opts.canOwn);
          } else {
            throw new Error("Unsupported data type");
          }
          FS.close(stream);
        }, cwd: () => FS.currentPath, chdir(path) {
          var lookup = FS.lookupPath(path, { follow: true });
          if (lookup.node === null) {
            throw new FS.ErrnoError(44);
          }
          if (!FS.isDir(lookup.node.mode)) {
            throw new FS.ErrnoError(54);
          }
          var errCode = FS.nodePermissions(lookup.node, "x");
          if (errCode) {
            throw new FS.ErrnoError(errCode);
          }
          FS.currentPath = lookup.path;
        }, createDefaultDirectories() {
          FS.mkdir("/tmp");
          FS.mkdir("/home");
          FS.mkdir("/home/<USER>");
        }, createDefaultDevices() {
          FS.mkdir("/dev");
          FS.registerDevice(FS.makedev(1, 3), { read: () => 0, write: (stream, buffer, offset, length, pos) => length, llseek: () => 0 });
          FS.mkdev("/dev/null", FS.makedev(1, 3));
          TTY.register(FS.makedev(5, 0), TTY.default_tty_ops);
          TTY.register(FS.makedev(6, 0), TTY.default_tty1_ops);
          FS.mkdev("/dev/tty", FS.makedev(5, 0));
          FS.mkdev("/dev/tty1", FS.makedev(6, 0));
          var randomBuffer = new Uint8Array(1024), randomLeft = 0;
          var randomByte = () => {
            if (randomLeft === 0) {
              randomFill(randomBuffer);
              randomLeft = randomBuffer.byteLength;
            }
            return randomBuffer[--randomLeft];
          };
          FS.createDevice("/dev", "random", randomByte);
          FS.createDevice("/dev", "urandom", randomByte);
          FS.mkdir("/dev/shm");
          FS.mkdir("/dev/shm/tmp");
        }, createSpecialDirectories() {
          FS.mkdir("/proc");
          var proc_self = FS.mkdir("/proc/self");
          FS.mkdir("/proc/self/fd");
          FS.mount({ mount() {
            var node = FS.createNode(proc_self, "fd", 16895, 73);
            node.stream_ops = { llseek: MEMFS.stream_ops.llseek };
            node.node_ops = { lookup(parent, name) {
              var fd = +name;
              var stream = FS.getStreamChecked(fd);
              var ret = { parent: null, mount: { mountpoint: "fake" }, node_ops: { readlink: () => stream.path }, id: fd + 1 };
              ret.parent = ret;
              return ret;
            }, readdir() {
              return Array.from(FS.streams.entries()).filter(([k2, v2]) => v2).map(([k2, v2]) => k2.toString());
            } };
            return node;
          } }, {}, "/proc/self/fd");
        }, createStandardStreams(input, output, error) {
          if (input) {
            FS.createDevice("/dev", "stdin", input);
          } else {
            FS.symlink("/dev/tty", "/dev/stdin");
          }
          if (output) {
            FS.createDevice("/dev", "stdout", null, output);
          } else {
            FS.symlink("/dev/tty", "/dev/stdout");
          }
          if (error) {
            FS.createDevice("/dev", "stderr", null, error);
          } else {
            FS.symlink("/dev/tty1", "/dev/stderr");
          }
          var stdin = FS.open("/dev/stdin", 0);
          var stdout = FS.open("/dev/stdout", 1);
          var stderr = FS.open("/dev/stderr", 1);
        }, staticInit() {
          FS.nameTable = new Array(4096);
          FS.mount(MEMFS, {}, "/");
          FS.createDefaultDirectories();
          FS.createDefaultDevices();
          FS.createSpecialDirectories();
          FS.filesystems = { MEMFS };
        }, init(input, output, error) {
          FS.initialized = true;
          input ??= Module["stdin"];
          output ??= Module["stdout"];
          error ??= Module["stderr"];
          FS.createStandardStreams(input, output, error);
        }, quit() {
          FS.initialized = false;
          for (var stream of FS.streams) {
            if (stream) {
              FS.close(stream);
            }
          }
        }, findObject(path, dontResolveLastLink) {
          var ret = FS.analyzePath(path, dontResolveLastLink);
          if (!ret.exists) {
            return null;
          }
          return ret.object;
        }, analyzePath(path, dontResolveLastLink) {
          try {
            var lookup = FS.lookupPath(path, { follow: !dontResolveLastLink });
            path = lookup.path;
          } catch (e2) {
          }
          var ret = { isRoot: false, exists: false, error: 0, name: null, path: null, object: null, parentExists: false, parentPath: null, parentObject: null };
          try {
            var lookup = FS.lookupPath(path, { parent: true });
            ret.parentExists = true;
            ret.parentPath = lookup.path;
            ret.parentObject = lookup.node;
            ret.name = PATH.basename(path);
            lookup = FS.lookupPath(path, { follow: !dontResolveLastLink });
            ret.exists = true;
            ret.path = lookup.path;
            ret.object = lookup.node;
            ret.name = lookup.node.name;
            ret.isRoot = lookup.path === "/";
          } catch (e2) {
            ret.error = e2.errno;
          }
          return ret;
        }, createPath(parent, path, canRead, canWrite) {
          parent = typeof parent == "string" ? parent : FS.getPath(parent);
          var parts = path.split("/").reverse();
          while (parts.length) {
            var part = parts.pop();
            if (!part) continue;
            var current = PATH.join2(parent, part);
            try {
              FS.mkdir(current);
            } catch (e2) {
              if (e2.errno != 20) throw e2;
            }
            parent = current;
          }
          return current;
        }, createFile(parent, name, properties, canRead, canWrite) {
          var path = PATH.join2(typeof parent == "string" ? parent : FS.getPath(parent), name);
          var mode = FS_getMode(canRead, canWrite);
          return FS.create(path, mode);
        }, createDataFile(parent, name, data, canRead, canWrite, canOwn) {
          var path = name;
          if (parent) {
            parent = typeof parent == "string" ? parent : FS.getPath(parent);
            path = name ? PATH.join2(parent, name) : parent;
          }
          var mode = FS_getMode(canRead, canWrite);
          var node = FS.create(path, mode);
          if (data) {
            if (typeof data == "string") {
              var arr = new Array(data.length);
              for (var i3 = 0, len = data.length; i3 < len; ++i3) arr[i3] = data.charCodeAt(i3);
              data = arr;
            }
            FS.chmod(node, mode | 146);
            var stream = FS.open(node, 577);
            FS.write(stream, data, 0, data.length, 0, canOwn);
            FS.close(stream);
            FS.chmod(node, mode);
          }
        }, createDevice(parent, name, input, output) {
          var path = PATH.join2(typeof parent == "string" ? parent : FS.getPath(parent), name);
          var mode = FS_getMode(!!input, !!output);
          FS.createDevice.major ??= 64;
          var dev = FS.makedev(FS.createDevice.major++, 0);
          FS.registerDevice(dev, { open(stream) {
            stream.seekable = false;
          }, close(stream) {
            if (output?.buffer?.length) {
              output(10);
            }
          }, read(stream, buffer, offset, length, pos) {
            var bytesRead = 0;
            for (var i3 = 0; i3 < length; i3++) {
              var result;
              try {
                result = input();
              } catch (e2) {
                throw new FS.ErrnoError(29);
              }
              if (result === void 0 && bytesRead === 0) {
                throw new FS.ErrnoError(6);
              }
              if (result === null || result === void 0) break;
              bytesRead++;
              buffer[offset + i3] = result;
            }
            if (bytesRead) {
              stream.node.atime = Date.now();
            }
            return bytesRead;
          }, write(stream, buffer, offset, length, pos) {
            for (var i3 = 0; i3 < length; i3++) {
              try {
                output(buffer[offset + i3]);
              } catch (e2) {
                throw new FS.ErrnoError(29);
              }
            }
            if (length) {
              stream.node.mtime = stream.node.ctime = Date.now();
            }
            return i3;
          } });
          return FS.mkdev(path, mode, dev);
        }, forceLoadFile(obj) {
          if (obj.isDevice || obj.isFolder || obj.link || obj.contents) return true;
          if (typeof XMLHttpRequest != "undefined") {
            throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");
          } else {
            try {
              obj.contents = readBinary(obj.url);
              obj.usedBytes = obj.contents.length;
            } catch (e2) {
              throw new FS.ErrnoError(29);
            }
          }
        }, createLazyFile(parent, name, url, canRead, canWrite) {
          class LazyUint8Array {
            lengthKnown = false;
            chunks = [];
            get(idx) {
              if (idx > this.length - 1 || idx < 0) {
                return void 0;
              }
              var chunkOffset = idx % this.chunkSize;
              var chunkNum = idx / this.chunkSize | 0;
              return this.getter(chunkNum)[chunkOffset];
            }
            setDataGetter(getter) {
              this.getter = getter;
            }
            cacheLength() {
              var xhr = new XMLHttpRequest();
              xhr.open("HEAD", url, false);
              xhr.send(null);
              if (!(xhr.status >= 200 && xhr.status < 300 || xhr.status === 304)) throw new Error("Couldn't load " + url + ". Status: " + xhr.status);
              var datalength = Number(xhr.getResponseHeader("Content-length"));
              var header;
              var hasByteServing = (header = xhr.getResponseHeader("Accept-Ranges")) && header === "bytes";
              var usesGzip = (header = xhr.getResponseHeader("Content-Encoding")) && header === "gzip";
              var chunkSize = 1024 * 1024;
              if (!hasByteServing) chunkSize = datalength;
              var doXHR = (from, to) => {
                if (from > to) throw new Error("invalid range (" + from + ", " + to + ") or no bytes requested!");
                if (to > datalength - 1) throw new Error("only " + datalength + " bytes available! programmer error!");
                var xhr2 = new XMLHttpRequest();
                xhr2.open("GET", url, false);
                if (datalength !== chunkSize) xhr2.setRequestHeader("Range", "bytes=" + from + "-" + to);
                xhr2.responseType = "arraybuffer";
                if (xhr2.overrideMimeType) {
                  xhr2.overrideMimeType("text/plain; charset=x-user-defined");
                }
                xhr2.send(null);
                if (!(xhr2.status >= 200 && xhr2.status < 300 || xhr2.status === 304)) throw new Error("Couldn't load " + url + ". Status: " + xhr2.status);
                if (xhr2.response !== void 0) {
                  return new Uint8Array(xhr2.response || []);
                }
                return intArrayFromString(xhr2.responseText || "", true);
              };
              var lazyArray2 = this;
              lazyArray2.setDataGetter((chunkNum) => {
                var start = chunkNum * chunkSize;
                var end = (chunkNum + 1) * chunkSize - 1;
                end = Math.min(end, datalength - 1);
                if (typeof lazyArray2.chunks[chunkNum] == "undefined") {
                  lazyArray2.chunks[chunkNum] = doXHR(start, end);
                }
                if (typeof lazyArray2.chunks[chunkNum] == "undefined") throw new Error("doXHR failed!");
                return lazyArray2.chunks[chunkNum];
              });
              if (usesGzip || !datalength) {
                chunkSize = datalength = 1;
                datalength = this.getter(0).length;
                chunkSize = datalength;
                out("LazyFiles on gzip forces download of the whole file when length is accessed");
              }
              this._length = datalength;
              this._chunkSize = chunkSize;
              this.lengthKnown = true;
            }
            get length() {
              if (!this.lengthKnown) {
                this.cacheLength();
              }
              return this._length;
            }
            get chunkSize() {
              if (!this.lengthKnown) {
                this.cacheLength();
              }
              return this._chunkSize;
            }
          }
          if (typeof XMLHttpRequest != "undefined") {
            if (!ENVIRONMENT_IS_WORKER) throw "Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";
            var lazyArray = new LazyUint8Array();
            var properties = { isDevice: false, contents: lazyArray };
          } else {
            var properties = { isDevice: false, url };
          }
          var node = FS.createFile(parent, name, properties, canRead, canWrite);
          if (properties.contents) {
            node.contents = properties.contents;
          } else if (properties.url) {
            node.contents = null;
            node.url = properties.url;
          }
          Object.defineProperties(node, { usedBytes: { get: function() {
            return this.contents.length;
          } } });
          var stream_ops = {};
          var keys = Object.keys(node.stream_ops);
          keys.forEach((key) => {
            var fn = node.stream_ops[key];
            stream_ops[key] = (...args) => {
              FS.forceLoadFile(node);
              return fn(...args);
            };
          });
          function writeChunks(stream, buffer, offset, length, position) {
            var contents = stream.node.contents;
            if (position >= contents.length) return 0;
            var size = Math.min(contents.length - position, length);
            if (contents.slice) {
              for (var i3 = 0; i3 < size; i3++) {
                buffer[offset + i3] = contents[position + i3];
              }
            } else {
              for (var i3 = 0; i3 < size; i3++) {
                buffer[offset + i3] = contents.get(position + i3);
              }
            }
            return size;
          }
          stream_ops.read = (stream, buffer, offset, length, position) => {
            FS.forceLoadFile(node);
            return writeChunks(stream, buffer, offset, length, position);
          };
          stream_ops.mmap = (stream, length, position, prot, flags) => {
            FS.forceLoadFile(node);
            var ptr = mmapAlloc(length);
            if (!ptr) {
              throw new FS.ErrnoError(48);
            }
            writeChunks(stream, HEAP8, ptr, length, position);
            return { ptr, allocated: true };
          };
          node.stream_ops = stream_ops;
          return node;
        } };
        var UTF8ToString = (ptr, maxBytesToRead) => ptr ? UTF8ArrayToString(HEAPU8, ptr, maxBytesToRead) : "";
        var SYSCALLS = { DEFAULT_POLLMASK: 5, calculateAt(dirfd, path, allowEmpty) {
          if (PATH.isAbs(path)) {
            return path;
          }
          var dir;
          if (dirfd === -100) {
            dir = FS.cwd();
          } else {
            var dirstream = SYSCALLS.getStreamFromFD(dirfd);
            dir = dirstream.path;
          }
          if (path.length == 0) {
            if (!allowEmpty) {
              throw new FS.ErrnoError(44);
            }
            return dir;
          }
          return dir + "/" + path;
        }, writeStat(buf, stat) {
          HEAP32[buf >> 2] = stat.dev;
          HEAP32[buf + 4 >> 2] = stat.mode;
          HEAPU32[buf + 8 >> 2] = stat.nlink;
          HEAP32[buf + 12 >> 2] = stat.uid;
          HEAP32[buf + 16 >> 2] = stat.gid;
          HEAP32[buf + 20 >> 2] = stat.rdev;
          HEAP64[buf + 24 >> 3] = BigInt(stat.size);
          HEAP32[buf + 32 >> 2] = 4096;
          HEAP32[buf + 36 >> 2] = stat.blocks;
          var atime = stat.atime.getTime();
          var mtime = stat.mtime.getTime();
          var ctime = stat.ctime.getTime();
          HEAP64[buf + 40 >> 3] = BigInt(Math.floor(atime / 1e3));
          HEAPU32[buf + 48 >> 2] = atime % 1e3 * 1e3 * 1e3;
          HEAP64[buf + 56 >> 3] = BigInt(Math.floor(mtime / 1e3));
          HEAPU32[buf + 64 >> 2] = mtime % 1e3 * 1e3 * 1e3;
          HEAP64[buf + 72 >> 3] = BigInt(Math.floor(ctime / 1e3));
          HEAPU32[buf + 80 >> 2] = ctime % 1e3 * 1e3 * 1e3;
          HEAP64[buf + 88 >> 3] = BigInt(stat.ino);
          return 0;
        }, writeStatFs(buf, stats) {
          HEAP32[buf + 4 >> 2] = stats.bsize;
          HEAP32[buf + 40 >> 2] = stats.bsize;
          HEAP32[buf + 8 >> 2] = stats.blocks;
          HEAP32[buf + 12 >> 2] = stats.bfree;
          HEAP32[buf + 16 >> 2] = stats.bavail;
          HEAP32[buf + 20 >> 2] = stats.files;
          HEAP32[buf + 24 >> 2] = stats.ffree;
          HEAP32[buf + 28 >> 2] = stats.fsid;
          HEAP32[buf + 44 >> 2] = stats.flags;
          HEAP32[buf + 36 >> 2] = stats.namelen;
        }, doMsync(addr, stream, len, flags, offset) {
          if (!FS.isFile(stream.node.mode)) {
            throw new FS.ErrnoError(43);
          }
          if (flags & 2) {
            return 0;
          }
          var buffer = HEAPU8.slice(addr, addr + len);
          FS.msync(stream, buffer, offset, len, flags);
        }, getStreamFromFD(fd) {
          var stream = FS.getStreamChecked(fd);
          return stream;
        }, varargs: void 0, getStr(ptr) {
          var ret = UTF8ToString(ptr);
          return ret;
        } };
        function _fd_close(fd) {
          try {
            var stream = SYSCALLS.getStreamFromFD(fd);
            FS.close(stream);
            return 0;
          } catch (e2) {
            if (typeof FS == "undefined" || !(e2.name === "ErrnoError")) throw e2;
            return e2.errno;
          }
        }
        var INT53_MAX = 9007199254740992;
        var INT53_MIN = -9007199254740992;
        var bigintToI53Checked = (num) => num < INT53_MIN || num > INT53_MAX ? NaN : Number(num);
        function _fd_seek(fd, offset, whence, newOffset) {
          offset = bigintToI53Checked(offset);
          try {
            if (isNaN(offset)) return 61;
            var stream = SYSCALLS.getStreamFromFD(fd);
            FS.llseek(stream, offset, whence);
            HEAP64[newOffset >> 3] = BigInt(stream.position);
            if (stream.getdents && offset === 0 && whence === 0) stream.getdents = null;
            return 0;
          } catch (e2) {
            if (typeof FS == "undefined" || !(e2.name === "ErrnoError")) throw e2;
            return e2.errno;
          }
        }
        var doWritev = (stream, iov, iovcnt, offset) => {
          var ret = 0;
          for (var i3 = 0; i3 < iovcnt; i3++) {
            var ptr = HEAPU32[iov >> 2];
            var len = HEAPU32[iov + 4 >> 2];
            iov += 8;
            var curr = FS.write(stream, HEAP8, ptr, len, offset);
            if (curr < 0) return -1;
            ret += curr;
            if (curr < len) {
              break;
            }
            if (typeof offset != "undefined") {
              offset += curr;
            }
          }
          return ret;
        };
        function _fd_write(fd, iov, iovcnt, pnum) {
          try {
            var stream = SYSCALLS.getStreamFromFD(fd);
            var num = doWritev(stream, iov, iovcnt);
            HEAPU32[pnum >> 2] = num;
            return 0;
          } catch (e2) {
            if (typeof FS == "undefined" || !(e2.name === "ErrnoError")) throw e2;
            return e2.errno;
          }
        }
        var wasmTableMirror = [];
        var wasmTable;
        var getWasmTableEntry = (funcPtr) => {
          var func = wasmTableMirror[funcPtr];
          if (!func) {
            wasmTableMirror[funcPtr] = func = wasmTable.get(funcPtr);
          }
          return func;
        };
        var getCFunc = (ident) => {
          var func = Module["_" + ident];
          return func;
        };
        var writeArrayToMemory = (array, buffer) => {
          HEAP8.set(array, buffer);
        };
        var stackAlloc = (sz) => __emscripten_stack_alloc(sz);
        var stringToUTF8OnStack = (str) => {
          var size = lengthBytesUTF8(str) + 1;
          var ret = stackAlloc(size);
          stringToUTF8(str, ret, size);
          return ret;
        };
        var ccall = (ident, returnType, argTypes, args, opts) => {
          var toC = { string: (str) => {
            var ret2 = 0;
            if (str !== null && str !== void 0 && str !== 0) {
              ret2 = stringToUTF8OnStack(str);
            }
            return ret2;
          }, array: (arr) => {
            var ret2 = stackAlloc(arr.length);
            writeArrayToMemory(arr, ret2);
            return ret2;
          } };
          function convertReturnValue(ret2) {
            if (returnType === "string") {
              return UTF8ToString(ret2);
            }
            if (returnType === "boolean") return Boolean(ret2);
            return ret2;
          }
          var func = getCFunc(ident);
          var cArgs = [];
          var stack = 0;
          if (args) {
            for (var i3 = 0; i3 < args.length; i3++) {
              var converter = toC[argTypes[i3]];
              if (converter) {
                if (stack === 0) stack = stackSave();
                cArgs[i3] = converter(args[i3]);
              } else {
                cArgs[i3] = args[i3];
              }
            }
          }
          var ret = func(...cArgs);
          function onDone(ret2) {
            if (stack !== 0) stackRestore(stack);
            return convertReturnValue(ret2);
          }
          ret = onDone(ret);
          return ret;
        };
        var cwrap = (ident, returnType, argTypes, opts) => {
          var numericArgs = !argTypes || argTypes.every((type) => type === "number" || type === "boolean");
          var numericRet = returnType !== "string";
          if (numericRet && numericArgs && !opts) {
            return getCFunc(ident);
          }
          return (...args) => ccall(ident, returnType, argTypes, args, opts);
        };
        for (var base64ReverseLookup = new Uint8Array(123), i2 = 25; i2 >= 0; --i2) {
          base64ReverseLookup[48 + i2] = 52 + i2;
          base64ReverseLookup[65 + i2] = i2;
          base64ReverseLookup[97 + i2] = 26 + i2;
        }
        base64ReverseLookup[43] = 62;
        base64ReverseLookup[47] = 63;
        FS.createPreloadedFile = FS_createPreloadedFile;
        FS.staticInit();
        MEMFS.doesNotExistError = new FS.ErrnoError(44);
        MEMFS.doesNotExistError.stack = "<generic error, no stack>";
        {
          if (Module["noExitRuntime"]) noExitRuntime = Module["noExitRuntime"];
          if (Module["preloadPlugins"]) preloadPlugins = Module["preloadPlugins"];
          if (Module["print"]) out = Module["print"];
          if (Module["printErr"]) err = Module["printErr"];
          if (Module["wasmBinary"]) wasmBinary = Module["wasmBinary"];
          if (Module["arguments"]) arguments_ = Module["arguments"];
          if (Module["thisProgram"]) thisProgram = Module["thisProgram"];
        }
        Module["ccall"] = ccall;
        Module["cwrap"] = cwrap;
        var _AecNew, _AecCancelEcho, _AecDestroy, _malloc, _free, _setThrew, __emscripten_tempret_set, __emscripten_stack_restore, __emscripten_stack_alloc, _emscripten_stack_get_current, ___cxa_can_catch;
        function assignWasmExports(wasmExports2) {
          Module["_AecNew"] = _AecNew = wasmExports2["w"];
          Module["_AecCancelEcho"] = _AecCancelEcho = wasmExports2["x"];
          Module["_AecDestroy"] = _AecDestroy = wasmExports2["y"];
          Module["_malloc"] = _malloc = wasmExports2["z"];
          Module["_free"] = _free = wasmExports2["A"];
          _setThrew = wasmExports2["B"];
          __emscripten_tempret_set = wasmExports2["C"];
          __emscripten_stack_restore = wasmExports2["D"];
          __emscripten_stack_alloc = wasmExports2["E"];
          _emscripten_stack_get_current = wasmExports2["F"];
          ___cxa_can_catch = wasmExports2["G"];
        }
        var wasmImports = { a: ___cxa_find_matching_catch_2, q: ___cxa_throw, b: ___resumeException, p: ___syscall_getcwd, r: __abort_js, t: _emscripten_resize_heap, n: _environ_get, o: _environ_sizes_get, k: _exit, s: _fd_close, l: _fd_seek, g: _fd_write, i: invoke_ii, f: invoke_iiii, m: invoke_iiiiii, c: invoke_vi, d: invoke_vii, e: invoke_viii, j: invoke_viiii, h: invoke_viiiii };
        var wasmExports = await createWasm();
        function invoke_vi(index, a1) {
          var sp = stackSave();
          try {
            getWasmTableEntry(index)(a1);
          } catch (e2) {
            stackRestore(sp);
            if (e2 !== e2 + 0) throw e2;
            _setThrew(1, 0);
          }
        }
        function invoke_viii(index, a1, a2, a3) {
          var sp = stackSave();
          try {
            getWasmTableEntry(index)(a1, a2, a3);
          } catch (e2) {
            stackRestore(sp);
            if (e2 !== e2 + 0) throw e2;
            _setThrew(1, 0);
          }
        }
        function invoke_vii(index, a1, a2) {
          var sp = stackSave();
          try {
            getWasmTableEntry(index)(a1, a2);
          } catch (e2) {
            stackRestore(sp);
            if (e2 !== e2 + 0) throw e2;
            _setThrew(1, 0);
          }
        }
        function invoke_viiii(index, a1, a2, a3, a4) {
          var sp = stackSave();
          try {
            getWasmTableEntry(index)(a1, a2, a3, a4);
          } catch (e2) {
            stackRestore(sp);
            if (e2 !== e2 + 0) throw e2;
            _setThrew(1, 0);
          }
        }
        function invoke_iiii(index, a1, a2, a3) {
          var sp = stackSave();
          try {
            return getWasmTableEntry(index)(a1, a2, a3);
          } catch (e2) {
            stackRestore(sp);
            if (e2 !== e2 + 0) throw e2;
            _setThrew(1, 0);
          }
        }
        function invoke_viiiii(index, a1, a2, a3, a4, a5) {
          var sp = stackSave();
          try {
            getWasmTableEntry(index)(a1, a2, a3, a4, a5);
          } catch (e2) {
            stackRestore(sp);
            if (e2 !== e2 + 0) throw e2;
            _setThrew(1, 0);
          }
        }
        function invoke_ii(index, a1) {
          var sp = stackSave();
          try {
            return getWasmTableEntry(index)(a1);
          } catch (e2) {
            stackRestore(sp);
            if (e2 !== e2 + 0) throw e2;
            _setThrew(1, 0);
          }
        }
        function invoke_iiiiii(index, a1, a2, a3, a4, a5) {
          var sp = stackSave();
          try {
            return getWasmTableEntry(index)(a1, a2, a3, a4, a5);
          } catch (e2) {
            stackRestore(sp);
            if (e2 !== e2 + 0) throw e2;
            _setThrew(1, 0);
          }
        }
        function run() {
          if (runDependencies > 0) {
            dependenciesFulfilled = run;
            return;
          }
          preRun();
          if (runDependencies > 0) {
            dependenciesFulfilled = run;
            return;
          }
          function doRun() {
            Module["calledRun"] = true;
            if (ABORT) return;
            initRuntime();
            readyPromiseResolve?.(Module);
            Module["onRuntimeInitialized"]?.();
            postRun();
          }
          if (Module["setStatus"]) {
            Module["setStatus"]("Running...");
            setTimeout(() => {
              setTimeout(() => Module["setStatus"](""), 1);
              doRun();
            }, 1);
          } else {
            doRun();
          }
        }
        function preInit() {
          if (Module["preInit"]) {
            if (typeof Module["preInit"] == "function") Module["preInit"] = [Module["preInit"]];
            while (Module["preInit"].length > 0) {
              Module["preInit"].shift()();
            }
          }
        }
        preInit();
        run();
        if (runtimeInitialized) {
          moduleRtn = Module;
        } else {
          moduleRtn = new Promise((resolve, reject) => {
            readyPromiseResolve = resolve;
            readyPromiseReject = reject;
          });
        }
        return moduleRtn;
      };
    })();
    if (typeof exports === "object" && typeof module === "object") {
      module.exports = createAecModule;
      module.exports.default = createAecModule;
    } else if (typeof define === "function" && define["amd"])
      define([], () => createAecModule);
  }
});

// src/features/listen/renderer/listenCapture.js
var require_listenCapture = __commonJS({
  "src/features/listen/renderer/listenCapture.js"(exports, module) {
    var { ipcRenderer: ipcRenderer2 } = __require("electron");
    var createAecModule = require_aec();
    var aecModPromise = null;
    var aecMod = null;
    var aecPtr = 0;
    async function getAec() {
      if (aecModPromise) return aecModPromise;
      aecModPromise = createAecModule().then((M2) => {
        aecMod = M2;
        M2.newPtr = M2.cwrap(
          "AecNew",
          "number",
          ["number", "number", "number", "number"]
        );
        M2.cancel = M2.cwrap(
          "AecCancelEcho",
          null,
          ["number", "number", "number", "number", "number"]
        );
        M2.destroy = M2.cwrap("AecDestroy", null, ["number"]);
        return M2;
      });
      return aecModPromise;
    }
    getAec().catch(console.error);
    var SAMPLE_RATE = 24e3;
    var AUDIO_CHUNK_DURATION = 0.1;
    var BUFFER_SIZE = 4096;
    var isLinux = process.platform === "linux";
    var isMacOS = process.platform === "darwin";
    var mediaStream = null;
    var micMediaStream = null;
    var screenshotInterval = null;
    var audioContext = null;
    var audioProcessor = null;
    var systemAudioContext = null;
    var systemAudioProcessor = null;
    var currentImageQuality = "medium";
    var lastScreenshotBase64 = null;
    var systemAudioBuffer = [];
    var MAX_SYSTEM_BUFFER_SIZE = 10;
    function base64ToFloat32Array(base64) {
      const binaryString = atob(base64);
      const bytes = new Uint8Array(binaryString.length);
      for (let i2 = 0; i2 < binaryString.length; i2++) {
        bytes[i2] = binaryString.charCodeAt(i2);
      }
      const int16Array = new Int16Array(bytes.buffer);
      const float32Array = new Float32Array(int16Array.length);
      for (let i2 = 0; i2 < int16Array.length; i2++) {
        float32Array[i2] = int16Array[i2] / 32768;
      }
      return float32Array;
    }
    function convertFloat32ToInt16(float32Array) {
      const int16Array = new Int16Array(float32Array.length);
      for (let i2 = 0; i2 < float32Array.length; i2++) {
        const s2 = Math.max(-1, Math.min(1, float32Array[i2]));
        int16Array[i2] = s2 < 0 ? s2 * 32768 : s2 * 32767;
      }
      return int16Array;
    }
    function arrayBufferToBase64(buffer) {
      let binary = "";
      const bytes = new Uint8Array(buffer);
      const len = bytes.byteLength;
      for (let i2 = 0; i2 < len; i2++) {
        binary += String.fromCharCode(bytes[i2]);
      }
      return btoa(binary);
    }
    function int16PtrFromFloat32(mod, f32) {
      const len = f32.length;
      const bytes = len * 2;
      const ptr = mod._malloc(bytes);
      const heapBuf = mod.HEAP16 ? mod.HEAP16.buffer : mod.HEAPU8.buffer;
      const i16 = new Int16Array(heapBuf, ptr, len);
      for (let i2 = 0; i2 < len; ++i2) {
        const s2 = Math.max(-1, Math.min(1, f32[i2]));
        i16[i2] = s2 < 0 ? s2 * 32768 : s2 * 32767;
      }
      return { ptr, view: i16 };
    }
    function float32FromInt16View(i16) {
      const out = new Float32Array(i16.length);
      for (let i2 = 0; i2 < i16.length; ++i2) out[i2] = i16[i2] / 32768;
      return out;
    }
    function disposeAec() {
      getAec().then((mod) => {
        if (aecPtr) mod.destroy(aecPtr);
      });
    }
    function runAecSync(micF32, sysF32) {
      if (!aecMod || !aecPtr || !aecMod.HEAPU8) return micF32;
      const len = micF32.length;
      const mic = int16PtrFromFloat32(aecMod, micF32);
      const echo = int16PtrFromFloat32(aecMod, sysF32);
      const out = aecMod._malloc(len * 2);
      aecMod.cancel(aecPtr, mic.ptr, echo.ptr, out, len);
      const heapBuf = aecMod.HEAP16 ? aecMod.HEAP16.buffer : aecMod.HEAPU8.buffer;
      const outF32 = float32FromInt16View(new Int16Array(heapBuf, out, len));
      aecMod._free(mic.ptr);
      aecMod._free(echo.ptr);
      aecMod._free(out);
      return outF32;
    }
    ipcRenderer2.on("system-audio-data", (event, { data }) => {
      systemAudioBuffer.push({
        data,
        timestamp: Date.now()
      });
      if (systemAudioBuffer.length > MAX_SYSTEM_BUFFER_SIZE) {
        systemAudioBuffer = systemAudioBuffer.slice(-MAX_SYSTEM_BUFFER_SIZE);
      }
    });
    var tokenTracker = {
      tokens: [],
      audioStartTime: null,
      addTokens(count, type = "image") {
        const now = Date.now();
        this.tokens.push({
          timestamp: now,
          count,
          type
        });
        this.cleanOldTokens();
      },
      calculateImageTokens(width, height) {
        const pixels = width * height;
        if (pixels <= 384 * 384) {
          return 85;
        }
        const tiles = Math.ceil(pixels / (768 * 768));
        return tiles * 85;
      },
      trackAudioTokens() {
        if (!this.audioStartTime) {
          this.audioStartTime = Date.now();
          return;
        }
        const now = Date.now();
        const elapsedSeconds = (now - this.audioStartTime) / 1e3;
        const audioTokens = Math.floor(elapsedSeconds * 16);
        if (audioTokens > 0) {
          this.addTokens(audioTokens, "audio");
          this.audioStartTime = now;
        }
      },
      cleanOldTokens() {
        const oneMinuteAgo = Date.now() - 60 * 1e3;
        this.tokens = this.tokens.filter((token) => token.timestamp > oneMinuteAgo);
      },
      getTokensInLastMinute() {
        this.cleanOldTokens();
        return this.tokens.reduce((total, token) => total + token.count, 0);
      },
      shouldThrottle() {
        const throttleEnabled = localStorage.getItem("throttleTokens") === "true";
        if (!throttleEnabled) {
          return false;
        }
        const maxTokensPerMin = parseInt(localStorage.getItem("maxTokensPerMin") || "500000", 10);
        const throttleAtPercent = parseInt(localStorage.getItem("throttleAtPercent") || "75", 10);
        const currentTokens = this.getTokensInLastMinute();
        const throttleThreshold = Math.floor(maxTokensPerMin * throttleAtPercent / 100);
        console.log(`Token check: ${currentTokens}/${maxTokensPerMin} (throttle at ${throttleThreshold})`);
        return currentTokens >= throttleThreshold;
      },
      // Reset the tracker
      reset() {
        this.tokens = [];
        this.audioStartTime = null;
      }
    };
    setInterval(() => {
      tokenTracker.trackAudioTokens();
    }, 2e3);
    async function setupMicProcessing(micStream) {
      const mod = await getAec();
      if (!aecPtr) aecPtr = mod.newPtr(160, 1600, 24e3, 1);
      const micAudioContext = new AudioContext({ sampleRate: SAMPLE_RATE });
      await micAudioContext.resume();
      const micSource = micAudioContext.createMediaStreamSource(micStream);
      const micProcessor = micAudioContext.createScriptProcessor(BUFFER_SIZE, 1, 1);
      let audioBuffer = [];
      const samplesPerChunk = SAMPLE_RATE * AUDIO_CHUNK_DURATION;
      micProcessor.onaudioprocess = (e2) => {
        const inputData = e2.inputBuffer.getChannelData(0);
        audioBuffer.push(...inputData);
        console.log("\u{1F3A4} micProcessor.onaudioprocess");
        while (audioBuffer.length >= samplesPerChunk) {
          let chunk = audioBuffer.splice(0, samplesPerChunk);
          let processedChunk = new Float32Array(chunk);
          if (systemAudioBuffer.length > 0) {
            const latest = systemAudioBuffer[systemAudioBuffer.length - 1];
            const sysF32 = base64ToFloat32Array(latest.data);
            processedChunk = runAecSync(new Float32Array(chunk), sysF32);
            console.log("\u{1F50A} Applied WASM-AEC (speex)");
          } else {
            console.log("\u{1F50A} No system audio for AEC reference");
          }
          const pcm16 = convertFloat32ToInt16(processedChunk);
          const b64 = arrayBufferToBase64(pcm16.buffer);
          ipcRenderer2.invoke("send-audio-content", {
            data: b64,
            mimeType: "audio/pcm;rate=24000"
          });
        }
      };
      micSource.connect(micProcessor);
      micProcessor.connect(micAudioContext.destination);
      audioProcessor = micProcessor;
      return { context: micAudioContext, processor: micProcessor };
    }
    function setupLinuxMicProcessing(micStream) {
      const micAudioContext = new AudioContext({ sampleRate: SAMPLE_RATE });
      const micSource = micAudioContext.createMediaStreamSource(micStream);
      const micProcessor = micAudioContext.createScriptProcessor(BUFFER_SIZE, 1, 1);
      let audioBuffer = [];
      const samplesPerChunk = SAMPLE_RATE * AUDIO_CHUNK_DURATION;
      micProcessor.onaudioprocess = async (e2) => {
        const inputData = e2.inputBuffer.getChannelData(0);
        audioBuffer.push(...inputData);
        while (audioBuffer.length >= samplesPerChunk) {
          const chunk = audioBuffer.splice(0, samplesPerChunk);
          const pcmData16 = convertFloat32ToInt16(chunk);
          const base64Data = arrayBufferToBase64(pcmData16.buffer);
          await ipcRenderer2.invoke("send-audio-content", {
            data: base64Data,
            mimeType: "audio/pcm;rate=24000"
          });
        }
      };
      micSource.connect(micProcessor);
      micProcessor.connect(micAudioContext.destination);
      audioProcessor = micProcessor;
    }
    function setupSystemAudioProcessing(systemStream) {
      const systemAudioContext2 = new AudioContext({ sampleRate: SAMPLE_RATE });
      const systemSource = systemAudioContext2.createMediaStreamSource(systemStream);
      const systemProcessor = systemAudioContext2.createScriptProcessor(BUFFER_SIZE, 1, 1);
      let audioBuffer = [];
      const samplesPerChunk = SAMPLE_RATE * AUDIO_CHUNK_DURATION;
      systemProcessor.onaudioprocess = async (e2) => {
        const inputData = e2.inputBuffer.getChannelData(0);
        if (!inputData || inputData.length === 0) return;
        audioBuffer.push(...inputData);
        while (audioBuffer.length >= samplesPerChunk) {
          const chunk = audioBuffer.splice(0, samplesPerChunk);
          const pcmData16 = convertFloat32ToInt16(chunk);
          const base64Data = arrayBufferToBase64(pcmData16.buffer);
          try {
            await ipcRenderer2.invoke("send-system-audio-content", {
              data: base64Data,
              mimeType: "audio/pcm;rate=24000"
            });
          } catch (error) {
            console.error("Failed to send system audio:", error);
          }
        }
      };
      systemSource.connect(systemProcessor);
      systemProcessor.connect(systemAudioContext2.destination);
      return { context: systemAudioContext2, processor: systemProcessor };
    }
    async function captureScreenshot(imageQuality = "medium", isManual = false) {
      console.log(`Capturing ${isManual ? "manual" : "automated"} screenshot...`);
      if (!isManual && tokenTracker.shouldThrottle()) {
        console.log("\u26A0\uFE0F Automated screenshot skipped due to rate limiting");
        return;
      }
      try {
        const result = await ipcRenderer2.invoke("capture-screenshot", {
          quality: imageQuality
        });
        if (result.success && result.base64) {
          lastScreenshotBase64 = result.base64;
        } else {
          console.error("Failed to capture screenshot:", result.error);
        }
      } catch (error) {
        console.error("Error capturing screenshot:", error);
      }
    }
    async function captureManualScreenshot(imageQuality = null) {
      console.log("Manual screenshot triggered");
      const quality = imageQuality || currentImageQuality;
      await captureScreenshot(quality, true);
    }
    async function getCurrentScreenshot() {
      try {
        const result = await ipcRenderer2.invoke("get-current-screenshot");
        if (result.success && result.base64) {
          console.log("\u{1F4F8} Got fresh screenshot from main process");
          return result.base64;
        }
        console.log("\u{1F4F8} No screenshot available, capturing new one");
        const captureResult = await ipcRenderer2.invoke("capture-screenshot", {
          quality: currentImageQuality
        });
        if (captureResult.success && captureResult.base64) {
          lastScreenshotBase64 = captureResult.base64;
          return captureResult.base64;
        }
        if (lastScreenshotBase64) {
          console.log("\u{1F4F8} Using cached screenshot");
          return lastScreenshotBase64;
        }
        throw new Error("Failed to get screenshot");
      } catch (error) {
        console.error("Error getting current screenshot:", error);
        return null;
      }
    }
    async function startCapture(screenshotIntervalSeconds = 5, imageQuality = "medium") {
      currentImageQuality = imageQuality;
      tokenTracker.reset();
      console.log("\u{1F3AF} Token tracker reset for new capture session");
      try {
        if (isMacOS) {
          console.log("Starting macOS capture with SystemAudioDump...");
          const audioResult = await ipcRenderer2.invoke("start-macos-audio");
          if (!audioResult.success) {
            console.warn("[listenCapture] macOS audio start failed:", audioResult.error);
            if (audioResult.error === "already_running") {
              await ipcRenderer2.invoke("stop-macos-audio");
              await new Promise((r2) => setTimeout(r2, 500));
              const retry = await ipcRenderer2.invoke("start-macos-audio");
              if (!retry.success) {
                throw new Error("Retry failed: " + retry.error);
              }
            } else {
              throw new Error("Failed to start macOS audio capture: " + audioResult.error);
            }
          }
          const screenResult = await ipcRenderer2.invoke("start-screen-capture");
          if (!screenResult.success) {
            throw new Error("Failed to start screen capture: " + screenResult.error);
          }
          try {
            micMediaStream = await navigator.mediaDevices.getUserMedia({
              audio: {
                sampleRate: SAMPLE_RATE,
                channelCount: 1,
                echoCancellation: true,
                noiseSuppression: true,
                autoGainControl: true
              },
              video: false
            });
            console.log("macOS microphone capture started");
            const { context, processor } = await setupMicProcessing(micMediaStream);
            audioContext = context;
            audioProcessor = processor;
          } catch (micErr) {
            console.warn("Failed to get microphone on macOS:", micErr);
          }
          console.log("macOS screen capture started - audio handled by SystemAudioDump");
        } else if (isLinux) {
          mediaStream = await navigator.mediaDevices.getDisplayMedia({
            video: {
              frameRate: 1,
              width: { ideal: 1920 },
              height: { ideal: 1080 }
            },
            audio: false
            // Don't use system audio loopback on Linux
          });
          let micMediaStream2 = null;
          try {
            micMediaStream2 = await navigator.mediaDevices.getUserMedia({
              audio: {
                sampleRate: SAMPLE_RATE,
                channelCount: 1,
                echoCancellation: true,
                noiseSuppression: true,
                autoGainControl: true
              },
              video: false
            });
            console.log("Linux microphone capture started");
            setupLinuxMicProcessing(micMediaStream2);
          } catch (micError) {
            console.warn("Failed to get microphone access on Linux:", micError);
          }
          console.log("Linux screen capture started");
        } else {
          console.log("Starting Windows capture with native loopback audio...");
          const screenResult = await ipcRenderer2.invoke("start-screen-capture");
          if (!screenResult.success) {
            throw new Error("Failed to start screen capture: " + screenResult.error);
          }
          const sessionActive = await ipcRenderer2.invoke("is-session-active");
          if (!sessionActive) {
            throw new Error("STT sessions not initialized - please wait for initialization to complete");
          }
          try {
            micMediaStream = await navigator.mediaDevices.getUserMedia({
              audio: {
                sampleRate: SAMPLE_RATE,
                channelCount: 1,
                echoCancellation: true,
                noiseSuppression: true,
                autoGainControl: true
              },
              video: false
            });
            console.log("Windows microphone capture started");
            const { context, processor } = await setupMicProcessing(micMediaStream);
            audioContext = context;
            audioProcessor = processor;
          } catch (micErr) {
            console.warn("Could not get microphone access on Windows:", micErr);
          }
          try {
            mediaStream = await navigator.mediaDevices.getDisplayMedia({
              video: true,
              audio: true
              // This will now use native loopback from our handler
            });
            const audioTracks = mediaStream.getAudioTracks();
            if (audioTracks.length === 0) {
              throw new Error("No audio track in native loopback stream");
            }
            console.log("Windows native loopback audio capture started");
            const { context, processor } = setupSystemAudioProcessing(mediaStream);
            systemAudioContext = context;
            systemAudioProcessor = processor;
          } catch (sysAudioErr) {
            console.error("Failed to start Windows native loopback audio:", sysAudioErr);
          }
        }
        if (screenshotIntervalSeconds === "manual" || screenshotIntervalSeconds === "Manual") {
          console.log("Manual mode enabled - screenshots will be captured on demand only");
        } else {
          const intervalMilliseconds = parseInt(screenshotIntervalSeconds) * 1e3;
          screenshotInterval = setInterval(() => captureScreenshot(imageQuality), intervalMilliseconds);
          setTimeout(() => captureScreenshot(imageQuality), 100);
          console.log(`\u{1F4F8} Screenshot capture enabled with ${screenshotIntervalSeconds}s interval`);
        }
      } catch (err) {
        console.error("Error starting capture:", err);
      }
    }
    function stopCapture() {
      if (screenshotInterval) {
        clearInterval(screenshotInterval);
        screenshotInterval = null;
      }
      if (audioProcessor) {
        audioProcessor.disconnect();
        audioProcessor = null;
      }
      if (audioContext) {
        audioContext.close();
        audioContext = null;
      }
      if (systemAudioProcessor) {
        systemAudioProcessor.disconnect();
        systemAudioProcessor = null;
      }
      if (systemAudioContext) {
        systemAudioContext.close();
        systemAudioContext = null;
      }
      if (mediaStream) {
        mediaStream.getTracks().forEach((track) => track.stop());
        mediaStream = null;
      }
      if (micMediaStream) {
        micMediaStream.getTracks().forEach((t2) => t2.stop());
        micMediaStream = null;
      }
      ipcRenderer2.invoke("stop-screen-capture").catch((err) => {
        console.error("Error stopping screen capture:", err);
      });
      if (isMacOS) {
        ipcRenderer2.invoke("stop-macos-audio").catch((err) => {
          console.error("Error stopping macOS audio:", err);
        });
      }
    }
    module.exports = {
      getAec,
      // 새로 만든 초기화 함수
      runAecSync,
      // sync 버전
      disposeAec,
      // 필요시 Rust 객체 파괴
      startCapture,
      stopCapture,
      captureManualScreenshot,
      getCurrentScreenshot,
      isLinux,
      isMacOS
    };
    if (typeof window !== "undefined") {
      window.captureManualScreenshot = captureManualScreenshot;
      window.listenCapture = module.exports;
      window.pickleGlass = window.pickleGlass || {};
      window.pickleGlass.startCapture = startCapture;
      window.pickleGlass.stopCapture = stopCapture;
      window.pickleGlass.captureManualScreenshot = captureManualScreenshot;
      window.pickleGlass.getCurrentScreenshot = getCurrentScreenshot;
    }
  }
});

// src/assets/lit-core-2.7.4.min.js
var t = window;
var i = t.ShadowRoot && (void 0 === t.ShadyCSS || t.ShadyCSS.nativeShadow) && "adoptedStyleSheets" in Document.prototype && "replace" in CSSStyleSheet.prototype;
var s = Symbol();
var e = /* @__PURE__ */ new WeakMap();
var o = class {
  constructor(t2, e2, i2) {
    if (this._$cssResult$ = true, i2 !== s) throw Error("CSSResult is not constructable. Use `unsafeCSS` or `css` instead.");
    this.cssText = t2, this.t = e2;
  }
  get styleSheet() {
    let t2 = this.i;
    const s2 = this.t;
    if (i && void 0 === t2) {
      const i2 = void 0 !== s2 && 1 === s2.length;
      i2 && (t2 = e.get(s2)), void 0 === t2 && ((this.i = t2 = new CSSStyleSheet()).replaceSync(this.cssText), i2 && e.set(s2, t2));
    }
    return t2;
  }
  toString() {
    return this.cssText;
  }
};
var n = (t2) => new o("string" == typeof t2 ? t2 : t2 + "", void 0, s);
var r = (t2, ...e2) => {
  const i2 = 1 === t2.length ? t2[0] : e2.reduce((e3, s2, i3) => e3 + ((t3) => {
    if (true === t3._$cssResult$) return t3.cssText;
    if ("number" == typeof t3) return t3;
    throw Error("Value passed to 'css' function must be a 'css' function result: " + t3 + ". Use 'unsafeCSS' to pass non-literal values, but take care to ensure page security.");
  })(s2) + t2[i3 + 1], t2[0]);
  return new o(i2, t2, s);
};
var h = (e2, s2) => {
  i ? e2.adoptedStyleSheets = s2.map((t2) => t2 instanceof CSSStyleSheet ? t2 : t2.styleSheet) : s2.forEach((s3) => {
    const i2 = document.createElement("style"), n2 = t.litNonce;
    void 0 !== n2 && i2.setAttribute("nonce", n2), i2.textContent = s3.cssText, e2.appendChild(i2);
  });
};
var l = i ? (t2) => t2 : (t2) => t2 instanceof CSSStyleSheet ? ((t3) => {
  let e2 = "";
  for (const s2 of t3.cssRules) e2 += s2.cssText;
  return n(e2);
})(t2) : t2;
var a;
var u = window;
var c = u.trustedTypes;
var d = c ? c.emptyScript : "";
var v = u.reactiveElementPolyfillSupport;
var p = { toAttribute(t2, e2) {
  switch (e2) {
    case Boolean:
      t2 = t2 ? d : null;
      break;
    case Object:
    case Array:
      t2 = null == t2 ? t2 : JSON.stringify(t2);
  }
  return t2;
}, fromAttribute(t2, e2) {
  let s2 = t2;
  switch (e2) {
    case Boolean:
      s2 = null !== t2;
      break;
    case Number:
      s2 = null === t2 ? null : Number(t2);
      break;
    case Object:
    case Array:
      try {
        s2 = JSON.parse(t2);
      } catch (t3) {
        s2 = null;
      }
  }
  return s2;
} };
var f = (t2, e2) => e2 !== t2 && (e2 == e2 || t2 == t2);
var m = { attribute: true, type: String, converter: p, reflect: false, hasChanged: f };
var y = "finalized";
var _ = class extends HTMLElement {
  constructor() {
    super(), this.o = /* @__PURE__ */ new Map(), this.isUpdatePending = false, this.hasUpdated = false, this.l = null, this.u();
  }
  static addInitializer(t2) {
    var e2;
    this.finalize(), (null !== (e2 = this.v) && void 0 !== e2 ? e2 : this.v = []).push(t2);
  }
  static get observedAttributes() {
    this.finalize();
    const t2 = [];
    return this.elementProperties.forEach((e2, s2) => {
      const i2 = this.p(s2, e2);
      void 0 !== i2 && (this.m.set(i2, s2), t2.push(i2));
    }), t2;
  }
  static createProperty(t2, e2 = m) {
    if (e2.state && (e2.attribute = false), this.finalize(), this.elementProperties.set(t2, e2), !e2.noAccessor && !this.prototype.hasOwnProperty(t2)) {
      const s2 = "symbol" == typeof t2 ? Symbol() : "__" + t2, i2 = this.getPropertyDescriptor(t2, s2, e2);
      void 0 !== i2 && Object.defineProperty(this.prototype, t2, i2);
    }
  }
  static getPropertyDescriptor(t2, e2, s2) {
    return { get() {
      return this[e2];
    }, set(i2) {
      const n2 = this[t2];
      this[e2] = i2, this.requestUpdate(t2, n2, s2);
    }, configurable: true, enumerable: true };
  }
  static getPropertyOptions(t2) {
    return this.elementProperties.get(t2) || m;
  }
  static finalize() {
    if (this.hasOwnProperty(y)) return false;
    this[y] = true;
    const t2 = Object.getPrototypeOf(this);
    if (t2.finalize(), void 0 !== t2.v && (this.v = [...t2.v]), this.elementProperties = new Map(t2.elementProperties), this.m = /* @__PURE__ */ new Map(), this.hasOwnProperty("properties")) {
      const t3 = this.properties, e2 = [...Object.getOwnPropertyNames(t3), ...Object.getOwnPropertySymbols(t3)];
      for (const s2 of e2) this.createProperty(s2, t3[s2]);
    }
    return this.elementStyles = this.finalizeStyles(this.styles), true;
  }
  static finalizeStyles(t2) {
    const e2 = [];
    if (Array.isArray(t2)) {
      const s2 = new Set(t2.flat(1 / 0).reverse());
      for (const t3 of s2) e2.unshift(l(t3));
    } else void 0 !== t2 && e2.push(l(t2));
    return e2;
  }
  static p(t2, e2) {
    const s2 = e2.attribute;
    return false === s2 ? void 0 : "string" == typeof s2 ? s2 : "string" == typeof t2 ? t2.toLowerCase() : void 0;
  }
  u() {
    var t2;
    this._ = new Promise((t3) => this.enableUpdating = t3), this._$AL = /* @__PURE__ */ new Map(), this.g(), this.requestUpdate(), null === (t2 = this.constructor.v) || void 0 === t2 || t2.forEach((t3) => t3(this));
  }
  addController(t2) {
    var e2, s2;
    (null !== (e2 = this.S) && void 0 !== e2 ? e2 : this.S = []).push(t2), void 0 !== this.renderRoot && this.isConnected && (null === (s2 = t2.hostConnected) || void 0 === s2 || s2.call(t2));
  }
  removeController(t2) {
    var e2;
    null === (e2 = this.S) || void 0 === e2 || e2.splice(this.S.indexOf(t2) >>> 0, 1);
  }
  g() {
    this.constructor.elementProperties.forEach((t2, e2) => {
      this.hasOwnProperty(e2) && (this.o.set(e2, this[e2]), delete this[e2]);
    });
  }
  createRenderRoot() {
    var t2;
    const e2 = null !== (t2 = this.shadowRoot) && void 0 !== t2 ? t2 : this.attachShadow(this.constructor.shadowRootOptions);
    return h(e2, this.constructor.elementStyles), e2;
  }
  connectedCallback() {
    var t2;
    void 0 === this.renderRoot && (this.renderRoot = this.createRenderRoot()), this.enableUpdating(true), null === (t2 = this.S) || void 0 === t2 || t2.forEach((t3) => {
      var e2;
      return null === (e2 = t3.hostConnected) || void 0 === e2 ? void 0 : e2.call(t3);
    });
  }
  enableUpdating(t2) {
  }
  disconnectedCallback() {
    var t2;
    null === (t2 = this.S) || void 0 === t2 || t2.forEach((t3) => {
      var e2;
      return null === (e2 = t3.hostDisconnected) || void 0 === e2 ? void 0 : e2.call(t3);
    });
  }
  attributeChangedCallback(t2, e2, s2) {
    this._$AK(t2, s2);
  }
  $(t2, e2, s2 = m) {
    var i2;
    const n2 = this.constructor.p(t2, s2);
    if (void 0 !== n2 && true === s2.reflect) {
      const o2 = (void 0 !== (null === (i2 = s2.converter) || void 0 === i2 ? void 0 : i2.toAttribute) ? s2.converter : p).toAttribute(e2, s2.type);
      this.l = t2, null == o2 ? this.removeAttribute(n2) : this.setAttribute(n2, o2), this.l = null;
    }
  }
  _$AK(t2, e2) {
    var s2;
    const i2 = this.constructor, n2 = i2.m.get(t2);
    if (void 0 !== n2 && this.l !== n2) {
      const t3 = i2.getPropertyOptions(n2), o2 = "function" == typeof t3.converter ? { fromAttribute: t3.converter } : void 0 !== (null === (s2 = t3.converter) || void 0 === s2 ? void 0 : s2.fromAttribute) ? t3.converter : p;
      this.l = n2, this[n2] = o2.fromAttribute(e2, t3.type), this.l = null;
    }
  }
  requestUpdate(t2, e2, s2) {
    let i2 = true;
    void 0 !== t2 && (((s2 = s2 || this.constructor.getPropertyOptions(t2)).hasChanged || f)(this[t2], e2) ? (this._$AL.has(t2) || this._$AL.set(t2, e2), true === s2.reflect && this.l !== t2 && (void 0 === this.C && (this.C = /* @__PURE__ */ new Map()), this.C.set(t2, s2))) : i2 = false), !this.isUpdatePending && i2 && (this._ = this.T());
  }
  async T() {
    this.isUpdatePending = true;
    try {
      await this._;
    } catch (t3) {
      Promise.reject(t3);
    }
    const t2 = this.scheduleUpdate();
    return null != t2 && await t2, !this.isUpdatePending;
  }
  scheduleUpdate() {
    return this.performUpdate();
  }
  performUpdate() {
    var t2;
    if (!this.isUpdatePending) return;
    this.hasUpdated, this.o && (this.o.forEach((t3, e3) => this[e3] = t3), this.o = void 0);
    let e2 = false;
    const s2 = this._$AL;
    try {
      e2 = this.shouldUpdate(s2), e2 ? (this.willUpdate(s2), null === (t2 = this.S) || void 0 === t2 || t2.forEach((t3) => {
        var e3;
        return null === (e3 = t3.hostUpdate) || void 0 === e3 ? void 0 : e3.call(t3);
      }), this.update(s2)) : this.P();
    } catch (t3) {
      throw e2 = false, this.P(), t3;
    }
    e2 && this._$AE(s2);
  }
  willUpdate(t2) {
  }
  _$AE(t2) {
    var e2;
    null === (e2 = this.S) || void 0 === e2 || e2.forEach((t3) => {
      var e3;
      return null === (e3 = t3.hostUpdated) || void 0 === e3 ? void 0 : e3.call(t3);
    }), this.hasUpdated || (this.hasUpdated = true, this.firstUpdated(t2)), this.updated(t2);
  }
  P() {
    this._$AL = /* @__PURE__ */ new Map(), this.isUpdatePending = false;
  }
  get updateComplete() {
    return this.getUpdateComplete();
  }
  getUpdateComplete() {
    return this._;
  }
  shouldUpdate(t2) {
    return true;
  }
  update(t2) {
    void 0 !== this.C && (this.C.forEach((t3, e2) => this.$(e2, this[e2], t3)), this.C = void 0), this.P();
  }
  updated(t2) {
  }
  firstUpdated(t2) {
  }
};
var b;
_[y] = true, _.elementProperties = /* @__PURE__ */ new Map(), _.elementStyles = [], _.shadowRootOptions = { mode: "open" }, null == v || v({ ReactiveElement: _ }), (null !== (a = u.reactiveElementVersions) && void 0 !== a ? a : u.reactiveElementVersions = []).push("1.6.1");
var g = window;
var w = g.trustedTypes;
var S = w ? w.createPolicy("lit-html", { createHTML: (t2) => t2 }) : void 0;
var $ = "$lit$";
var C = `lit$${(Math.random() + "").slice(9)}$`;
var T = "?" + C;
var P = `<${T}>`;
var x = document;
var A = () => x.createComment("");
var k = (t2) => null === t2 || "object" != typeof t2 && "function" != typeof t2;
var E = Array.isArray;
var M = (t2) => E(t2) || "function" == typeof (null == t2 ? void 0 : t2[Symbol.iterator]);
var U = "[ 	\n\f\r]";
var N = /<(?:(!--|\/[^a-zA-Z])|(\/?[a-zA-Z][^>\s]*)|(\/?$))/g;
var R = /-->/g;
var O = />/g;
var V = RegExp(`>|${U}(?:([^\\s"'>=/]+)(${U}*=${U}*(?:[^ 	
\f\r"'\`<>=]|("|')|))|$)`, "g");
var j = /'/g;
var z = /"/g;
var L = /^(?:script|style|textarea|title)$/i;
var I = (t2) => (e2, ...s2) => ({ _$litType$: t2, strings: e2, values: s2 });
var H = I(1);
var B = I(2);
var D = Symbol.for("lit-noChange");
var q = Symbol.for("lit-nothing");
var J = /* @__PURE__ */ new WeakMap();
var W = x.createTreeWalker(x, 129, null, false);
var Z = (t2, e2) => {
  const s2 = t2.length - 1, i2 = [];
  let n2, o2 = 2 === e2 ? "<svg>" : "", r2 = N;
  for (let e3 = 0; e3 < s2; e3++) {
    const s3 = t2[e3];
    let l3, h2, a2 = -1, d2 = 0;
    for (; d2 < s3.length && (r2.lastIndex = d2, h2 = r2.exec(s3), null !== h2); ) d2 = r2.lastIndex, r2 === N ? "!--" === h2[1] ? r2 = R : void 0 !== h2[1] ? r2 = O : void 0 !== h2[2] ? (L.test(h2[2]) && (n2 = RegExp("</" + h2[2], "g")), r2 = V) : void 0 !== h2[3] && (r2 = V) : r2 === V ? ">" === h2[0] ? (r2 = null != n2 ? n2 : N, a2 = -1) : void 0 === h2[1] ? a2 = -2 : (a2 = r2.lastIndex - h2[2].length, l3 = h2[1], r2 = void 0 === h2[3] ? V : '"' === h2[3] ? z : j) : r2 === z || r2 === j ? r2 = V : r2 === R || r2 === O ? r2 = N : (r2 = V, n2 = void 0);
    const c2 = r2 === V && t2[e3 + 1].startsWith("/>") ? " " : "";
    o2 += r2 === N ? s3 + P : a2 >= 0 ? (i2.push(l3), s3.slice(0, a2) + $ + s3.slice(a2) + C + c2) : s3 + C + (-2 === a2 ? (i2.push(void 0), e3) : c2);
  }
  const l2 = o2 + (t2[s2] || "<?>") + (2 === e2 ? "</svg>" : "");
  if (!Array.isArray(t2) || !t2.hasOwnProperty("raw")) throw Error("invalid template strings array");
  return [void 0 !== S ? S.createHTML(l2) : l2, i2];
};
var F = class _F {
  constructor({ strings: t2, _$litType$: e2 }, s2) {
    let i2;
    this.parts = [];
    let n2 = 0, o2 = 0;
    const r2 = t2.length - 1, l2 = this.parts, [h2, a2] = Z(t2, e2);
    if (this.el = _F.createElement(h2, s2), W.currentNode = this.el.content, 2 === e2) {
      const t3 = this.el.content, e3 = t3.firstChild;
      e3.remove(), t3.append(...e3.childNodes);
    }
    for (; null !== (i2 = W.nextNode()) && l2.length < r2; ) {
      if (1 === i2.nodeType) {
        if (i2.hasAttributes()) {
          const t3 = [];
          for (const e3 of i2.getAttributeNames()) if (e3.endsWith($) || e3.startsWith(C)) {
            const s3 = a2[o2++];
            if (t3.push(e3), void 0 !== s3) {
              const t4 = i2.getAttribute(s3.toLowerCase() + $).split(C), e4 = /([.?@])?(.*)/.exec(s3);
              l2.push({ type: 1, index: n2, name: e4[2], strings: t4, ctor: "." === e4[1] ? Y : "?" === e4[1] ? it : "@" === e4[1] ? st : X });
            } else l2.push({ type: 6, index: n2 });
          }
          for (const e3 of t3) i2.removeAttribute(e3);
        }
        if (L.test(i2.tagName)) {
          const t3 = i2.textContent.split(C), e3 = t3.length - 1;
          if (e3 > 0) {
            i2.textContent = w ? w.emptyScript : "";
            for (let s3 = 0; s3 < e3; s3++) i2.append(t3[s3], A()), W.nextNode(), l2.push({ type: 2, index: ++n2 });
            i2.append(t3[e3], A());
          }
        }
      } else if (8 === i2.nodeType) if (i2.data === T) l2.push({ type: 2, index: n2 });
      else {
        let t3 = -1;
        for (; -1 !== (t3 = i2.data.indexOf(C, t3 + 1)); ) l2.push({ type: 7, index: n2 }), t3 += C.length - 1;
      }
      n2++;
    }
  }
  static createElement(t2, e2) {
    const s2 = x.createElement("template");
    return s2.innerHTML = t2, s2;
  }
};
function G(t2, e2, s2 = t2, i2) {
  var n2, o2, r2, l2;
  if (e2 === D) return e2;
  let h2 = void 0 !== i2 ? null === (n2 = s2.A) || void 0 === n2 ? void 0 : n2[i2] : s2.k;
  const a2 = k(e2) ? void 0 : e2._$litDirective$;
  return (null == h2 ? void 0 : h2.constructor) !== a2 && (null === (o2 = null == h2 ? void 0 : h2._$AO) || void 0 === o2 || o2.call(h2, false), void 0 === a2 ? h2 = void 0 : (h2 = new a2(t2), h2._$AT(t2, s2, i2)), void 0 !== i2 ? (null !== (r2 = (l2 = s2).A) && void 0 !== r2 ? r2 : l2.A = [])[i2] = h2 : s2.k = h2), void 0 !== h2 && (e2 = G(t2, h2._$AS(t2, e2.values), h2, i2)), e2;
}
var K = class {
  constructor(t2, e2) {
    this._$AV = [], this._$AN = void 0, this._$AD = t2, this._$AM = e2;
  }
  get parentNode() {
    return this._$AM.parentNode;
  }
  get _$AU() {
    return this._$AM._$AU;
  }
  M(t2) {
    var e2;
    const { el: { content: s2 }, parts: i2 } = this._$AD, n2 = (null !== (e2 = null == t2 ? void 0 : t2.creationScope) && void 0 !== e2 ? e2 : x).importNode(s2, true);
    W.currentNode = n2;
    let o2 = W.nextNode(), r2 = 0, l2 = 0, h2 = i2[0];
    for (; void 0 !== h2; ) {
      if (r2 === h2.index) {
        let e3;
        2 === h2.type ? e3 = new Q(o2, o2.nextSibling, this, t2) : 1 === h2.type ? e3 = new h2.ctor(o2, h2.name, h2.strings, this, t2) : 6 === h2.type && (e3 = new et(o2, this, t2)), this._$AV.push(e3), h2 = i2[++l2];
      }
      r2 !== (null == h2 ? void 0 : h2.index) && (o2 = W.nextNode(), r2++);
    }
    return n2;
  }
  U(t2) {
    let e2 = 0;
    for (const s2 of this._$AV) void 0 !== s2 && (void 0 !== s2.strings ? (s2._$AI(t2, s2, e2), e2 += s2.strings.length - 2) : s2._$AI(t2[e2])), e2++;
  }
};
var Q = class _Q {
  constructor(t2, e2, s2, i2) {
    var n2;
    this.type = 2, this._$AH = q, this._$AN = void 0, this._$AA = t2, this._$AB = e2, this._$AM = s2, this.options = i2, this.N = null === (n2 = null == i2 ? void 0 : i2.isConnected) || void 0 === n2 || n2;
  }
  get _$AU() {
    var t2, e2;
    return null !== (e2 = null === (t2 = this._$AM) || void 0 === t2 ? void 0 : t2._$AU) && void 0 !== e2 ? e2 : this.N;
  }
  get parentNode() {
    let t2 = this._$AA.parentNode;
    const e2 = this._$AM;
    return void 0 !== e2 && 11 === (null == t2 ? void 0 : t2.nodeType) && (t2 = e2.parentNode), t2;
  }
  get startNode() {
    return this._$AA;
  }
  get endNode() {
    return this._$AB;
  }
  _$AI(t2, e2 = this) {
    t2 = G(this, t2, e2), k(t2) ? t2 === q || null == t2 || "" === t2 ? (this._$AH !== q && this._$AR(), this._$AH = q) : t2 !== this._$AH && t2 !== D && this.R(t2) : void 0 !== t2._$litType$ ? this.O(t2) : void 0 !== t2.nodeType ? this.V(t2) : M(t2) ? this.j(t2) : this.R(t2);
  }
  L(t2) {
    return this._$AA.parentNode.insertBefore(t2, this._$AB);
  }
  V(t2) {
    this._$AH !== t2 && (this._$AR(), this._$AH = this.L(t2));
  }
  R(t2) {
    this._$AH !== q && k(this._$AH) ? this._$AA.nextSibling.data = t2 : this.V(x.createTextNode(t2)), this._$AH = t2;
  }
  O(t2) {
    var e2;
    const { values: s2, _$litType$: i2 } = t2, n2 = "number" == typeof i2 ? this._$AC(t2) : (void 0 === i2.el && (i2.el = F.createElement(i2.h, this.options)), i2);
    if ((null === (e2 = this._$AH) || void 0 === e2 ? void 0 : e2._$AD) === n2) this._$AH.U(s2);
    else {
      const t3 = new K(n2, this), e3 = t3.M(this.options);
      t3.U(s2), this.V(e3), this._$AH = t3;
    }
  }
  _$AC(t2) {
    let e2 = J.get(t2.strings);
    return void 0 === e2 && J.set(t2.strings, e2 = new F(t2)), e2;
  }
  j(t2) {
    E(this._$AH) || (this._$AH = [], this._$AR());
    const e2 = this._$AH;
    let s2, i2 = 0;
    for (const n2 of t2) i2 === e2.length ? e2.push(s2 = new _Q(this.L(A()), this.L(A()), this, this.options)) : s2 = e2[i2], s2._$AI(n2), i2++;
    i2 < e2.length && (this._$AR(s2 && s2._$AB.nextSibling, i2), e2.length = i2);
  }
  _$AR(t2 = this._$AA.nextSibling, e2) {
    var s2;
    for (null === (s2 = this._$AP) || void 0 === s2 || s2.call(this, false, true, e2); t2 && t2 !== this._$AB; ) {
      const e3 = t2.nextSibling;
      t2.remove(), t2 = e3;
    }
  }
  setConnected(t2) {
    var e2;
    void 0 === this._$AM && (this.N = t2, null === (e2 = this._$AP) || void 0 === e2 || e2.call(this, t2));
  }
};
var X = class {
  constructor(t2, e2, s2, i2, n2) {
    this.type = 1, this._$AH = q, this._$AN = void 0, this.element = t2, this.name = e2, this._$AM = i2, this.options = n2, s2.length > 2 || "" !== s2[0] || "" !== s2[1] ? (this._$AH = Array(s2.length - 1).fill(new String()), this.strings = s2) : this._$AH = q;
  }
  get tagName() {
    return this.element.tagName;
  }
  get _$AU() {
    return this._$AM._$AU;
  }
  _$AI(t2, e2 = this, s2, i2) {
    const n2 = this.strings;
    let o2 = false;
    if (void 0 === n2) t2 = G(this, t2, e2, 0), o2 = !k(t2) || t2 !== this._$AH && t2 !== D, o2 && (this._$AH = t2);
    else {
      const i3 = t2;
      let r2, l2;
      for (t2 = n2[0], r2 = 0; r2 < n2.length - 1; r2++) l2 = G(this, i3[s2 + r2], e2, r2), l2 === D && (l2 = this._$AH[r2]), o2 || (o2 = !k(l2) || l2 !== this._$AH[r2]), l2 === q ? t2 = q : t2 !== q && (t2 += (null != l2 ? l2 : "") + n2[r2 + 1]), this._$AH[r2] = l2;
    }
    o2 && !i2 && this.I(t2);
  }
  I(t2) {
    t2 === q ? this.element.removeAttribute(this.name) : this.element.setAttribute(this.name, null != t2 ? t2 : "");
  }
};
var Y = class extends X {
  constructor() {
    super(...arguments), this.type = 3;
  }
  I(t2) {
    this.element[this.name] = t2 === q ? void 0 : t2;
  }
};
var tt = w ? w.emptyScript : "";
var it = class extends X {
  constructor() {
    super(...arguments), this.type = 4;
  }
  I(t2) {
    t2 && t2 !== q ? this.element.setAttribute(this.name, tt) : this.element.removeAttribute(this.name);
  }
};
var st = class extends X {
  constructor(t2, e2, s2, i2, n2) {
    super(t2, e2, s2, i2, n2), this.type = 5;
  }
  _$AI(t2, e2 = this) {
    var s2;
    if ((t2 = null !== (s2 = G(this, t2, e2, 0)) && void 0 !== s2 ? s2 : q) === D) return;
    const i2 = this._$AH, n2 = t2 === q && i2 !== q || t2.capture !== i2.capture || t2.once !== i2.once || t2.passive !== i2.passive, o2 = t2 !== q && (i2 === q || n2);
    n2 && this.element.removeEventListener(this.name, this, i2), o2 && this.element.addEventListener(this.name, this, t2), this._$AH = t2;
  }
  handleEvent(t2) {
    var e2, s2;
    "function" == typeof this._$AH ? this._$AH.call(null !== (s2 = null === (e2 = this.options) || void 0 === e2 ? void 0 : e2.host) && void 0 !== s2 ? s2 : this.element, t2) : this._$AH.handleEvent(t2);
  }
};
var et = class {
  constructor(t2, e2, s2) {
    this.element = t2, this.type = 6, this._$AN = void 0, this._$AM = e2, this.options = s2;
  }
  get _$AU() {
    return this._$AM._$AU;
  }
  _$AI(t2) {
    G(this, t2);
  }
};
var nt = g.litHtmlPolyfillSupport;
null == nt || nt(F, Q), (null !== (b = g.litHtmlVersions) && void 0 !== b ? b : g.litHtmlVersions = []).push("2.7.3");
var rt = (t2, e2, s2) => {
  var i2, n2;
  const o2 = null !== (i2 = null == s2 ? void 0 : s2.renderBefore) && void 0 !== i2 ? i2 : e2;
  let r2 = o2._$litPart$;
  if (void 0 === r2) {
    const t3 = null !== (n2 = null == s2 ? void 0 : s2.renderBefore) && void 0 !== n2 ? n2 : null;
    o2._$litPart$ = r2 = new Q(e2.insertBefore(A(), t3), t3, void 0, null != s2 ? s2 : {});
  }
  return r2._$AI(t2), r2;
};
var ht;
var lt;
var ut = class extends _ {
  constructor() {
    super(...arguments), this.renderOptions = { host: this }, this.st = void 0;
  }
  createRenderRoot() {
    var t2, e2;
    const s2 = super.createRenderRoot();
    return null !== (t2 = (e2 = this.renderOptions).renderBefore) && void 0 !== t2 || (e2.renderBefore = s2.firstChild), s2;
  }
  update(t2) {
    const e2 = this.render();
    this.hasUpdated || (this.renderOptions.isConnected = this.isConnected), super.update(t2), this.st = rt(e2, this.renderRoot, this.renderOptions);
  }
  connectedCallback() {
    var t2;
    super.connectedCallback(), null === (t2 = this.st) || void 0 === t2 || t2.setConnected(true);
  }
  disconnectedCallback() {
    var t2;
    super.disconnectedCallback(), null === (t2 = this.st) || void 0 === t2 || t2.setConnected(false);
  }
  render() {
    return D;
  }
};
ut.finalized = true, ut._$litElement$ = true, null === (ht = globalThis.litElementHydrateSupport) || void 0 === ht || ht.call(globalThis, { LitElement: ut });
var ct = globalThis.litElementPolyfillSupport;
null == ct || ct({ LitElement: ut });
(null !== (lt = globalThis.litElementVersions) && void 0 !== lt ? lt : globalThis.litElementVersions = []).push("3.3.2");

// src/features/settings/SettingsView.js
var SettingsView = class extends ut {
  static styles = r`
        * {
            font-family: 'Helvetica Neue', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            cursor: default;
            user-select: none;
        }

        :host {
            display: block;
            width: 240px;
            height: 100%;
            color: white;
        }

        .settings-container {
            display: flex;
            flex-direction: column;
            height: 100%;
            width: 100%;
            background: rgba(20, 20, 20, 0.8);
            border-radius: 12px;
            outline: 0.5px rgba(255, 255, 255, 0.2) solid;
            outline-offset: -1px;
            box-sizing: border-box;
            position: relative;
            overflow-y: auto;
            padding: 12px 12px;
            z-index: 1000;
        }

        .settings-container::-webkit-scrollbar {
            width: 6px;
        }

        .settings-container::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 3px;
        }

        .settings-container::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
        }

        .settings-container::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .settings-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.15);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border-radius: 12px;
            filter: blur(10px);
            z-index: -1;
        }
            
        .settings-button[disabled],
        .api-key-section input[disabled] {
            opacity: 0.4;
            cursor: not-allowed;
            pointer-events: none;
        }

        .header-section {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding-bottom: 6px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            z-index: 1;
        }

        .title-line {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .app-title {
            font-size: 13px;
            font-weight: 500;
            color: white;
            margin: 0 0 4px 0;
        }

        .account-info {
            font-size: 11px;
            color: rgba(255, 255, 255, 0.7);
            margin: 0;
        }

        .invisibility-icon {
            padding-top: 2px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .invisibility-icon.visible {
            opacity: 1;
        }

        .invisibility-icon svg {
            width: 16px;
            height: 16px;
        }

        .shortcuts-section {
            display: flex;
            flex-direction: column;
            gap: 2px;
            padding: 4px 0;
            position: relative;
            z-index: 1;
        }

        .shortcut-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
            color: white;
            font-size: 11px;
        }

        .shortcut-name {
            font-weight: 300;
        }

        .shortcut-keys {
            display: flex;
            align-items: center;
            gap: 3px;
        }

        .cmd-key, .shortcut-key {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
        }

        /* Buttons Section */
        .buttons-section {
            display: flex;
            flex-direction: column;
            gap: 4px;
            padding-top: 6px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            z-index: 1;
            flex: 1;
        }

        .settings-button {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            color: white;
            padding: 5px 10px;
            font-size: 11px;
            font-weight: 400;
            cursor: pointer;
            transition: all 0.15s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            white-space: nowrap;
        }

        .settings-button:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .settings-button:active {
            transform: translateY(1px);
        }

        .settings-button.full-width {
            width: 100%;
        }

        .settings-button.half-width {
            flex: 1;
        }

        .settings-button.danger {
            background: rgba(255, 59, 48, 0.1);
            border-color: rgba(255, 59, 48, 0.3);
            color: rgba(255, 59, 48, 0.9);
        }

        .settings-button.danger:hover {
            background: rgba(255, 59, 48, 0.15);
            border-color: rgba(255, 59, 48, 0.4);
        }

        .move-buttons, .bottom-buttons {
            display: flex;
            gap: 4px;
        }

        .api-key-section {
            padding: 6px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .api-key-section input {
            width: 100%;
            background: rgba(0,0,0,0.2);
            border: 1px solid rgba(255,255,255,0.2);
            color: white;
            border-radius: 4px;
            padding: 4px;
            font-size: 11px;
            margin-bottom: 4px;
            box-sizing: border-box;
        }

        .api-key-section input::placeholder {
            color: rgba(255, 255, 255, 0.4);
        }

        /* Preset Management Section */
        .preset-section {
            padding: 6px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .preset-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }

        .preset-title {
            font-size: 11px;
            font-weight: 500;
            color: white;
        }

        .preset-count {
            font-size: 9px;
            color: rgba(255, 255, 255, 0.5);
            margin-left: 4px;
        }

        .preset-toggle {
            font-size: 10px;
            color: rgba(255, 255, 255, 0.6);
            cursor: pointer;
            padding: 2px 4px;
            border-radius: 2px;
            transition: background-color 0.15s ease;
        }

        .preset-toggle:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .preset-list {
            display: flex;
            flex-direction: column;
            gap: 2px;
            max-height: 120px;
            overflow-y: auto;
        }

        .preset-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 6px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 3px;
            cursor: pointer;
            transition: all 0.15s ease;
            font-size: 11px;
            border: 1px solid transparent;
        }

        .preset-item:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.1);
        }

        .preset-item.selected {
            background: rgba(0, 122, 255, 0.25);
            border-color: rgba(0, 122, 255, 0.6);
            box-shadow: 0 0 0 1px rgba(0, 122, 255, 0.3);
        }

        .preset-name {
            color: white;
            flex: 1;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            font-weight: 300;
        }

        .preset-item.selected .preset-name {
            font-weight: 500;
        }

        .preset-status {
            font-size: 9px;
            color: rgba(0, 122, 255, 0.8);
            font-weight: 500;
            margin-left: 6px;
        }

        .no-presets-message {
            padding: 12px 8px;
            text-align: center;
            color: rgba(255, 255, 255, 0.5);
            font-size: 10px;
            line-height: 1.4;
        }

        .no-presets-message .web-link {
            color: rgba(0, 122, 255, 0.8);
            text-decoration: underline;
            cursor: pointer;
        }

        .no-presets-message .web-link:hover {
            color: rgba(0, 122, 255, 1);
        }

        .loading-state {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            color: rgba(255, 255, 255, 0.7);
            font-size: 11px;
        }

        .loading-spinner {
            width: 12px;
            height: 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-top: 1px solid rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 6px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }

        .api-key-section, .model-selection-section {
            padding: 8px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .provider-key-group, .model-select-group {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        label {
            font-size: 11px;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.8);
            margin-left: 2px;
        }
        label > strong {
            color: white;
            font-weight: 600;
        }
        .provider-key-group input {
            width: 100%; background: rgba(0,0,0,0.2); border: 1px solid rgba(255,255,255,0.2);
            color: white; border-radius: 4px; padding: 5px 8px; font-size: 11px; box-sizing: border-box;
        }
        .key-buttons { display: flex; gap: 4px; }
        .key-buttons .settings-button { flex: 1; padding: 4px; }
        .model-list {
            display: flex; flex-direction: column; gap: 2px; max-height: 120px;
            overflow-y: auto; background: rgba(0,0,0,0.3); border-radius: 4px;
            padding: 4px; margin-top: 4px;
        }
        .model-item { padding: 5px 8px; font-size: 11px; border-radius: 3px; cursor: pointer; transition: background-color 0.15s; }
        .model-item:hover { background-color: rgba(255,255,255,0.1); }
        .model-item.selected { background-color: rgba(0, 122, 255, 0.4); font-weight: 500; }
            
        /* ────────────────[ GLASS BYPASS ]─────────────── */
        :host-context(body.has-glass) {
            animation: none !important;
            transition: none !important;
            transform: none !important;
            will-change: auto !important;
        }

        :host-context(body.has-glass) * {
            background: transparent !important;
            filter: none !important;
            backdrop-filter: none !important;
            box-shadow: none !important;
            outline: none !important;
            border: none !important;
            border-radius: 0 !important;
            transition: none !important;
            animation: none !important;
        }

        :host-context(body.has-glass) .settings-container::before {
            display: none !important;
        }
    `;
  //////// after_modelStateService ////////
  static properties = {
    shortcuts: { type: Object, state: true },
    firebaseUser: { type: Object, state: true },
    isLoading: { type: Boolean, state: true },
    isContentProtectionOn: { type: Boolean, state: true },
    saving: { type: Boolean, state: true },
    providerConfig: { type: Object, state: true },
    apiKeys: { type: Object, state: true },
    availableLlmModels: { type: Array, state: true },
    availableSttModels: { type: Array, state: true },
    selectedLlm: { type: String, state: true },
    selectedStt: { type: String, state: true },
    isLlmListVisible: { type: Boolean },
    isSttListVisible: { type: Boolean },
    presets: { type: Array, state: true },
    selectedPreset: { type: Object, state: true },
    showPresets: { type: Boolean, state: true },
    autoUpdateEnabled: { type: Boolean, state: true },
    autoUpdateLoading: { type: Boolean, state: true }
  };
  //////// after_modelStateService ////////
  constructor() {
    super();
    this.shortcuts = {};
    this.firebaseUser = null;
    this.apiKeys = { openai: "", gemini: "", anthropic: "" };
    this.providerConfig = {};
    this.isLoading = true;
    this.isContentProtectionOn = true;
    this.saving = false;
    this.availableLlmModels = [];
    this.availableSttModels = [];
    this.selectedLlm = null;
    this.selectedStt = null;
    this.isLlmListVisible = false;
    this.isSttListVisible = false;
    this.presets = [];
    this.selectedPreset = null;
    this.showPresets = false;
    this.handleUsePicklesKey = this.handleUsePicklesKey.bind(this);
    this.autoUpdateEnabled = true;
    this.autoUpdateLoading = true;
    this.loadInitialData();
  }
  async loadAutoUpdateSetting() {
    if (!window.require) return;
    const { ipcRenderer: ipcRenderer2 } = window.require("electron");
    this.autoUpdateLoading = true;
    try {
      const enabled = await ipcRenderer2.invoke("settings:get-auto-update");
      this.autoUpdateEnabled = enabled;
      console.log("Auto-update setting loaded:", enabled);
    } catch (e2) {
      console.error("Error loading auto-update setting:", e2);
      this.autoUpdateEnabled = true;
    }
    this.autoUpdateLoading = false;
    this.requestUpdate();
  }
  async handleToggleAutoUpdate() {
    if (!window.require || this.autoUpdateLoading) return;
    const { ipcRenderer: ipcRenderer2 } = window.require("electron");
    this.autoUpdateLoading = true;
    this.requestUpdate();
    try {
      const newValue = !this.autoUpdateEnabled;
      const result = await ipcRenderer2.invoke("settings:set-auto-update", newValue);
      if (result && result.success) {
        this.autoUpdateEnabled = newValue;
      } else {
        console.error("Failed to update auto-update setting");
      }
    } catch (e2) {
      console.error("Error toggling auto-update:", e2);
    }
    this.autoUpdateLoading = false;
    this.requestUpdate();
  }
  //////// after_modelStateService ////////
  async loadInitialData() {
    if (!window.require) return;
    this.isLoading = true;
    const { ipcRenderer: ipcRenderer2 } = window.require("electron");
    try {
      const [userState, config, storedKeys, availableLlm, availableStt, selectedModels, presets, contentProtection, shortcuts] = await Promise.all([
        ipcRenderer2.invoke("get-current-user"),
        ipcRenderer2.invoke("model:get-provider-config"),
        // Provider 설정 로드
        ipcRenderer2.invoke("model:get-all-keys"),
        ipcRenderer2.invoke("model:get-available-models", { type: "llm" }),
        ipcRenderer2.invoke("model:get-available-models", { type: "stt" }),
        ipcRenderer2.invoke("model:get-selected-models"),
        ipcRenderer2.invoke("settings:getPresets"),
        ipcRenderer2.invoke("get-content-protection-status"),
        ipcRenderer2.invoke("get-current-shortcuts")
      ]);
      if (userState && userState.isLoggedIn) this.firebaseUser = userState;
      this.providerConfig = config;
      this.apiKeys = storedKeys;
      this.availableLlmModels = availableLlm;
      this.availableSttModels = availableStt;
      this.selectedLlm = selectedModels.llm;
      this.selectedStt = selectedModels.stt;
      this.presets = presets || [];
      this.isContentProtectionOn = contentProtection;
      this.shortcuts = shortcuts || {};
      if (this.presets.length > 0) {
        const firstUserPreset = this.presets.find((p2) => p2.is_default === 0);
        if (firstUserPreset) this.selectedPreset = firstUserPreset;
      }
    } catch (error) {
      console.error("Error loading initial settings data:", error);
    } finally {
      this.isLoading = false;
    }
  }
  async handleSaveKey(provider) {
    const input = this.shadowRoot.querySelector(`#key-input-${provider}`);
    if (!input) return;
    const key = input.value;
    this.saving = true;
    const { ipcRenderer: ipcRenderer2 } = window.require("electron");
    const result = await ipcRenderer2.invoke("model:validate-key", { provider, key });
    if (result.success) {
      this.apiKeys = { ...this.apiKeys, [provider]: key };
      await this.refreshModelData();
    } else {
      alert(`Failed to save ${provider} key: ${result.error}`);
      input.value = this.apiKeys[provider] || "";
    }
    this.saving = false;
  }
  async handleClearKey(provider) {
    this.saving = true;
    const { ipcRenderer: ipcRenderer2 } = window.require("electron");
    await ipcRenderer2.invoke("model:remove-api-key", { provider });
    this.apiKeys = { ...this.apiKeys, [provider]: "" };
    await this.refreshModelData();
    this.saving = false;
  }
  async refreshModelData() {
    const { ipcRenderer: ipcRenderer2 } = window.require("electron");
    const [availableLlm, availableStt, selected] = await Promise.all([
      ipcRenderer2.invoke("model:get-available-models", { type: "llm" }),
      ipcRenderer2.invoke("model:get-available-models", { type: "stt" }),
      ipcRenderer2.invoke("model:get-selected-models")
    ]);
    this.availableLlmModels = availableLlm;
    this.availableSttModels = availableStt;
    this.selectedLlm = selected.llm;
    this.selectedStt = selected.stt;
    this.requestUpdate();
  }
  async toggleModelList(type) {
    const visibilityProp = type === "llm" ? "isLlmListVisible" : "isSttListVisible";
    if (!this[visibilityProp]) {
      this.saving = true;
      this.requestUpdate();
      await this.refreshModelData();
      this.saving = false;
    }
    this[visibilityProp] = !this[visibilityProp];
    this.requestUpdate();
  }
  async selectModel(type, modelId) {
    this.saving = true;
    const { ipcRenderer: ipcRenderer2 } = window.require("electron");
    await ipcRenderer2.invoke("model:set-selected-model", { type, modelId });
    if (type === "llm") this.selectedLlm = modelId;
    if (type === "stt") this.selectedStt = modelId;
    this.isLlmListVisible = false;
    this.isSttListVisible = false;
    this.saving = false;
    this.requestUpdate();
  }
  handleUsePicklesKey(e2) {
    e2.preventDefault();
    if (this.wasJustDragged) return;
    console.log("Requesting Firebase authentication from main process...");
    if (window.require) {
      window.require("electron").ipcRenderer.invoke("start-firebase-auth");
    }
  }
  //////// after_modelStateService ////////
  openShortcutEditor() {
    if (window.require) {
      const { ipcRenderer: ipcRenderer2 } = window.require("electron");
      ipcRenderer2.invoke("open-shortcut-editor");
    }
  }
  connectedCallback() {
    super.connectedCallback();
    this.setupEventListeners();
    this.setupIpcListeners();
    this.setupWindowResize();
    this.loadAutoUpdateSetting();
  }
  disconnectedCallback() {
    super.disconnectedCallback();
    this.cleanupEventListeners();
    this.cleanupIpcListeners();
    this.cleanupWindowResize();
  }
  setupEventListeners() {
    this.addEventListener("mouseenter", this.handleMouseEnter);
    this.addEventListener("mouseleave", this.handleMouseLeave);
  }
  cleanupEventListeners() {
    this.removeEventListener("mouseenter", this.handleMouseEnter);
    this.removeEventListener("mouseleave", this.handleMouseLeave);
  }
  setupIpcListeners() {
    if (!window.require) return;
    const { ipcRenderer: ipcRenderer2 } = window.require("electron");
    this._userStateListener = (event, userState) => {
      console.log("[SettingsView] Received user-state-changed:", userState);
      if (userState && userState.isLoggedIn) {
        this.firebaseUser = userState;
      } else {
        this.firebaseUser = null;
      }
      this.loadAutoUpdateSetting();
      this.requestUpdate();
    };
    this._settingsUpdatedListener = (event, settings) => {
      console.log("[SettingsView] Received settings-updated");
      this.settings = settings;
      this.requestUpdate();
    };
    this._presetsUpdatedListener = async (event) => {
      console.log("[SettingsView] Received presets-updated, refreshing presets");
      try {
        const presets = await ipcRenderer2.invoke("settings:getPresets");
        this.presets = presets || [];
        const userPresets = this.presets.filter((p2) => p2.is_default === 0);
        if (this.selectedPreset && !userPresets.find((p2) => p2.id === this.selectedPreset.id)) {
          this.selectedPreset = userPresets.length > 0 ? userPresets[0] : null;
        }
        this.requestUpdate();
      } catch (error) {
        console.error("[SettingsView] Failed to refresh presets:", error);
      }
    };
    this._shortcutListener = (event, keybinds) => {
      console.log("[SettingsView] Received updated shortcuts:", keybinds);
      this.shortcuts = keybinds;
    };
    ipcRenderer2.on("user-state-changed", this._userStateListener);
    ipcRenderer2.on("settings-updated", this._settingsUpdatedListener);
    ipcRenderer2.on("presets-updated", this._presetsUpdatedListener);
    ipcRenderer2.on("shortcuts-updated", this._shortcutListener);
  }
  cleanupIpcListeners() {
    if (!window.require) return;
    const { ipcRenderer: ipcRenderer2 } = window.require("electron");
    if (this._userStateListener) {
      ipcRenderer2.removeListener("user-state-changed", this._userStateListener);
    }
    if (this._settingsUpdatedListener) {
      ipcRenderer2.removeListener("settings-updated", this._settingsUpdatedListener);
    }
    if (this._presetsUpdatedListener) {
      ipcRenderer2.removeListener("presets-updated", this._presetsUpdatedListener);
    }
    if (this._shortcutListener) {
      ipcRenderer2.removeListener("shortcuts-updated", this._shortcutListener);
    }
  }
  setupWindowResize() {
    this.resizeHandler = () => {
      this.requestUpdate();
      this.updateScrollHeight();
    };
    window.addEventListener("resize", this.resizeHandler);
    setTimeout(() => this.updateScrollHeight(), 100);
  }
  cleanupWindowResize() {
    if (this.resizeHandler) {
      window.removeEventListener("resize", this.resizeHandler);
    }
  }
  updateScrollHeight() {
    const windowHeight = window.innerHeight;
    const maxHeight = windowHeight;
    this.style.maxHeight = `${maxHeight}px`;
    const container = this.shadowRoot?.querySelector(".settings-container");
    if (container) {
      container.style.maxHeight = `${maxHeight}px`;
    }
  }
  handleMouseEnter = () => {
    if (window.require) {
      const { ipcRenderer: ipcRenderer2 } = window.require("electron");
      ipcRenderer2.send("cancel-hide-window", "settings");
    }
  };
  handleMouseLeave = () => {
    if (window.require) {
      const { ipcRenderer: ipcRenderer2 } = window.require("electron");
      ipcRenderer2.send("hide-window", "settings");
    }
  };
  // getMainShortcuts() {
  //     return [
  //         { name: 'Show / Hide', key: '\\' },
  //         { name: 'Ask Anything', key: '↵' },
  //         { name: 'Scroll AI Response', key: '↕' }
  //     ];
  // }
  getMainShortcuts() {
    return [
      { name: "Show / Hide", accelerator: this.shortcuts.toggleVisibility },
      { name: "Ask Anything", accelerator: this.shortcuts.nextStep },
      { name: "Scroll Up Response", accelerator: this.shortcuts.scrollUp },
      { name: "Scroll Down Response", accelerator: this.shortcuts.scrollDown }
    ];
  }
  renderShortcutKeys(accelerator) {
    if (!accelerator) return H`N/A`;
    const keyMap = {
      "Cmd": "\u2318",
      "Command": "\u2318",
      "Ctrl": "\u2303",
      "Alt": "\u2325",
      "Shift": "\u21E7",
      "Enter": "\u21B5",
      "Up": "\u2191",
      "Down": "\u2193",
      "Left": "\u2190",
      "Right": "\u2192"
    };
    if (accelerator.includes("\u2195")) {
      const keys2 = accelerator.replace("\u2195", "").split("+");
      keys2.push("\u2195");
      return H`${keys2.map((key) => H`<span class="shortcut-key">${keyMap[key] || key}</span>`)}`;
    }
    const keys = accelerator.split("+");
    return H`${keys.map((key) => H`<span class="shortcut-key">${keyMap[key] || key}</span>`)}`;
  }
  togglePresets() {
    this.showPresets = !this.showPresets;
  }
  async handlePresetSelect(preset) {
    this.selectedPreset = preset;
    console.log("Selected preset:", preset);
  }
  handleMoveLeft() {
    console.log("Move Left clicked");
    if (window.require) {
      const { ipcRenderer: ipcRenderer2 } = window.require("electron");
      ipcRenderer2.invoke("move-window-step", "left");
    }
  }
  handleMoveRight() {
    console.log("Move Right clicked");
    if (window.require) {
      const { ipcRenderer: ipcRenderer2 } = window.require("electron");
      ipcRenderer2.invoke("move-window-step", "right");
    }
  }
  async handlePersonalize() {
    console.log("Personalize clicked");
    if (window.require) {
      const { ipcRenderer: ipcRenderer2 } = window.require("electron");
      try {
        await ipcRenderer2.invoke("open-login-page");
      } catch (error) {
        console.error("Failed to open personalize page:", error);
      }
    }
  }
  async handleToggleInvisibility() {
    console.log("Toggle Invisibility clicked");
    if (window.require) {
      const { ipcRenderer: ipcRenderer2 } = window.require("electron");
      this.isContentProtectionOn = await ipcRenderer2.invoke("toggle-content-protection");
      this.requestUpdate();
    }
  }
  async handleSaveApiKey() {
    const input = this.shadowRoot.getElementById("api-key-input");
    if (!input || !input.value) return;
    const newApiKey = input.value;
    if (window.require) {
      const { ipcRenderer: ipcRenderer2 } = window.require("electron");
      try {
        const result = await ipcRenderer2.invoke("settings:saveApiKey", newApiKey);
        if (result.success) {
          console.log("API Key saved successfully via IPC.");
          this.apiKey = newApiKey;
          this.requestUpdate();
        } else {
          console.error("Failed to save API Key via IPC:", result.error);
        }
      } catch (e2) {
        console.error("Error invoking save-api-key IPC:", e2);
      }
    }
  }
  async handleClearApiKey() {
    console.log("Clear API Key clicked");
    if (window.require) {
      const { ipcRenderer: ipcRenderer2 } = window.require("electron");
      await ipcRenderer2.invoke("settings:removeApiKey");
      this.apiKey = null;
      this.requestUpdate();
    }
  }
  handleQuit() {
    console.log("Quit clicked");
    if (window.require) {
      const { ipcRenderer: ipcRenderer2 } = window.require("electron");
      ipcRenderer2.invoke("quit-application");
    }
  }
  handleFirebaseLogout() {
    console.log("Firebase Logout clicked");
    if (window.require) {
      const { ipcRenderer: ipcRenderer2 } = window.require("electron");
      ipcRenderer2.invoke("firebase-logout");
    }
  }
  //////// before_modelStateService ////////
  // render() {
  //     if (this.isLoading) {
  //         return html`
  //             <div class="settings-container">
  //                 <div class="loading-state">
  //                     <div class="loading-spinner"></div>
  //                     <span>Loading...</span>
  //                 </div>
  //             </div>
  //         `;
  //     }
  //     const loggedIn = !!this.firebaseUser;
  //     return html`
  //         <div class="settings-container">
  //             <div class="header-section">
  //                 <div>
  //                     <h1 class="app-title">Pickle Glass</h1>
  //                     <div class="account-info">
  //                         ${this.firebaseUser
  //                             ? html`Account: ${this.firebaseUser.email || 'Logged In'}`
  //                             : this.apiKey && this.apiKey.length > 10
  //                                 ? html`API Key: ${this.apiKey.substring(0, 6)}...${this.apiKey.substring(this.apiKey.length - 6)}`
  //                                 : `Account: Not Logged In`
  //                         }
  //                     </div>
  //                 </div>
  //                 <div class="invisibility-icon ${this.isContentProtectionOn ? 'visible' : ''}" title="Invisibility is On">
  //                     <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
  //                         <path d="M9.785 7.41787C8.7 7.41787 7.79 8.19371 7.55667 9.22621C7.0025 8.98704 6.495 9.05121 6.11 9.22037C5.87083 8.18204 4.96083 7.41787 3.88167 7.41787C2.61583 7.41787 1.58333 8.46204 1.58333 9.75121C1.58333 11.0404 2.61583 12.0845 3.88167 12.0845C5.08333 12.0845 6.06333 11.1395 6.15667 9.93787C6.355 9.79787 6.87417 9.53537 7.51 9.94954C7.615 11.1454 8.58333 12.0845 9.785 12.0845C11.0508 12.0845 12.0833 11.0404 12.0833 9.75121C12.0833 8.46204 11.0508 7.41787 9.785 7.41787ZM3.88167 11.4195C2.97167 11.4195 2.2425 10.6729 2.2425 9.75121C2.2425 8.82954 2.9775 8.08287 3.88167 8.08287C4.79167 8.08287 5.52083 8.82954 5.52083 9.75121C5.52083 10.6729 4.79167 11.4195 3.88167 11.4195ZM9.785 11.4195C8.875 11.4195 8.14583 10.6729 8.14583 9.75121C8.14583 8.82954 8.875 8.08287 9.785 8.08287C10.695 8.08287 11.43 8.82954 11.43 9.75121C11.43 10.6729 10.6892 11.4195 9.785 11.4195ZM12.6667 5.95954H1V6.83454H12.6667V5.95954ZM8.8925 1.36871C8.76417 1.08287 8.4375 0.931207 8.12833 1.03037L6.83333 1.46204L5.5325 1.03037L5.50333 1.02454C5.19417 0.93704 4.8675 1.10037 4.75083 1.39787L3.33333 5.08454H10.3333L8.91 1.39787L8.8925 1.36871Z" fill="white"/>
  //                     </svg>
  //                 </div>
  //             </div>
  //             <div class="api-key-section">
  //                 <input 
  //                     type="password" 
  //                     id="api-key-input"
  //                     placeholder="Enter API Key" 
  //                     .value=${this.apiKey || ''}
  //                     ?disabled=${loggedIn}
  //                 >
  //                 <button class="settings-button full-width" @click=${this.handleSaveApiKey} ?disabled=${loggedIn}>
  //                     Save API Key
  //                 </button>
  //             </div>
  //             <div class="shortcuts-section">
  //                 ${this.getMainShortcuts().map(shortcut => html`
  //                     <div class="shortcut-item">
  //                         <span class="shortcut-name">${shortcut.name}</span>
  //                         <div class="shortcut-keys">
  //                             <span class="cmd-key">⌘</span>
  //                             <span class="shortcut-key">${shortcut.key}</span>
  //                         </div>
  //                     </div>
  //                 `)}
  //             </div>
  //             <!-- Preset Management Section -->
  //             <div class="preset-section">
  //                 <div class="preset-header">
  //                     <span class="preset-title">
  //                         My Presets
  //                         <span class="preset-count">(${this.presets.filter(p => p.is_default === 0).length})</span>
  //                     </span>
  //                     <span class="preset-toggle" @click=${this.togglePresets}>
  //                         ${this.showPresets ? '▼' : '▶'}
  //                     </span>
  //                 </div>
  //                 <div class="preset-list ${this.showPresets ? '' : 'hidden'}">
  //                     ${this.presets.filter(p => p.is_default === 0).length === 0 ? html`
  //                         <div class="no-presets-message">
  //                             No custom presets yet.<br>
  //                             <span class="web-link" @click=${this.handlePersonalize}>
  //                                 Create your first preset
  //                             </span>
  //                         </div>
  //                     ` : this.presets.filter(p => p.is_default === 0).map(preset => html`
  //                         <div class="preset-item ${this.selectedPreset?.id === preset.id ? 'selected' : ''}"
  //                              @click=${() => this.handlePresetSelect(preset)}>
  //                             <span class="preset-name">${preset.title}</span>
  //                             ${this.selectedPreset?.id === preset.id ? html`<span class="preset-status">Selected</span>` : ''}
  //                         </div>
  //                     `)}
  //                 </div>
  //             </div>
  //             <div class="buttons-section">
  //                 <button class="settings-button full-width" @click=${this.handlePersonalize}>
  //                     <span>Personalize / Meeting Notes</span>
  //                 </button>
  //                 <div class="move-buttons">
  //                     <button class="settings-button half-width" @click=${this.handleMoveLeft}>
  //                         <span>← Move</span>
  //                     </button>
  //                     <button class="settings-button half-width" @click=${this.handleMoveRight}>
  //                         <span>Move →</span>
  //                     </button>
  //                 </div>
  //                 <button class="settings-button full-width" @click=${this.handleToggleInvisibility}>
  //                     <span>${this.isContentProtectionOn ? 'Disable Invisibility' : 'Enable Invisibility'}</span>
  //                 </button>
  //                 <div class="bottom-buttons">
  //                     ${this.firebaseUser
  //                         ? html`
  //                             <button class="settings-button half-width danger" @click=${this.handleFirebaseLogout}>
  //                                 <span>Logout</span>
  //                             </button>
  //                             `
  //                         : html`
  //                             <button class="settings-button half-width danger" @click=${this.handleClearApiKey}>
  //                                 <span>Clear API Key</span>
  //                             </button>
  //                             `
  //                     }
  //                     <button class="settings-button half-width danger" @click=${this.handleQuit}>
  //                         <span>Quit</span>
  //                     </button>
  //                 </div>
  //             </div>
  //         </div>
  //     `;
  // }
  //////// before_modelStateService ////////
  //////// after_modelStateService ////////
  render() {
    if (this.isLoading) {
      return H`
                <div class="settings-container">
                    <div class="loading-state">
                        <div class="loading-spinner"></div>
                        <span>Loading...</span>
                    </div>
                </div>
            `;
    }
    const loggedIn = !!this.firebaseUser;
    const apiKeyManagementHTML = H`
            <div class="api-key-section">
                ${Object.entries(this.providerConfig).filter(([id, config]) => !id.includes("-glass")).map(([id, config]) => H`
                        <div class="provider-key-group">
                            <label for="key-input-${id}">${config.name} API Key</label>
                            <input type="password" id="key-input-${id}"
                                placeholder=${loggedIn ? "Using Pickle's Key" : `Enter ${config.name} API Key`} 
                                .value=${this.apiKeys[id] || ""}
                                
                            >
                            <div class="key-buttons">
                               <button class="settings-button" @click=${() => this.handleSaveKey(id)} >Save</button>
                               <button class="settings-button danger" @click=${() => this.handleClearKey(id)} }>Clear</button>
                            </div>
                        </div>
                    `)}
            </div>
        `;
    const getModelName = (type, id) => {
      const models = type === "llm" ? this.availableLlmModels : this.availableSttModels;
      const model = models.find((m2) => m2.id === id);
      return model ? model.name : id;
    };
    const modelSelectionHTML = H`
            <div class="model-selection-section">
                <div class="model-select-group">
                    <label>LLM Model: <strong>${getModelName("llm", this.selectedLlm) || "Not Set"}</strong></label>
                    <button class="settings-button full-width" @click=${() => this.toggleModelList("llm")} ?disabled=${this.saving || this.availableLlmModels.length === 0}>
                        Change LLM Model
                    </button>
                    ${this.isLlmListVisible ? H`
                        <div class="model-list">
                            ${this.availableLlmModels.map((model) => H`
                                <div class="model-item ${this.selectedLlm === model.id ? "selected" : ""}" @click=${() => this.selectModel("llm", model.id)}>
                                    ${model.name}
                                </div>
                            `)}
                        </div>
                    ` : ""}
                </div>
                <div class="model-select-group">
                    <label>STT Model: <strong>${getModelName("stt", this.selectedStt) || "Not Set"}</strong></label>
                    <button class="settings-button full-width" @click=${() => this.toggleModelList("stt")} ?disabled=${this.saving || this.availableSttModels.length === 0}>
                        Change STT Model
                    </button>
                    ${this.isSttListVisible ? H`
                        <div class="model-list">
                            ${this.availableSttModels.map((model) => H`
                                <div class="model-item ${this.selectedStt === model.id ? "selected" : ""}" @click=${() => this.selectModel("stt", model.id)}>
                                    ${model.name}
                                </div>
                            `)}
                        </div>
                    ` : ""}
                </div>
            </div>
        `;
    return H`
            <div class="settings-container">
                <div class="header-section">
                    <div>
                        <h1 class="app-title">Pickle Glass</h1>
                        <div class="account-info">
                            ${this.firebaseUser ? H`Account: ${this.firebaseUser.email || "Logged In"}` : `Account: Not Logged In`}
                        </div>
                    </div>
                    <div class="invisibility-icon ${this.isContentProtectionOn ? "visible" : ""}" title="Invisibility is On">
                        <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9.785 7.41787C8.7 7.41787 7.79 8.19371 7.55667 9.22621C7.0025 8.98704 6.495 9.05121 6.11 9.22037C5.87083 8.18204 4.96083 7.41787 3.88167 7.41787C2.61583 7.41787 1.58333 8.46204 1.58333 9.75121C1.58333 11.0404 2.61583 12.0845 3.88167 12.0845C5.08333 12.0845 6.06333 11.1395 6.15667 9.93787C6.355 9.79787 6.87417 9.53537 7.51 9.94954C7.615 11.1454 8.58333 12.0845 9.785 12.0845C11.0508 12.0845 12.0833 11.0404 12.0833 9.75121C12.0833 8.46204 11.0508 7.41787 9.785 7.41787ZM3.88167 11.4195C2.97167 11.4195 2.2425 10.6729 2.2425 9.75121C2.2425 8.82954 2.9775 8.08287 3.88167 8.08287C4.79167 8.08287 5.52083 8.82954 5.52083 9.75121C5.52083 10.6729 4.79167 11.4195 3.88167 11.4195ZM9.785 11.4195C8.875 11.4195 8.14583 10.6729 8.14583 9.75121C8.14583 8.82954 8.875 8.08287 9.785 8.08287C10.695 8.08287 11.43 8.82954 11.43 9.75121C11.43 10.6729 10.6892 11.4195 9.785 11.4195ZM12.6667 5.95954H1V6.83454H12.6667V5.95954ZM8.8925 1.36871C8.76417 1.08287 8.4375 0.931207 8.12833 1.03037L6.83333 1.46204L5.5325 1.03037L5.50333 1.02454C5.19417 0.93704 4.8675 1.10037 4.75083 1.39787L3.33333 5.08454H10.3333L8.91 1.39787L8.8925 1.36871Z" fill="white"/>
                        </svg>
                    </div>
                </div>

                ${apiKeyManagementHTML}
                ${modelSelectionHTML}

                <div class="buttons-section" style="border-top: 1px solid rgba(255, 255, 255, 0.1); padding-top: 6px; margin-top: 6px;">
                    <button class="settings-button full-width" @click=${this.openShortcutEditor}>
                        Edit Shortcuts
                    </button>
                </div>

                
                <div class="shortcuts-section">
                    ${this.getMainShortcuts().map((shortcut) => H`
                        <div class="shortcut-item">
                            <span class="shortcut-name">${shortcut.name}</span>
                            <div class="shortcut-keys">
                                ${this.renderShortcutKeys(shortcut.accelerator)}
                            </div>
                        </div>
                    `)}
                </div>

                <div class="preset-section">
                    <div class="preset-header">
                        <span class="preset-title">
                            My Presets
                            <span class="preset-count">(${this.presets.filter((p2) => p2.is_default === 0).length})</span>
                        </span>
                        <span class="preset-toggle" @click=${this.togglePresets}>
                            ${this.showPresets ? "\u25BC" : "\u25B6"}
                        </span>
                    </div>
                    
                    <div class="preset-list ${this.showPresets ? "" : "hidden"}">
                        ${this.presets.filter((p2) => p2.is_default === 0).length === 0 ? H`
                            <div class="no-presets-message">
                                No custom presets yet.<br>
                                <span class="web-link" @click=${this.handlePersonalize}>
                                    Create your first preset
                                </span>
                            </div>
                        ` : this.presets.filter((p2) => p2.is_default === 0).map((preset) => H`
                            <div class="preset-item ${this.selectedPreset?.id === preset.id ? "selected" : ""}"
                                 @click=${() => this.handlePresetSelect(preset)}>
                                <span class="preset-name">${preset.title}</span>
                                ${this.selectedPreset?.id === preset.id ? H`<span class="preset-status">Selected</span>` : ""}
                            </div>
                        `)}
                    </div>
                </div>

                <div class="buttons-section">
                    <button class="settings-button full-width" @click=${this.handlePersonalize}>
                        <span>Personalize / Meeting Notes</span>
                    </button>
                    <button class="settings-button full-width" @click=${this.handleToggleAutoUpdate} ?disabled=${this.autoUpdateLoading}>
                        <span>Automatic Updates: ${this.autoUpdateEnabled ? "On" : "Off"}</span>
                    </button>
                    
                    <div class="move-buttons">
                        <button class="settings-button half-width" @click=${this.handleMoveLeft}>
                            <span>← Move</span>
                        </button>
                        <button class="settings-button half-width" @click=${this.handleMoveRight}>
                            <span>Move →</span>
                        </button>
                    </div>
                    
                    <button class="settings-button full-width" @click=${this.handleToggleInvisibility}>
                        <span>${this.isContentProtectionOn ? "Disable Invisibility" : "Enable Invisibility"}</span>
                    </button>
                    
                    <div class="bottom-buttons">
                        ${this.firebaseUser ? H`
                                <button class="settings-button half-width danger" @click=${this.handleFirebaseLogout}>
                                    <span>Logout</span>
                                </button>
                                ` : H`
                                <button class="settings-button half-width" @click=${this.handleUsePicklesKey}>
                                    <span>Login</span>
                                </button>
                                `}
                        <button class="settings-button half-width danger" @click=${this.handleQuit}>
                            <span>Quit</span>
                        </button>
                    </div>
                </div>
            </div>
        `;
  }
  //////// after_modelStateService ////////
};
customElements.define("settings-view", SettingsView);

// src/features/listen/stt/SttView.js
var SttView = class extends ut {
  static styles = r`
        :host {
            display: block;
            width: 100%;
        }

        /* Inherit font styles from parent */

        .transcription-container {
            overflow-y: auto;
            padding: 12px 12px 16px 12px;
            display: flex;
            flex-direction: column;
            gap: 8px;
            min-height: 150px;
            max-height: 600px;
            position: relative;
            z-index: 1;
            flex: 1;
        }

        /* Visibility handled by parent component */

        .transcription-container::-webkit-scrollbar {
            width: 8px;
        }
        .transcription-container::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 4px;
        }
        .transcription-container::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
        }
        .transcription-container::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        .stt-message {
            padding: 8px 12px;
            border-radius: 12px;
            max-width: 80%;
            word-wrap: break-word;
            word-break: break-word;
            line-height: 1.5;
            font-size: 13px;
            margin-bottom: 4px;
            box-sizing: border-box;
        }

        .stt-message.them {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.9);
            align-self: flex-start;
            border-bottom-left-radius: 4px;
            margin-right: auto;
        }

        .stt-message.me {
            background: rgba(0, 122, 255, 0.8);
            color: white;
            align-self: flex-end;
            border-bottom-right-radius: 4px;
            margin-left: auto;
        }

        .empty-state {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100px;
            color: rgba(255, 255, 255, 0.6);
            font-size: 12px;
            font-style: italic;
        }
    `;
  static properties = {
    sttMessages: { type: Array },
    isVisible: { type: Boolean }
  };
  constructor() {
    super();
    this.sttMessages = [];
    this.isVisible = true;
    this.messageIdCounter = 0;
    this._shouldScrollAfterUpdate = false;
    this.handleSttUpdate = this.handleSttUpdate.bind(this);
  }
  connectedCallback() {
    super.connectedCallback();
    if (window.require) {
      const { ipcRenderer: ipcRenderer2 } = window.require("electron");
      ipcRenderer2.on("stt-update", this.handleSttUpdate);
    }
  }
  disconnectedCallback() {
    super.disconnectedCallback();
    if (window.require) {
      const { ipcRenderer: ipcRenderer2 } = window.require("electron");
      ipcRenderer2.removeListener("stt-update", this.handleSttUpdate);
    }
  }
  // Handle session reset from parent
  resetTranscript() {
    this.sttMessages = [];
    this.requestUpdate();
  }
  handleSttUpdate(event, { speaker, text, isFinal, isPartial }) {
    if (text === void 0) return;
    const container = this.shadowRoot.querySelector(".transcription-container");
    this._shouldScrollAfterUpdate = container ? container.scrollTop + container.clientHeight >= container.scrollHeight - 10 : false;
    const findLastPartialIdx = (spk) => {
      for (let i2 = this.sttMessages.length - 1; i2 >= 0; i2--) {
        const m2 = this.sttMessages[i2];
        if (m2.speaker === spk && m2.isPartial) return i2;
      }
      return -1;
    };
    const newMessages = [...this.sttMessages];
    const targetIdx = findLastPartialIdx(speaker);
    if (isPartial) {
      if (targetIdx !== -1) {
        newMessages[targetIdx] = {
          ...newMessages[targetIdx],
          text,
          isPartial: true,
          isFinal: false
        };
      } else {
        newMessages.push({
          id: this.messageIdCounter++,
          speaker,
          text,
          isPartial: true,
          isFinal: false
        });
      }
    } else if (isFinal) {
      if (targetIdx !== -1) {
        newMessages[targetIdx] = {
          ...newMessages[targetIdx],
          text,
          isPartial: false,
          isFinal: true
        };
      } else {
        newMessages.push({
          id: this.messageIdCounter++,
          speaker,
          text,
          isPartial: false,
          isFinal: true
        });
      }
    }
    this.sttMessages = newMessages;
    this.dispatchEvent(new CustomEvent("stt-messages-updated", {
      detail: { messages: this.sttMessages },
      bubbles: true
    }));
  }
  scrollToBottom() {
    setTimeout(() => {
      const container = this.shadowRoot.querySelector(".transcription-container");
      if (container) {
        container.scrollTop = container.scrollHeight;
      }
    }, 0);
  }
  getSpeakerClass(speaker) {
    return speaker.toLowerCase() === "me" ? "me" : "them";
  }
  getTranscriptText() {
    return this.sttMessages.map((msg) => `${msg.speaker}: ${msg.text}`).join("\n");
  }
  updated(changedProperties) {
    super.updated(changedProperties);
    if (changedProperties.has("sttMessages")) {
      if (this._shouldScrollAfterUpdate) {
        this.scrollToBottom();
        this._shouldScrollAfterUpdate = false;
      }
    }
  }
  render() {
    if (!this.isVisible) {
      return H`<div style="display: none;"></div>`;
    }
    return H`
            <div class="transcription-container">
                ${this.sttMessages.length === 0 ? H`<div class="empty-state">Waiting for speech...</div>` : this.sttMessages.map((msg) => H`
                        <div class="stt-message ${this.getSpeakerClass(msg.speaker)}">
                            ${msg.text}
                        </div>
                    `)}
            </div>
        `;
  }
};
customElements.define("stt-view", SttView);

// src/features/listen/summary/SummaryView.js
var SummaryView = class extends ut {
  static styles = r`
        :host {
            display: block;
            width: 100%;
        }

        /* Inherit font styles from parent */

        /* highlight.js 스타일 추가 */
        .insights-container pre {
            background: rgba(0, 0, 0, 0.4) !important;
            border-radius: 8px !important;
            padding: 12px !important;
            margin: 8px 0 !important;
            overflow-x: auto !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
            white-space: pre !important;
            word-wrap: normal !important;
            word-break: normal !important;
        }

        .insights-container code {
            font-family: 'Monaco', 'Menlo', 'Consolas', monospace !important;
            font-size: 11px !important;
            background: transparent !important;
            white-space: pre !important;
            word-wrap: normal !important;
            word-break: normal !important;
        }

        .insights-container pre code {
            white-space: pre !important;
            word-wrap: normal !important;
            word-break: normal !important;
            display: block !important;
        }

        .insights-container p code {
            background: rgba(255, 255, 255, 0.1) !important;
            padding: 2px 4px !important;
            border-radius: 3px !important;
            color: #ffd700 !important;
        }

        .hljs-keyword {
            color: #ff79c6 !important;
        }
        .hljs-string {
            color: #f1fa8c !important;
        }
        .hljs-comment {
            color: #6272a4 !important;
        }
        .hljs-number {
            color: #bd93f9 !important;
        }
        .hljs-function {
            color: #50fa7b !important;
        }
        .hljs-variable {
            color: #8be9fd !important;
        }
        .hljs-built_in {
            color: #ffb86c !important;
        }
        .hljs-title {
            color: #50fa7b !important;
        }
        .hljs-attr {
            color: #50fa7b !important;
        }
        .hljs-tag {
            color: #ff79c6 !important;
        }

        .insights-container {
            overflow-y: auto;
            padding: 12px 16px 16px 16px;
            position: relative;
            z-index: 1;
            min-height: 150px;
            max-height: 600px;
            flex: 1;
        }

        /* Visibility handled by parent component */

        .insights-container::-webkit-scrollbar {
            width: 8px;
        }
        .insights-container::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 4px;
        }
        .insights-container::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
        }
        .insights-container::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        insights-title {
            color: rgba(255, 255, 255, 0.8);
            font-size: 15px;
            font-weight: 500;
            font-family: 'Helvetica Neue', sans-serif;
            margin: 12px 0 8px 0;
            display: block;
        }

        .insights-container h4 {
            color: #ffffff;
            font-size: 12px;
            font-weight: 600;
            margin: 12px 0 8px 0;
            padding: 4px 8px;
            border-radius: 4px;
            background: transparent;
            cursor: default;
        }

        .insights-container h4:hover {
            background: transparent;
        }

        .insights-container h4:first-child {
            margin-top: 0;
        }

        .outline-item {
            color: #ffffff;
            font-size: 11px;
            line-height: 1.4;
            margin: 4px 0;
            padding: 6px 8px;
            border-radius: 4px;
            background: transparent;
            transition: background-color 0.15s ease;
            cursor: pointer;
            word-wrap: break-word;
        }

        .outline-item:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .request-item {
            color: #ffffff;
            font-size: 12px;
            line-height: 1.2;
            margin: 4px 0;
            padding: 6px 8px;
            border-radius: 4px;
            background: transparent;
            cursor: default;
            word-wrap: break-word;
            transition: background-color 0.15s ease;
        }

        .request-item.clickable {
            cursor: pointer;
            transition: all 0.15s ease;
        }
        .request-item.clickable:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(2px);
        }

        /* 마크다운 렌더링된 콘텐츠 스타일 */
        .markdown-content {
            color: #ffffff;
            font-size: 11px;
            line-height: 1.4;
            margin: 4px 0;
            padding: 6px 8px;
            border-radius: 4px;
            background: transparent;
            cursor: pointer;
            word-wrap: break-word;
            transition: all 0.15s ease;
        }

        .markdown-content:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(2px);
        }

        .markdown-content p {
            margin: 4px 0;
        }

        .markdown-content ul,
        .markdown-content ol {
            margin: 4px 0;
            padding-left: 16px;
        }

        .markdown-content li {
            margin: 2px 0;
        }

        .markdown-content a {
            color: #8be9fd;
            text-decoration: none;
        }

        .markdown-content a:hover {
            text-decoration: underline;
        }

        .markdown-content strong {
            font-weight: 600;
            color: #f8f8f2;
        }

        .markdown-content em {
            font-style: italic;
            color: #f1fa8c;
        }

        .empty-state {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100px;
            color: rgba(255, 255, 255, 0.6);
            font-size: 12px;
            font-style: italic;
        }
    `;
  static properties = {
    structuredData: { type: Object },
    isVisible: { type: Boolean },
    hasCompletedRecording: { type: Boolean }
  };
  constructor() {
    super();
    this.structuredData = {
      summary: [],
      topic: { header: "", bullets: [] },
      actions: [],
      followUps: []
    };
    this.isVisible = true;
    this.hasCompletedRecording = false;
    this.marked = null;
    this.hljs = null;
    this.isLibrariesLoaded = false;
    this.DOMPurify = null;
    this.isDOMPurifyLoaded = false;
    this.loadLibraries();
  }
  connectedCallback() {
    super.connectedCallback();
    if (window.require) {
      const { ipcRenderer: ipcRenderer2 } = window.require("electron");
      ipcRenderer2.on("update-structured-data", (event, data) => {
        this.structuredData = data;
        this.requestUpdate();
      });
    }
  }
  disconnectedCallback() {
    super.disconnectedCallback();
    if (window.require) {
      const { ipcRenderer: ipcRenderer2 } = window.require("electron");
      ipcRenderer2.removeAllListeners("update-structured-data");
    }
  }
  // Handle session reset from parent
  resetAnalysis() {
    this.structuredData = {
      summary: [],
      topic: { header: "", bullets: [] },
      actions: [],
      followUps: []
    };
    this.requestUpdate();
  }
  async loadLibraries() {
    try {
      if (!window.marked) {
        await this.loadScript("../../../assets/marked-4.3.0.min.js");
      }
      if (!window.hljs) {
        await this.loadScript("../../../assets/highlight-11.9.0.min.js");
      }
      if (!window.DOMPurify) {
        await this.loadScript("../../../assets/dompurify-3.0.7.min.js");
      }
      this.marked = window.marked;
      this.hljs = window.hljs;
      this.DOMPurify = window.DOMPurify;
      if (this.marked && this.hljs) {
        this.marked.setOptions({
          highlight: (code, lang) => {
            if (lang && this.hljs.getLanguage(lang)) {
              try {
                return this.hljs.highlight(code, { language: lang }).value;
              } catch (err) {
                console.warn("Highlight error:", err);
              }
            }
            try {
              return this.hljs.highlightAuto(code).value;
            } catch (err) {
              console.warn("Auto highlight error:", err);
            }
            return code;
          },
          breaks: true,
          gfm: true,
          pedantic: false,
          smartypants: false,
          xhtml: false
        });
        this.isLibrariesLoaded = true;
        console.log("Markdown libraries loaded successfully");
      }
      if (this.DOMPurify) {
        this.isDOMPurifyLoaded = true;
        console.log("DOMPurify loaded successfully in SummaryView");
      }
    } catch (error) {
      console.error("Failed to load libraries:", error);
    }
  }
  loadScript(src) {
    return new Promise((resolve, reject) => {
      const script = document.createElement("script");
      script.src = src;
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });
  }
  parseMarkdown(text) {
    if (!text) return "";
    if (!this.isLibrariesLoaded || !this.marked) {
      return text;
    }
    try {
      return this.marked(text);
    } catch (error) {
      console.error("Markdown parsing error:", error);
      return text;
    }
  }
  handleMarkdownClick(originalText) {
    this.handleRequestClick(originalText);
  }
  renderMarkdownContent() {
    if (!this.isLibrariesLoaded || !this.marked) {
      return;
    }
    const markdownElements = this.shadowRoot.querySelectorAll("[data-markdown-id]");
    markdownElements.forEach((element) => {
      const originalText = element.getAttribute("data-original-text");
      if (originalText) {
        try {
          let parsedHTML = this.parseMarkdown(originalText);
          if (this.isDOMPurifyLoaded && this.DOMPurify) {
            parsedHTML = this.DOMPurify.sanitize(parsedHTML);
            if (this.DOMPurify.removed && this.DOMPurify.removed.length > 0) {
              console.warn("Unsafe content detected in insights, showing plain text");
              element.textContent = "\u26A0\uFE0F " + originalText;
              return;
            }
          }
          element.innerHTML = parsedHTML;
        } catch (error) {
          console.error("Error rendering markdown for element:", error);
          element.textContent = originalText;
        }
      }
    });
  }
  async handleRequestClick(requestText) {
    console.log("\u{1F525} Analysis request clicked:", requestText);
    if (window.require) {
      const { ipcRenderer: ipcRenderer2 } = window.require("electron");
      try {
        const isAskViewVisible = await ipcRenderer2.invoke("is-window-visible", "ask");
        if (!isAskViewVisible) {
          await ipcRenderer2.invoke("toggle-feature", "ask");
          await new Promise((resolve) => setTimeout(resolve, 100));
        }
        const result = await ipcRenderer2.invoke("send-question-to-ask", requestText);
        if (result.success) {
          console.log("\u2705 Question sent to AskView successfully");
        } else {
          console.error("\u274C Failed to send question to AskView:", result.error);
        }
      } catch (error) {
        console.error("\u274C Error in handleRequestClick:", error);
      }
    }
  }
  getSummaryText() {
    const data = this.structuredData || { summary: [], topic: { header: "", bullets: [] }, actions: [] };
    let sections = [];
    if (data.summary && data.summary.length > 0) {
      sections.push(`Current Summary:
${data.summary.map((s2) => `\u2022 ${s2}`).join("\n")}`);
    }
    if (data.topic && data.topic.header && data.topic.bullets.length > 0) {
      sections.push(`
${data.topic.header}:
${data.topic.bullets.map((b2) => `\u2022 ${b2}`).join("\n")}`);
    }
    if (data.actions && data.actions.length > 0) {
      sections.push(`
Actions:
${data.actions.map((a2) => `\u25B8 ${a2}`).join("\n")}`);
    }
    if (data.followUps && data.followUps.length > 0) {
      sections.push(`
Follow-Ups:
${data.followUps.map((f2) => `\u25B8 ${f2}`).join("\n")}`);
    }
    return sections.join("\n\n").trim();
  }
  updated(changedProperties) {
    super.updated(changedProperties);
    this.renderMarkdownContent();
  }
  render() {
    if (!this.isVisible) {
      return H`<div style="display: none;"></div>`;
    }
    const data = this.structuredData || {
      summary: [],
      topic: { header: "", bullets: [] },
      actions: []
    };
    const hasAnyContent = data.summary.length > 0 || data.topic.bullets.length > 0 || data.actions.length > 0;
    return H`
            <div class="insights-container">
                ${!hasAnyContent ? H`<div class="empty-state">No insights yet...</div>` : H`
                        <insights-title>Current Summary</insights-title>
                        ${data.summary.length > 0 ? data.summary.slice(0, 5).map(
      (bullet, index) => H`
                                          <div
                                              class="markdown-content"
                                              data-markdown-id="summary-${index}"
                                              data-original-text="${bullet}"
                                              @click=${() => this.handleMarkdownClick(bullet)}
                                          >
                                              ${bullet}
                                          </div>
                                      `
    ) : H` <div class="request-item">No content yet...</div> `}
                        ${data.topic.header ? H`
                                  <insights-title>${data.topic.header}</insights-title>
                                  ${data.topic.bullets.slice(0, 3).map(
      (bullet, index) => H`
                                              <div
                                                  class="markdown-content"
                                                  data-markdown-id="topic-${index}"
                                                  data-original-text="${bullet}"
                                                  @click=${() => this.handleMarkdownClick(bullet)}
                                              >
                                                  ${bullet}
                                              </div>
                                          `
    )}
                              ` : ""}
                        ${data.actions.length > 0 ? H`
                                  <insights-title>Actions</insights-title>
                                  ${data.actions.slice(0, 5).map(
      (action, index) => H`
                                              <div
                                                  class="markdown-content"
                                                  data-markdown-id="action-${index}"
                                                  data-original-text="${action}"
                                                  @click=${() => this.handleMarkdownClick(action)}
                                              >
                                                  ${action}
                                              </div>
                                          `
    )}
                              ` : ""}
                        ${this.hasCompletedRecording && data.followUps && data.followUps.length > 0 ? H`
                                  <insights-title>Follow-Ups</insights-title>
                                  ${data.followUps.map(
      (followUp, index) => H`
                                          <div
                                              class="markdown-content"
                                              data-markdown-id="followup-${index}"
                                              data-original-text="${followUp}"
                                              @click=${() => this.handleMarkdownClick(followUp)}
                                          >
                                              ${followUp}
                                          </div>
                                      `
    )}
                              ` : ""}
                    `}
            </div>
        `;
  }
};
customElements.define("summary-view", SummaryView);

// src/features/listen/AssistantView.js
var AssistantView = class extends ut {
  static styles = r`
        :host {
            display: block;
            width: 400px;
            transform: translate3d(0, 0, 0);
            backface-visibility: hidden;
            transition: transform 0.2s cubic-bezier(0.23, 1, 0.32, 1), opacity 0.2s ease-out;
            will-change: transform, opacity;
        }

        :host(.hiding) {
            animation: slideUp 0.3s cubic-bezier(0.4, 0, 0.6, 1) forwards;
        }

        :host(.showing) {
            animation: slideDown 0.35s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
        }

        :host(.hidden) {
            opacity: 0;
            transform: translateY(-150%) scale(0.85);
            pointer-events: none;
        }

        @keyframes slideUp {
            0% {
                opacity: 1;
                transform: translateY(0) scale(1);
                filter: blur(0px);
            }
            30% {
                opacity: 0.7;
                transform: translateY(-20%) scale(0.98);
                filter: blur(0.5px);
            }
            70% {
                opacity: 0.3;
                transform: translateY(-80%) scale(0.92);
                filter: blur(1.5px);
            }
            100% {
                opacity: 0;
                transform: translateY(-150%) scale(0.85);
                filter: blur(2px);
            }
        }

        @keyframes slideDown {
            0% {
                opacity: 0;
                transform: translateY(-150%) scale(0.85);
                filter: blur(2px);
            }
            30% {
                opacity: 0.5;
                transform: translateY(-50%) scale(0.92);
                filter: blur(1px);
            }
            65% {
                opacity: 0.9;
                transform: translateY(-5%) scale(0.99);
                filter: blur(0.2px);
            }
            85% {
                opacity: 0.98;
                transform: translateY(2%) scale(1.005);
                filter: blur(0px);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
                filter: blur(0px);
            }
        }

        * {
            font-family: 'Helvetica Neue', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            cursor: default;
            user-select: none;
        }

/* Allow text selection in insights responses */
.insights-container, .insights-container *, .markdown-content {
    user-select: text !important;
    cursor: text !important;
}

/* highlight.js 스타일 추가 */
.insights-container pre {
    background: rgba(0, 0, 0, 0.4) !important;
    border-radius: 8px !important;
    padding: 12px !important;
    margin: 8px 0 !important;
    overflow-x: auto !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    white-space: pre !important;
    word-wrap: normal !important;
    word-break: normal !important;
}

.insights-container code {
    font-family: 'Monaco', 'Menlo', 'Consolas', monospace !important;
    font-size: 11px !important;
    background: transparent !important;
    white-space: pre !important;
    word-wrap: normal !important;
    word-break: normal !important;
}

.insights-container pre code {
    white-space: pre !important;
    word-wrap: normal !important;
    word-break: normal !important;
    display: block !important;
}

.insights-container p code {
    background: rgba(255, 255, 255, 0.1) !important;
    padding: 2px 4px !important;
    border-radius: 3px !important;
    color: #ffd700 !important;
}

.hljs-keyword {
    color: #ff79c6 !important;
}

.hljs-string {
    color: #f1fa8c !important;
}

.hljs-comment {
    color: #6272a4 !important;
}

.hljs-number {
    color: #bd93f9 !important;
}

.hljs-function {
    color: #50fa7b !important;
}

.hljs-title {
    color: #50fa7b !important;
}

.hljs-variable {
    color: #8be9fd !important;
}

.hljs-built_in {
    color: #ffb86c !important;
}

.hljs-attr {
    color: #50fa7b !important;
}

.hljs-tag {
    color: #ff79c6 !important;
}
        .assistant-container {
            display: flex;
            flex-direction: column;
            color: #ffffff;
            box-sizing: border-box;
            position: relative;
            background: rgba(0, 0, 0, 0.6);
            overflow: hidden;
            border-radius: 12px;
            width: 100%;
            height: 100%;
        }

        .assistant-container::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 12px;
            padding: 1px;
            background: linear-gradient(169deg, rgba(255, 255, 255, 0.17) 0%, rgba(255, 255, 255, 0.08) 50%, rgba(255, 255, 255, 0.17) 100%);
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: destination-out;
            mask-composite: exclude;
            pointer-events: none;
        }

        .assistant-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.15);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border-radius: 12px;
            z-index: -1;
        }

        .top-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px 16px;
            min-height: 32px;
            position: relative;
            z-index: 1;
            width: 100%;
            box-sizing: border-box;
            flex-shrink: 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .bar-left-text {
            color: white;
            font-size: 13px;
            font-family: 'Helvetica Neue', sans-serif;
            font-weight: 500;
            position: relative;
            overflow: hidden;
            white-space: nowrap;
            flex: 1;
            min-width: 0;
            max-width: 200px;
        }

        .bar-left-text-content {
            display: inline-block;
            transition: transform 0.3s ease;
        }

        .bar-left-text-content.slide-in {
            animation: slideIn 0.3s ease forwards;
        }

        @keyframes slideIn {
            from {
                transform: translateX(10%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .bar-controls {
            display: flex;
            gap: 4px;
            align-items: center;
            flex-shrink: 0;
            width: 120px;
            justify-content: flex-end;
            box-sizing: border-box;
            padding: 4px;
        }

        .toggle-button {
            display: flex;
            align-items: center;
            gap: 5px;
            background: transparent;
            color: rgba(255, 255, 255, 0.9);
            border: none;
            outline: none;
            box-shadow: none;
            padding: 4px 8px;
            border-radius: 5px;
            font-size: 11px;
            font-weight: 500;
            cursor: pointer;
            height: 24px;
            white-space: nowrap;
            transition: background-color 0.15s ease;
            justify-content: center;
        }

        .toggle-button:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .toggle-button svg {
            flex-shrink: 0;
            width: 12px;
            height: 12px;
        }

        .copy-button {
            background: transparent;
            color: rgba(255, 255, 255, 0.9);
            border: none;
            outline: none;
            box-shadow: none;
            padding: 4px;
            border-radius: 3px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 24px;
            height: 24px;
            flex-shrink: 0;
            transition: background-color 0.15s ease;
            position: relative;
            overflow: hidden;
        }

        .copy-button:hover {
            background: rgba(255, 255, 255, 0.15);
        }

        .copy-button svg {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
        }

        .copy-button .check-icon {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.5);
        }

        .copy-button.copied .copy-icon {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.5);
        }

        .copy-button.copied .check-icon {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
        }

        .timer {
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 10px;
            color: rgba(255, 255, 255, 0.7);
        }
        
        /* ────────────────[ GLASS BYPASS ]─────────────── */
        :host-context(body.has-glass) .assistant-container,
        :host-context(body.has-glass) .top-bar,
        :host-context(body.has-glass) .toggle-button,
        :host-context(body.has-glass) .copy-button,
        :host-context(body.has-glass) .transcription-container,
        :host-context(body.has-glass) .insights-container,
        :host-context(body.has-glass) .stt-message,
        :host-context(body.has-glass) .outline-item,
        :host-context(body.has-glass) .request-item,
        :host-context(body.has-glass) .markdown-content,
        :host-context(body.has-glass) .insights-container pre,
        :host-context(body.has-glass) .insights-container p code,
        :host-context(body.has-glass) .insights-container pre code {
            background: transparent !important;
            border: none !important;
            outline: none !important;
            box-shadow: none !important;
            filter: none !important;
            backdrop-filter: none !important;
        }

        :host-context(body.has-glass) .assistant-container::before,
        :host-context(body.has-glass) .assistant-container::after {
            display: none !important;
        }

        :host-context(body.has-glass) .toggle-button:hover,
        :host-context(body.has-glass) .copy-button:hover,
        :host-context(body.has-glass) .outline-item:hover,
        :host-context(body.has-glass) .request-item.clickable:hover,
        :host-context(body.has-glass) .markdown-content:hover {
            background: transparent !important;
            transform: none !important;
        }

        :host-context(body.has-glass) .transcription-container::-webkit-scrollbar-track,
        :host-context(body.has-glass) .transcription-container::-webkit-scrollbar-thumb,
        :host-context(body.has-glass) .insights-container::-webkit-scrollbar-track,
        :host-context(body.has-glass) .insights-container::-webkit-scrollbar-thumb {
            background: transparent !important;
        }
        :host-context(body.has-glass) * {
            animation: none !important;
            transition: none !important;
            transform: none !important;
            filter: none !important;
            backdrop-filter: none !important;
            box-shadow: none !important;
        }

        :host-context(body.has-glass) .assistant-container,
        :host-context(body.has-glass) .stt-message,
        :host-context(body.has-glass) .toggle-button,
        :host-context(body.has-glass) .copy-button {
            border-radius: 0 !important;
        }

        :host-context(body.has-glass) ::-webkit-scrollbar,
        :host-context(body.has-glass) ::-webkit-scrollbar-track,
        :host-context(body.has-glass) ::-webkit-scrollbar-thumb {
            background: transparent !important;
            width: 0 !important;      /* 스크롤바 자체 숨기기 */
        }
        :host-context(body.has-glass) .assistant-container,
        :host-context(body.has-glass) .top-bar,
        :host-context(body.has-glass) .toggle-button,
        :host-context(body.has-glass) .copy-button,
        :host-context(body.has-glass) .transcription-container,
        :host-context(body.has-glass) .insights-container,
        :host-context(body.has-glass) .stt-message,
        :host-context(body.has-glass) .outline-item,
        :host-context(body.has-glass) .request-item,
        :host-context(body.has-glass) .markdown-content,
        :host-context(body.has-glass) .insights-container pre,
        :host-context(body.has-glass) .insights-container p code,
        :host-context(body.has-glass) .insights-container pre code {
            background: transparent !important;
            border: none !important;
            outline: none !important;
            box-shadow: none !important;
            filter: none !important;
            backdrop-filter: none !important;
        }

        :host-context(body.has-glass) .assistant-container::before,
        :host-context(body.has-glass) .assistant-container::after {
            display: none !important;
        }

        :host-context(body.has-glass) .toggle-button:hover,
        :host-context(body.has-glass) .copy-button:hover,
        :host-context(body.has-glass) .outline-item:hover,
        :host-context(body.has-glass) .request-item.clickable:hover,
        :host-context(body.has-glass) .markdown-content:hover {
            background: transparent !important;
            transform: none !important;
        }

        :host-context(body.has-glass) .transcription-container::-webkit-scrollbar-track,
        :host-context(body.has-glass) .transcription-container::-webkit-scrollbar-thumb,
        :host-context(body.has-glass) .insights-container::-webkit-scrollbar-track,
        :host-context(body.has-glass) .insights-container::-webkit-scrollbar-thumb {
            background: transparent !important;
        }
        :host-context(body.has-glass) * {
            animation: none !important;
            transition: none !important;
            transform: none !important;
            filter: none !important;
            backdrop-filter: none !important;
            box-shadow: none !important;
        }

        :host-context(body.has-glass) .assistant-container,
        :host-context(body.has-glass) .stt-message,
        :host-context(body.has-glass) .toggle-button,
        :host-context(body.has-glass) .copy-button {
            border-radius: 0 !important;
        }

        :host-context(body.has-glass) ::-webkit-scrollbar,
        :host-context(body.has-glass) ::-webkit-scrollbar-track,
        :host-context(body.has-glass) ::-webkit-scrollbar-thumb {
            background: transparent !important;
            width: 0 !important;
        }
    `;
  static properties = {
    viewMode: { type: String },
    isHovering: { type: Boolean },
    isAnimating: { type: Boolean },
    copyState: { type: String },
    elapsedTime: { type: String },
    captureStartTime: { type: Number },
    isSessionActive: { type: Boolean },
    hasCompletedRecording: { type: Boolean }
  };
  constructor() {
    super();
    this.isSessionActive = false;
    this.hasCompletedRecording = false;
    this.viewMode = "insights";
    this.isHovering = false;
    this.isAnimating = false;
    this.elapsedTime = "00:00";
    this.captureStartTime = null;
    this.timerInterval = null;
    this.adjustHeightThrottle = null;
    this.isThrottled = false;
    this.copyState = "idle";
    this.copyTimeout = null;
    this.adjustWindowHeight = this.adjustWindowHeight.bind(this);
  }
  connectedCallback() {
    super.connectedCallback();
    if (this.isSessionActive) {
      this.startTimer();
    }
    if (window.require) {
      const { ipcRenderer: ipcRenderer2 } = window.require("electron");
      ipcRenderer2.on("session-state-changed", (event, { isActive }) => {
        const wasActive = this.isSessionActive;
        this.isSessionActive = isActive;
        if (!wasActive && isActive) {
          this.hasCompletedRecording = false;
          this.startTimer();
          this.updateComplete.then(() => {
            const sttView = this.shadowRoot.querySelector("stt-view");
            const summaryView = this.shadowRoot.querySelector("summary-view");
            if (sttView) sttView.resetTranscript();
            if (summaryView) summaryView.resetAnalysis();
          });
          this.requestUpdate();
        }
        if (wasActive && !isActive) {
          this.hasCompletedRecording = true;
          this.stopTimer();
          this.requestUpdate();
        }
      });
    }
  }
  disconnectedCallback() {
    super.disconnectedCallback();
    this.stopTimer();
    if (this.adjustHeightThrottle) {
      clearTimeout(this.adjustHeightThrottle);
      this.adjustHeightThrottle = null;
    }
    if (this.copyTimeout) {
      clearTimeout(this.copyTimeout);
    }
  }
  startTimer() {
    this.captureStartTime = Date.now();
    this.timerInterval = setInterval(() => {
      const elapsed = Math.floor((Date.now() - this.captureStartTime) / 1e3);
      const minutes = Math.floor(elapsed / 60).toString().padStart(2, "0");
      const seconds = (elapsed % 60).toString().padStart(2, "0");
      this.elapsedTime = `${minutes}:${seconds}`;
      this.requestUpdate();
    }, 1e3);
  }
  stopTimer() {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
      this.timerInterval = null;
    }
  }
  adjustWindowHeight() {
    if (!window.require) return;
    this.updateComplete.then(() => {
      const topBar = this.shadowRoot.querySelector(".top-bar");
      const activeContent = this.viewMode === "transcript" ? this.shadowRoot.querySelector("stt-view") : this.shadowRoot.querySelector("summary-view");
      if (!topBar || !activeContent) return;
      const topBarHeight = topBar.offsetHeight;
      const contentHeight = activeContent.scrollHeight;
      const idealHeight = topBarHeight + contentHeight;
      const targetHeight = Math.min(700, idealHeight);
      console.log(
        `[Height Adjusted] Mode: ${this.viewMode}, TopBar: ${topBarHeight}px, Content: ${contentHeight}px, Ideal: ${idealHeight}px, Target: ${targetHeight}px`
      );
      const { ipcRenderer: ipcRenderer2 } = window.require("electron");
      ipcRenderer2.invoke("adjust-window-height", targetHeight);
    }).catch((error) => {
      console.error("Error in adjustWindowHeight:", error);
    });
  }
  toggleViewMode() {
    this.viewMode = this.viewMode === "insights" ? "transcript" : "insights";
    this.requestUpdate();
  }
  handleCopyHover(isHovering) {
    this.isHovering = isHovering;
    if (isHovering) {
      this.isAnimating = true;
    } else {
      this.isAnimating = false;
    }
    this.requestUpdate();
  }
  async handleCopy() {
    if (this.copyState === "copied") return;
    let textToCopy = "";
    if (this.viewMode === "transcript") {
      const sttView = this.shadowRoot.querySelector("stt-view");
      textToCopy = sttView ? sttView.getTranscriptText() : "";
    } else {
      const summaryView = this.shadowRoot.querySelector("summary-view");
      textToCopy = summaryView ? summaryView.getSummaryText() : "";
    }
    try {
      await navigator.clipboard.writeText(textToCopy);
      console.log("Content copied to clipboard");
      this.copyState = "copied";
      this.requestUpdate();
      if (this.copyTimeout) {
        clearTimeout(this.copyTimeout);
      }
      this.copyTimeout = setTimeout(() => {
        this.copyState = "idle";
        this.requestUpdate();
      }, 1500);
    } catch (err) {
      console.error("Failed to copy:", err);
    }
  }
  adjustWindowHeightThrottled() {
    if (this.isThrottled) {
      return;
    }
    this.adjustWindowHeight();
    this.isThrottled = true;
    this.adjustHeightThrottle = setTimeout(() => {
      this.isThrottled = false;
    }, 16);
  }
  updated(changedProperties) {
    super.updated(changedProperties);
    if (changedProperties.has("viewMode")) {
      this.adjustWindowHeight();
    }
  }
  handleSttMessagesUpdated(event) {
    this.adjustWindowHeightThrottled();
  }
  firstUpdated() {
    super.firstUpdated();
    setTimeout(() => this.adjustWindowHeight(), 200);
  }
  render() {
    const displayText = this.isHovering ? this.viewMode === "transcript" ? "Copy Transcript" : "Copy Glass Analysis" : this.viewMode === "insights" ? `Live insights` : `Glass is Listening ${this.elapsedTime}`;
    return H`
            <div class="assistant-container">
                <div class="top-bar">
                    <div class="bar-left-text">
                        <span class="bar-left-text-content ${this.isAnimating ? "slide-in" : ""}">${displayText}</span>
                    </div>
                    <div class="bar-controls">
                        <button class="toggle-button" @click=${this.toggleViewMode}>
                            ${this.viewMode === "insights" ? H`
                                      <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                          <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7z" />
                                          <circle cx="12" cy="12" r="3" />
                                      </svg>
                                      <span>Show Transcript</span>
                                  ` : H`
                                      <svg width="8" height="8" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                          <path d="M9 11l3 3L22 4" />
                                          <path d="M22 12v7a2 2 0 01-2 2H4a2 2 0 01-2-2V5a2 2 0 012-2h11" />
                                      </svg>
                                      <span>Show Insights</span>
                                  `}
                        </button>
                        <button
                            class="copy-button ${this.copyState === "copied" ? "copied" : ""}"
                            @click=${this.handleCopy}
                            @mouseenter=${() => this.handleCopyHover(true)}
                            @mouseleave=${() => this.handleCopyHover(false)}
                        >
                            <svg class="copy-icon" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="9" y="9" width="13" height="13" rx="2" ry="2" />
                                <path d="M5 15H4a2 2 0 01-2-2V4a2 2 0 012-2h9a2 2 0 012 2v1" />
                            </svg>
                            <svg class="check-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5">
                                <path d="M20 6L9 17l-5-5" />
                            </svg>
                        </button>
                    </div>
                </div>

                <stt-view 
                    .isVisible=${this.viewMode === "transcript"}
                    @stt-messages-updated=${this.handleSttMessagesUpdated}
                ></stt-view>

                <summary-view 
                    .isVisible=${this.viewMode === "insights"}
                    .hasCompletedRecording=${this.hasCompletedRecording}
                ></summary-view>
            </div>
        `;
  }
};
customElements.define("assistant-view", AssistantView);

// src/features/ask/AskView.js
var AskView = class extends ut {
  static properties = {
    currentResponse: { type: String },
    currentQuestion: { type: String },
    isLoading: { type: Boolean },
    copyState: { type: String },
    isHovering: { type: Boolean },
    hoveredLineIndex: { type: Number },
    lineCopyState: { type: Object },
    showTextInput: { type: Boolean },
    headerText: { type: String },
    headerAnimating: { type: Boolean },
    isStreaming: { type: Boolean }
  };
  static styles = r`
        :host {
            display: block;
            width: 100%;
            height: 100%;
            color: white;
            transform: translate3d(0, 0, 0);
            backface-visibility: hidden;
            transition: transform 0.2s cubic-bezier(0.23, 1, 0.32, 1), opacity 0.2s ease-out;
            will-change: transform, opacity;
        }

        :host(.hiding) {
            animation: slideUp 0.3s cubic-bezier(0.4, 0, 0.6, 1) forwards;
        }

        :host(.showing) {
            animation: slideDown 0.35s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
        }

        :host(.hidden) {
            opacity: 0;
            transform: translateY(-150%) scale(0.85);
            pointer-events: none;
        }

        @keyframes slideUp {
            0% {
                opacity: 1;
                transform: translateY(0) scale(1);
                filter: blur(0px);
            }
            30% {
                opacity: 0.7;
                transform: translateY(-20%) scale(0.98);
                filter: blur(0.5px);
            }
            70% {
                opacity: 0.3;
                transform: translateY(-80%) scale(0.92);
                filter: blur(1.5px);
            }
            100% {
                opacity: 0;
                transform: translateY(-150%) scale(0.85);
                filter: blur(2px);
            }
        }

        @keyframes slideDown {
            0% {
                opacity: 0;
                transform: translateY(-150%) scale(0.85);
                filter: blur(2px);
            }
            30% {
                opacity: 0.5;
                transform: translateY(-50%) scale(0.92);
                filter: blur(1px);
            }
            65% {
                opacity: 0.9;
                transform: translateY(-5%) scale(0.99);
                filter: blur(0.2px);
            }
            85% {
                opacity: 0.98;
                transform: translateY(2%) scale(1.005);
                filter: blur(0px);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
                filter: blur(0px);
            }
        }

        * {
            font-family: 'Helvetica Neue', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            cursor: default;
            user-select: none;
        }

        /* Allow text selection in assistant responses */
        .response-container, .response-container * {
            user-select: text !important;
            cursor: text !important;
        }

        .response-container pre {
            background: rgba(0, 0, 0, 0.4) !important;
            border-radius: 8px !important;
            padding: 12px !important;
            margin: 8px 0 !important;
            overflow-x: auto !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
            white-space: pre !important;
            word-wrap: normal !important;
            word-break: normal !important;
        }

        .response-container code {
            font-family: 'Monaco', 'Menlo', 'Consolas', monospace !important;
            font-size: 11px !important;
            background: transparent !important;
            white-space: pre !important;
            word-wrap: normal !important;
            word-break: normal !important;
        }

        .response-container pre code {
            white-space: pre !important;
            word-wrap: normal !important;
            word-break: normal !important;
            display: block !important;
        }

        .response-container p code {
            background: rgba(255, 255, 255, 0.1) !important;
            padding: 2px 4px !important;
            border-radius: 3px !important;
            color: #ffd700 !important;
        }

        .hljs-keyword {
            color: #ff79c6 !important;
        }
        .hljs-string {
            color: #f1fa8c !important;
        }
        .hljs-comment {
            color: #6272a4 !important;
        }
        .hljs-number {
            color: #bd93f9 !important;
        }
        .hljs-function {
            color: #50fa7b !important;
        }
        .hljs-variable {
            color: #8be9fd !important;
        }
        .hljs-built_in {
            color: #ffb86c !important;
        }
        .hljs-title {
            color: #50fa7b !important;
        }
        .hljs-attr {
            color: #50fa7b !important;
        }
        .hljs-tag {
            color: #ff79c6 !important;
        }

        .ask-container {
            display: flex;
            flex-direction: column;
            height: 100%;
            width: 100%;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 12px;
            outline: 0.5px rgba(255, 255, 255, 0.3) solid;
            outline-offset: -1px;
            backdrop-filter: blur(1px);
            box-sizing: border-box;
            position: relative;
            overflow: hidden;
        }

        .ask-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.15);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border-radius: 12px;
            filter: blur(10px);
            z-index: -1;
        }

        .response-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background: transparent;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            flex-shrink: 0;
        }

        .response-header.hidden {
            display: none;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-shrink: 0;
        }

        .response-icon {
            width: 20px;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .response-icon svg {
            width: 12px;
            height: 12px;
            stroke: rgba(255, 255, 255, 0.9);
        }

        .response-label {
            font-size: 13px;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
            white-space: nowrap;
            position: relative;
            overflow: hidden;
        }

        .response-label.animating {
            animation: fadeInOut 0.3s ease-in-out;
        }

        @keyframes fadeInOut {
            0% {
                opacity: 1;
                transform: translateY(0);
            }
            50% {
                opacity: 0;
                transform: translateY(-10px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;
            justify-content: flex-end;
        }

        .question-text {
            font-size: 13px;
            color: rgba(255, 255, 255, 0.7);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 300px;
            margin-right: 8px;
        }

        .header-controls {
            display: flex;
            gap: 8px;
            align-items: center;
            flex-shrink: 0;
        }

        .copy-button {
            background: transparent;
            color: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 4px;
            border-radius: 3px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 24px;
            height: 24px;
            flex-shrink: 0;
            transition: background-color 0.15s ease;
            position: relative;
            overflow: hidden;
        }

        .copy-button:hover {
            background: rgba(255, 255, 255, 0.15);
        }

        .copy-button svg {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
        }

        .copy-button .check-icon {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.5);
        }

        .copy-button.copied .copy-icon {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.5);
        }

        .copy-button.copied .check-icon {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
        }

        .close-button {
            background: rgba(255, 255, 255, 0.07);
            color: white;
            border: none;
            padding: 4px;
            border-radius: 20px;
            outline: 1px rgba(255, 255, 255, 0.3) solid;
            outline-offset: -1px;
            backdrop-filter: blur(0.5px);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
        }

        .close-button:hover {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 1);
        }

        .response-container {
            flex: 1;
            padding: 16px;
            padding-left: 48px;
            overflow-y: auto;
            font-size: 14px;
            line-height: 1.6;
            background: transparent;
            min-height: 0;
            max-height: 400px;
            position: relative;
        }

        .response-container.hidden {
            display: none;
        }

        .response-container::-webkit-scrollbar {
            width: 6px;
        }

        .response-container::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 3px;
        }

        .response-container::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
        }

        .response-container::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .loading-dots {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            padding: 40px;
        }

        .loading-dot {
            width: 8px;
            height: 8px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            animation: pulse 1.5s ease-in-out infinite;
        }

        .loading-dot:nth-child(1) {
            animation-delay: 0s;
        }

        .loading-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .loading-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes pulse {
            0%,
            80%,
            100% {
                opacity: 0.3;
                transform: scale(0.8);
            }
            40% {
                opacity: 1;
                transform: scale(1.2);
            }
        }

        .response-line {
            position: relative;
            padding: 2px 0;
            margin: 0;
            transition: background-color 0.15s ease;
        }

        .response-line:hover {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
        }

        .line-copy-button {
            position: absolute;
            left: -32px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            padding: 2px;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.15s ease, background-color 0.15s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
        }

        .response-line:hover .line-copy-button {
            opacity: 1;
        }

        .line-copy-button:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .line-copy-button.copied {
            background: rgba(40, 167, 69, 0.3);
        }

        .line-copy-button svg {
            width: 12px;
            height: 12px;
            stroke: rgba(255, 255, 255, 0.9);
        }

        .text-input-container {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 16px;
            background: rgba(0, 0, 0, 0.1);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            flex-shrink: 0;
            transition: all 0.3s ease-in-out;
            transform-origin: bottom;
        }

        .text-input-container.hidden {
            opacity: 0;
            transform: scaleY(0);
            padding: 0;
            height: 0;
            overflow: hidden;
        }

        .text-input-container.no-response {
            border-top: none;
        }

        #textInput {
            flex: 1;
            padding: 10px 14px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 20px;
            outline: none;
            border: none;
            color: white;
            font-size: 14px;
            font-family: 'Helvetica Neue', sans-serif;
            font-weight: 400;
        }

        #textInput::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        #textInput:focus {
            outline: none;
        }

        .response-line h1,
        .response-line h2,
        .response-line h3,
        .response-line h4,
        .response-line h5,
        .response-line h6 {
            color: rgba(255, 255, 255, 0.95);
            margin: 16px 0 8px 0;
            font-weight: 600;
        }

        .response-line p {
            margin: 8px 0;
            color: rgba(255, 255, 255, 0.9);
        }

        .response-line ul,
        .response-line ol {
            margin: 8px 0;
            padding-left: 20px;
        }

        .response-line li {
            margin: 4px 0;
            color: rgba(255, 255, 255, 0.9);
        }

        .response-line code {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.95);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 13px;
        }

        .response-line pre {
            background: rgba(255, 255, 255, 0.05);
            color: rgba(255, 255, 255, 0.95);
            padding: 12px;
            border-radius: 6px;
            overflow-x: auto;
            margin: 12px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .response-line pre code {
            background: none;
            padding: 0;
        }

        .response-line blockquote {
            border-left: 3px solid rgba(255, 255, 255, 0.3);
            margin: 12px 0;
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.05);
            color: rgba(255, 255, 255, 0.8);
        }

        .empty-state {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: rgba(255, 255, 255, 0.5);
            font-size: 14px;
        }

        /* ────────────────[ GLASS BYPASS ]─────────────── */
        :host-context(body.has-glass) .ask-container,
        :host-context(body.has-glass) .response-header,
        :host-context(body.has-glass) .response-icon,
        :host-context(body.has-glass) .copy-button,
        :host-context(body.has-glass) .close-button,
        :host-context(body.has-glass) .line-copy-button,
        :host-context(body.has-glass) .text-input-container,
        :host-context(body.has-glass) .response-container pre,
        :host-context(body.has-glass) .response-container p code,
        :host-context(body.has-glass) .response-container pre code {
            background: transparent !important;
            border: none !important;
            outline: none !important;
            box-shadow: none !important;
            filter: none !important;
            backdrop-filter: none !important;
        }

        :host-context(body.has-glass) .ask-container::before {
            display: none !important;
        }

        :host-context(body.has-glass) .copy-button:hover,
        :host-context(body.has-glass) .close-button:hover,
        :host-context(body.has-glass) .line-copy-button,
        :host-context(body.has-glass) .line-copy-button:hover,
        :host-context(body.has-glass) .response-line:hover {
            background: transparent !important;
        }

        :host-context(body.has-glass) .response-container::-webkit-scrollbar-track,
        :host-context(body.has-glass) .response-container::-webkit-scrollbar-thumb {
            background: transparent !important;
        }
    `;
  constructor() {
    super();
    this.currentResponse = "";
    this.currentQuestion = "";
    this.isLoading = false;
    this.copyState = "idle";
    this.showTextInput = true;
    this.headerText = "AI Response";
    this.headerAnimating = false;
    this.isStreaming = false;
    this.accumulatedResponse = "";
    this.marked = null;
    this.hljs = null;
    this.DOMPurify = null;
    this.isLibrariesLoaded = false;
    this.handleStreamChunk = this.handleStreamChunk.bind(this);
    this.handleStreamEnd = this.handleStreamEnd.bind(this);
    this.handleSendText = this.handleSendText.bind(this);
    this.handleTextKeydown = this.handleTextKeydown.bind(this);
    this.closeResponsePanel = this.closeResponsePanel.bind(this);
    this.handleCopy = this.handleCopy.bind(this);
    this.clearResponseContent = this.clearResponseContent.bind(this);
    this.processAssistantQuestion = this.processAssistantQuestion.bind(this);
    this.handleToggleTextInput = this.handleToggleTextInput.bind(this);
    this.handleEscKey = this.handleEscKey.bind(this);
    this.handleDocumentClick = this.handleDocumentClick.bind(this);
    this.handleWindowBlur = this.handleWindowBlur.bind(this);
    this.handleScroll = this.handleScroll.bind(this);
    this.loadLibraries();
    this.adjustHeightThrottle = null;
    this.isThrottled = false;
  }
  async loadLibraries() {
    try {
      if (!window.marked) {
        await this.loadScript("../../assets/marked-4.3.0.min.js");
      }
      if (!window.hljs) {
        await this.loadScript("../../assets/highlight-11.9.0.min.js");
      }
      if (!window.DOMPurify) {
        await this.loadScript("../../assets/dompurify-3.0.7.min.js");
      }
      this.marked = window.marked;
      this.hljs = window.hljs;
      this.DOMPurify = window.DOMPurify;
      if (this.marked && this.hljs) {
        this.marked.setOptions({
          highlight: (code, lang) => {
            if (lang && this.hljs.getLanguage(lang)) {
              try {
                return this.hljs.highlight(code, { language: lang }).value;
              } catch (err) {
                console.warn("Highlight error:", err);
              }
            }
            try {
              return this.hljs.highlightAuto(code).value;
            } catch (err) {
              console.warn("Auto highlight error:", err);
            }
            return code;
          },
          breaks: true,
          gfm: true,
          pedantic: false,
          smartypants: false,
          xhtml: false
        });
        this.isLibrariesLoaded = true;
        this.renderContent();
        console.log("Markdown libraries loaded successfully in AskView");
      }
      if (this.DOMPurify) {
        this.isDOMPurifyLoaded = true;
        console.log("DOMPurify loaded successfully in AskView");
      }
    } catch (error) {
      console.error("Failed to load libraries in AskView:", error);
    }
  }
  handleDocumentClick(e2) {
    if (!this.currentResponse && !this.isLoading && !this.isStreaming) {
      const askContainer = this.shadowRoot?.querySelector(".ask-container");
      if (askContainer && !e2.composedPath().includes(askContainer)) {
        this.closeIfNoContent();
      }
    }
  }
  handleEscKey(e2) {
    if (e2.key === "Escape") {
      e2.preventDefault();
      this.closeResponsePanel();
    }
  }
  handleWindowBlur() {
    if (!this.currentResponse && !this.isLoading && !this.isStreaming) {
      if (window.require) {
        const { ipcRenderer: ipcRenderer2 } = window.require("electron");
        ipcRenderer2.invoke("close-ask-window-if-empty");
      }
    }
  }
  closeIfNoContent() {
    if (window.require) {
      const { ipcRenderer: ipcRenderer2 } = window.require("electron");
      ipcRenderer2.invoke("force-close-window", "ask");
    }
  }
  loadScript(src) {
    return new Promise((resolve, reject) => {
      const script = document.createElement("script");
      script.src = src;
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });
  }
  parseMarkdown(text) {
    if (!text) return "";
    if (!this.isLibrariesLoaded || !this.marked) {
      return text;
    }
    try {
      return this.marked(text);
    } catch (error) {
      console.error("Markdown parsing error in AskView:", error);
      return text;
    }
  }
  fixIncompleteCodeBlocks(text) {
    if (!text) return text;
    const codeBlockMarkers = text.match(/```/g) || [];
    const markerCount = codeBlockMarkers.length;
    if (markerCount % 2 === 1) {
      return text + "\n```";
    }
    return text;
  }
  connectedCallback() {
    super.connectedCallback();
    console.log("\u{1F4F1} AskView connectedCallback - IPC \uC774\uBCA4\uD2B8 \uB9AC\uC2A4\uB108 \uC124\uC815");
    document.addEventListener("click", this.handleDocumentClick, true);
    document.addEventListener("keydown", this.handleEscKey);
    this.resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const needed = entry.contentRect.height;
        const current = window.innerHeight;
        if (needed > current - 4) {
          this.requestWindowResize(Math.ceil(needed));
        }
      }
    });
    const container = this.shadowRoot?.querySelector(".ask-container");
    if (container) this.resizeObserver.observe(container);
    this.handleQuestionFromAssistant = (event, question) => {
      console.log("\u{1F4E8} AskView: Received question from AssistantView:", question);
      this.currentResponse = "";
      this.isStreaming = false;
      this.requestUpdate();
      this.currentQuestion = question;
      this.isLoading = true;
      this.showTextInput = false;
      this.headerText = "analyzing screen...";
      this.startHeaderAnimation();
      this.requestUpdate();
      this.processAssistantQuestion(question);
    };
    if (window.require) {
      const { ipcRenderer: ipcRenderer2 } = window.require("electron");
      ipcRenderer2.on("ask-global-send", this.handleGlobalSendRequest);
      ipcRenderer2.on("toggle-text-input", this.handleToggleTextInput);
      ipcRenderer2.on("receive-question-from-assistant", this.handleQuestionFromAssistant);
      ipcRenderer2.on("hide-text-input", () => {
        console.log("\u{1F4E4} Hide text input signal received");
        this.showTextInput = false;
        this.requestUpdate();
      });
      ipcRenderer2.on("clear-ask-response", () => {
        console.log("\u{1F4E4} Clear response signal received");
        this.currentResponse = "";
        this.isStreaming = false;
        this.isLoading = false;
        this.headerText = "AI Response";
        this.requestUpdate();
      });
      ipcRenderer2.on("window-hide-animation", () => {
        console.log("\u{1F4E4} Ask window hiding - clearing response content");
        setTimeout(() => {
          this.clearResponseContent();
        }, 250);
      });
      ipcRenderer2.on("window-blur", this.handleWindowBlur);
      ipcRenderer2.on("window-did-show", () => {
        if (!this.currentResponse && !this.isLoading && !this.isStreaming) {
          this.focusTextInput();
        }
      });
      ipcRenderer2.on("ask-response-chunk", this.handleStreamChunk);
      ipcRenderer2.on("ask-response-stream-end", this.handleStreamEnd);
      ipcRenderer2.on("scroll-response-up", () => this.handleScroll("up"));
      ipcRenderer2.on("scroll-response-down", () => this.handleScroll("down"));
      console.log("\u2705 AskView: IPC \uC774\uBCA4\uD2B8 \uB9AC\uC2A4\uB108 \uB4F1\uB85D \uC644\uB8CC");
    }
  }
  disconnectedCallback() {
    super.disconnectedCallback();
    this.resizeObserver?.disconnect();
    console.log("\u{1F4F1} AskView disconnectedCallback - IPC \uC774\uBCA4\uD2B8 \uB9AC\uC2A4\uB108 \uC81C\uAC70");
    document.removeEventListener("click", this.handleDocumentClick, true);
    document.removeEventListener("keydown", this.handleEscKey);
    if (this.copyTimeout) {
      clearTimeout(this.copyTimeout);
    }
    if (this.headerAnimationTimeout) {
      clearTimeout(this.headerAnimationTimeout);
    }
    if (this.streamingTimeout) {
      clearTimeout(this.streamingTimeout);
    }
    Object.values(this.lineCopyTimeouts).forEach((timeout) => clearTimeout(timeout));
    if (window.require) {
      const { ipcRenderer: ipcRenderer2 } = window.require("electron");
      ipcRenderer2.removeListener("ask-global-send", this.handleGlobalSendRequest);
      ipcRenderer2.removeListener("toggle-text-input", this.handleToggleTextInput);
      ipcRenderer2.removeListener("clear-ask-response", () => {
      });
      ipcRenderer2.removeListener("hide-text-input", () => {
      });
      ipcRenderer2.removeListener("window-hide-animation", () => {
      });
      ipcRenderer2.removeListener("window-blur", this.handleWindowBlur);
      ipcRenderer2.removeListener("ask-response-chunk", this.handleStreamChunk);
      ipcRenderer2.removeListener("ask-response-stream-end", this.handleStreamEnd);
      ipcRenderer2.removeListener("scroll-response-up", () => this.handleScroll("up"));
      ipcRenderer2.removeListener("scroll-response-down", () => this.handleScroll("down"));
      console.log("\u2705 AskView: IPC \uC774\uBCA4\uD2B8 \uB9AC\uC2A4\uB108 \uC81C\uAC70 \uC644\uB8CC");
    }
  }
  handleScroll(direction) {
    const scrollableElement = this.shadowRoot.querySelector("#responseContainer");
    if (scrollableElement) {
      const scrollAmount = 100;
      if (direction === "up") {
        scrollableElement.scrollTop -= scrollAmount;
      } else {
        scrollableElement.scrollTop += scrollAmount;
      }
    }
  }
  // --- 스트리밍 처리 핸들러 ---
  handleStreamChunk(event, { token }) {
    if (!this.isStreaming) {
      this.isStreaming = true;
      this.isLoading = false;
      this.accumulatedResponse = "";
      const container = this.shadowRoot.getElementById("responseContainer");
      if (container) container.innerHTML = "";
      this.headerText = "AI Response";
      this.headerAnimating = false;
      this.requestUpdate();
    }
    this.accumulatedResponse += token;
    this.renderContent();
  }
  handleStreamEnd() {
    this.isStreaming = false;
    this.currentResponse = this.accumulatedResponse;
    if (this.headerText !== "AI Response") {
      this.headerText = "AI Response";
      this.requestUpdate();
    }
    this.renderContent();
  }
  // ✨ 렌더링 로직 통합
  renderContent() {
    if (!this.isLoading && !this.isStreaming && !this.currentResponse) {
      const responseContainer2 = this.shadowRoot.getElementById("responseContainer");
      if (responseContainer2) responseContainer2.innerHTML = '<div class="empty-state">Ask a question to see the response here</div>';
      return;
    }
    const responseContainer = this.shadowRoot.getElementById("responseContainer");
    if (!responseContainer) return;
    if (this.isLoading) {
      responseContainer.innerHTML = `
                <div class="loading-dots">
                    <div class="loading-dot"></div><div class="loading-dot"></div><div class="loading-dot"></div>
                </div>`;
      return;
    }
    let textToRender = this.isStreaming ? this.accumulatedResponse : this.currentResponse;
    textToRender = this.fixIncompleteMarkdown(textToRender);
    textToRender = this.fixIncompleteCodeBlocks(textToRender);
    if (this.isLibrariesLoaded && this.marked && this.DOMPurify) {
      try {
        const parsedHtml = this.marked.parse(textToRender);
        const cleanHtml = this.DOMPurify.sanitize(parsedHtml, {
          ALLOWED_TAGS: [
            "h1",
            "h2",
            "h3",
            "h4",
            "h5",
            "h6",
            "p",
            "br",
            "strong",
            "b",
            "em",
            "i",
            "ul",
            "ol",
            "li",
            "blockquote",
            "code",
            "pre",
            "a",
            "img",
            "table",
            "thead",
            "tbody",
            "tr",
            "th",
            "td",
            "hr",
            "sup",
            "sub",
            "del",
            "ins"
          ],
          ALLOWED_ATTR: ["href", "src", "alt", "title", "class", "id", "target", "rel"]
        });
        responseContainer.innerHTML = cleanHtml;
        if (this.hljs) {
          responseContainer.querySelectorAll("pre code").forEach((block) => {
            this.hljs.highlightElement(block);
          });
        }
        responseContainer.scrollTop = responseContainer.scrollHeight;
      } catch (error) {
        console.error("Error rendering markdown:", error);
        responseContainer.textContent = textToRender;
      }
    } else {
      const basicHtml = textToRender.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/\n\n/g, "</p><p>").replace(/\n/g, "<br>").replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>").replace(/\*(.*?)\*/g, "<em>$1</em>").replace(/`([^`]+)`/g, "<code>$1</code>");
      responseContainer.innerHTML = `<p>${basicHtml}</p>`;
    }
    this.adjustWindowHeightThrottled();
  }
  clearResponseContent() {
    this.currentResponse = "";
    this.currentQuestion = "";
    this.isLoading = false;
    this.isStreaming = false;
    this.headerText = "AI Response";
    this.showTextInput = true;
    this.accumulatedResponse = "";
    this.requestUpdate();
    this.renderContent();
  }
  handleToggleTextInput() {
    this.showTextInput = !this.showTextInput;
    this.requestUpdate();
  }
  requestWindowResize(targetHeight) {
    if (window.require) {
      const { ipcRenderer: ipcRenderer2 } = window.require("electron");
      ipcRenderer2.invoke("adjust-window-height", targetHeight);
    }
  }
  animateHeaderText(text) {
    this.headerAnimating = true;
    this.requestUpdate();
    setTimeout(() => {
      this.headerText = text;
      this.headerAnimating = false;
      this.requestUpdate();
    }, 150);
  }
  startHeaderAnimation() {
    this.animateHeaderText("analyzing screen...");
    if (this.headerAnimationTimeout) {
      clearTimeout(this.headerAnimationTimeout);
    }
    this.headerAnimationTimeout = setTimeout(() => {
      this.animateHeaderText("thinking...");
    }, 1500);
  }
  renderMarkdown(content) {
    if (!content) return "";
    if (this.isLibrariesLoaded && this.marked) {
      return this.parseMarkdown(content);
    }
    return content.replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>").replace(/\*(.*?)\*/g, "<em>$1</em>").replace(/`(.*?)`/g, "<code>$1</code>");
  }
  closeResponsePanel() {
    if (window.require) {
      const { ipcRenderer: ipcRenderer2 } = window.require("electron");
      ipcRenderer2.invoke("force-close-window", "ask");
    }
  }
  fixIncompleteMarkdown(text) {
    if (!text) return text;
    const boldCount = (text.match(/\*\*/g) || []).length;
    if (boldCount % 2 === 1) {
      text += "**";
    }
    const italicCount = (text.match(/(?<!\*)\*(?!\*)/g) || []).length;
    if (italicCount % 2 === 1) {
      text += "*";
    }
    const inlineCodeCount = (text.match(/`/g) || []).length;
    if (inlineCodeCount % 2 === 1) {
      text += "`";
    }
    const openBrackets = (text.match(/\[/g) || []).length;
    const closeBrackets = (text.match(/\]/g) || []).length;
    if (openBrackets > closeBrackets) {
      text += "]";
    }
    const openParens = (text.match(/\]\(/g) || []).length;
    const closeParens = (text.match(/\)\s*$/g) || []).length;
    if (openParens > closeParens && text.endsWith("(")) {
      text += ")";
    }
    return text;
  }
  // ✨ processAssistantQuestion 수정
  async processAssistantQuestion(question) {
    this.currentQuestion = question;
    this.showTextInput = false;
    this.isLoading = true;
    this.isStreaming = false;
    this.currentResponse = "";
    this.accumulatedResponse = "";
    this.startHeaderAnimation();
    this.requestUpdate();
    this.renderContent();
    if (window.require) {
      const { ipcRenderer: ipcRenderer2 } = window.require("electron");
      ipcRenderer2.invoke("ask:sendMessage", question).catch((error) => {
        console.error("Error processing assistant question:", error);
        this.isLoading = false;
        this.isStreaming = false;
        this.currentResponse = `Error: ${error.message}`;
        this.renderContent();
      });
    }
  }
  async handleCopy() {
    if (this.copyState === "copied") return;
    let responseToCopy = this.currentResponse;
    if (this.isDOMPurifyLoaded && this.DOMPurify) {
      const testHtml = this.renderMarkdown(responseToCopy);
      const sanitized = this.DOMPurify.sanitize(testHtml);
      if (this.DOMPurify.removed && this.DOMPurify.removed.length > 0) {
        console.warn("Unsafe content detected, copy blocked");
        return;
      }
    }
    const textToCopy = `Question: ${this.currentQuestion}

Answer: ${responseToCopy}`;
    try {
      await navigator.clipboard.writeText(textToCopy);
      console.log("Content copied to clipboard");
      this.copyState = "copied";
      this.requestUpdate();
      if (this.copyTimeout) {
        clearTimeout(this.copyTimeout);
      }
      this.copyTimeout = setTimeout(() => {
        this.copyState = "idle";
        this.requestUpdate();
      }, 1500);
    } catch (err) {
      console.error("Failed to copy:", err);
    }
  }
  async handleLineCopy(lineIndex) {
    const originalLines = this.currentResponse.split("\n");
    const lineToCopy = originalLines[lineIndex];
    if (!lineToCopy) return;
    try {
      await navigator.clipboard.writeText(lineToCopy);
      console.log("Line copied to clipboard");
      this.lineCopyState = { ...this.lineCopyState, [lineIndex]: true };
      this.requestUpdate();
      if (this.lineCopyTimeouts && this.lineCopyTimeouts[lineIndex]) {
        clearTimeout(this.lineCopyTimeouts[lineIndex]);
      }
      this.lineCopyTimeouts[lineIndex] = setTimeout(() => {
        const updatedState = { ...this.lineCopyState };
        delete updatedState[lineIndex];
        this.lineCopyState = updatedState;
        this.requestUpdate();
      }, 1500);
    } catch (err) {
      console.error("Failed to copy line:", err);
    }
  }
  async handleSendText() {
    const textInput = this.shadowRoot?.getElementById("textInput");
    if (!textInput) return;
    const text = textInput.value.trim();
    if (!text) return;
    textInput.value = "";
    this.currentQuestion = text;
    this.lineCopyState = {};
    this.showTextInput = false;
    this.isLoading = true;
    this.isStreaming = false;
    this.currentResponse = "";
    this.accumulatedResponse = "";
    this.startHeaderAnimation();
    this.requestUpdate();
    this.renderContent();
    if (window.require) {
      const { ipcRenderer: ipcRenderer2 } = window.require("electron");
      ipcRenderer2.invoke("ask:sendMessage", text).catch((error) => {
        console.error("Error sending text:", error);
        this.isLoading = false;
        this.isStreaming = false;
        this.currentResponse = `Error: ${error.message}`;
        this.renderContent();
      });
    }
  }
  handleTextKeydown(e2) {
    if (e2.isComposing) {
      return;
    }
    const isPlainEnter = e2.key === "Enter" && !e2.shiftKey && !e2.metaKey && !e2.ctrlKey;
    const isModifierEnter = e2.key === "Enter" && (e2.metaKey || e2.ctrlKey);
    if (isPlainEnter || isModifierEnter) {
      e2.preventDefault();
      this.handleSendText();
    }
  }
  updated(changedProperties) {
    super.updated(changedProperties);
    if (changedProperties.has("isLoading")) {
      this.renderContent();
    }
    if (changedProperties.has("showTextInput") || changedProperties.has("isLoading")) {
      this.adjustWindowHeightThrottled();
    }
    if (changedProperties.has("showTextInput") && this.showTextInput) {
      this.focusTextInput();
    }
  }
  focusTextInput() {
    requestAnimationFrame(() => {
      const textInput = this.shadowRoot?.getElementById("textInput");
      if (textInput) {
        textInput.focus();
      }
    });
  }
  firstUpdated() {
    setTimeout(() => this.adjustWindowHeight(), 200);
  }
  handleGlobalSendRequest() {
    const textInput = this.shadowRoot?.getElementById("textInput");
    if (!textInput) return;
    textInput.focus();
    if (!textInput.value.trim()) return;
    this.handleSendText();
  }
  getTruncatedQuestion(question, maxLength = 30) {
    if (!question) return "";
    if (question.length <= maxLength) return question;
    return question.substring(0, maxLength) + "...";
  }
  handleInputFocus() {
    this.isInputFocused = true;
  }
  handleInputBlur(e2) {
    this.isInputFocused = false;
    setTimeout(() => {
      const activeElement = this.shadowRoot?.activeElement || document.activeElement;
      const textInput = this.shadowRoot?.getElementById("textInput");
      if (!this.currentResponse && !this.isLoading && !this.isStreaming && activeElement !== textInput && !this.isInputFocused) {
        this.closeIfNoContent();
      }
    }, 200);
  }
  render() {
    const hasResponse = this.isLoading || this.currentResponse || this.isStreaming;
    return H`
            <div class="ask-container">
                <!-- Response Header -->
                <div class="response-header ${!hasResponse ? "hidden" : ""}">
                    <div class="header-left">
                        <div class="response-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z" />
                                <path d="M8 12l2 2 4-4" />
                            </svg>
                        </div>
                        <span class="response-label ${this.headerAnimating ? "animating" : ""}">${this.headerText}</span>
                    </div>
                    <div class="header-right">
                        <span class="question-text">${this.getTruncatedQuestion(this.currentQuestion)}</span>
                        <div class="header-controls">
                            <button class="copy-button ${this.copyState === "copied" ? "copied" : ""}" @click=${this.handleCopy}>
                                <svg class="copy-icon" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2" />
                                    <path d="M5 15H4a2 2 0 01-2-2V4a2 2 0 012-2h9a2 2 0 012 2v1" />
                                </svg>
                                <svg
                                    class="check-icon"
                                    width="16"
                                    height="16"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    stroke-width="2.5"
                                >
                                    <path d="M20 6L9 17l-5-5" />
                                </svg>
                            </button>
                            <button class="close-button" @click=${this.closeResponsePanel}>
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <line x1="18" y1="6" x2="6" y2="18" />
                                    <line x1="6" y1="6" x2="18" y2="18" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Response Container -->
                <div class="response-container ${!hasResponse ? "hidden" : ""}" id="responseContainer">
                    <!-- Content is dynamically generated in updateResponseContent() -->
                </div>

                <!-- Text Input Container -->
                <div class="text-input-container ${!hasResponse ? "no-response" : ""} ${!this.showTextInput ? "hidden" : ""}">
                    <input
                        type="text"
                        id="textInput"
                        placeholder="Ask about your screen or audio"
                        @keydown=${this.handleTextKeydown}
                        @focus=${this.handleInputFocus}
                        @blur=${this.handleInputBlur}
                    />
                </div>
            </div>
        `;
  }
  // Dynamically resize the BrowserWindow to fit current content
  adjustWindowHeight() {
    if (!window.require) return;
    this.updateComplete.then(() => {
      const headerEl = this.shadowRoot.querySelector(".response-header");
      const responseEl = this.shadowRoot.querySelector(".response-container");
      const inputEl = this.shadowRoot.querySelector(".text-input-container");
      if (!headerEl || !responseEl) return;
      const headerHeight = headerEl.classList.contains("hidden") ? 0 : headerEl.offsetHeight;
      const responseHeight = responseEl.scrollHeight;
      const inputHeight = inputEl && !inputEl.classList.contains("hidden") ? inputEl.offsetHeight : 0;
      const idealHeight = headerHeight + responseHeight + inputHeight;
      const targetHeight = Math.min(700, idealHeight);
      const { ipcRenderer: ipcRenderer2 } = window.require("electron");
      ipcRenderer2.invoke("adjust-window-height", targetHeight);
    }).catch((err) => console.error("AskView adjustWindowHeight error:", err));
  }
  // Throttled wrapper to avoid excessive IPC spam (executes at most once per animation frame)
  adjustWindowHeightThrottled() {
    if (this.isThrottled) return;
    this.adjustWindowHeight();
    this.isThrottled = true;
    this.adjustHeightThrottle = setTimeout(() => {
      this.isThrottled = false;
    }, 16);
  }
};
customElements.define("ask-view", AskView);

// src/features/settings/ShortCutSettingsView.js
var commonSystemShortcuts = /* @__PURE__ */ new Set([
  "Cmd+Q",
  "Cmd+W",
  "Cmd+A",
  "Cmd+S",
  "Cmd+Z",
  "Cmd+X",
  "Cmd+C",
  "Cmd+V",
  "Cmd+P",
  "Cmd+F",
  "Cmd+G",
  "Cmd+H",
  "Cmd+M",
  "Cmd+N",
  "Cmd+O",
  "Cmd+T",
  "Ctrl+Q",
  "Ctrl+W",
  "Ctrl+A",
  "Ctrl+S",
  "Ctrl+Z",
  "Ctrl+X",
  "Ctrl+C",
  "Ctrl+V",
  "Ctrl+P",
  "Ctrl+F",
  "Ctrl+G",
  "Ctrl+H",
  "Ctrl+M",
  "Ctrl+N",
  "Ctrl+O",
  "Ctrl+T"
]);
var displayNameMap = {
  nextStep: "Ask Anything",
  moveUp: "Move Up Window",
  moveDown: "Move Down Window",
  scrollUp: "Scroll Up Response",
  scrollDown: "Scroll Down Response"
};
var ShortcutSettingsView = class extends ut {
  static styles = r`
        * { font-family:'Helvetica Neue',-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;
            cursor:default; user-select:none; box-sizing:border-box; }

        :host { display:flex; width:100%; height:100%; color:white; }

        .container { display:flex; flex-direction:column; height:100%;
            background:rgba(20,20,20,.9); border-radius:12px;
            outline:.5px rgba(255,255,255,.2) solid; outline-offset:-1px;
            position:relative; overflow:hidden; padding:12px; }

        .close-button{position:absolute;top:10px;right:10px;inline-size:14px;block-size:14px;
            background:rgba(255,255,255,.1);border:none;border-radius:3px;
            color:rgba(255,255,255,.7);display:grid;place-items:center;
            font-size:14px;line-height:0;cursor:pointer;transition:.15s;z-index:10;}
        .close-button:hover{background:rgba(255,255,255,.2);color:rgba(255,255,255,.9);}

        .title{font-size:14px;font-weight:500;margin:0 0 8px;padding-bottom:8px;
            border-bottom:1px solid rgba(255,255,255,.1);text-align:center;}

        .scroll-area{flex:1 1 auto;overflow-y:auto;margin:0 -4px;padding:4px;}

        .shortcut-entry{display:flex;align-items:center;width:100%;gap:8px;
            margin-bottom:8px;font-size:12px;padding:4px;}
        .shortcut-name{flex:1 1 auto;color:rgba(255,255,255,.9);font-weight:300;
            white-space:nowrap;overflow:hidden;text-overflow:ellipsis;}

        .action-btn{background:none;border:none;color:rgba(0,122,255,.8);
            font-size:11px;padding:0 4px;cursor:pointer;transition:.15s;}
        .action-btn:hover{color:#0a84ff;text-decoration:underline;}

        .shortcut-input{inline-size:120px;background:rgba(0,0,0,.2);
            border:1px solid rgba(255,255,255,.2);border-radius:4px;
            padding:4px 6px;font:11px 'SF Mono','Menlo',monospace;
            color:white;text-align:right;cursor:text;margin-left:auto;}
        .shortcut-input:focus,.shortcut-input.capturing{
            outline:none;border-color:rgba(0,122,255,.6);
            box-shadow:0 0 0 1px rgba(0,122,255,.3);}

        .feedback{font-size:10px;margin-top:2px;min-height:12px;}
        .feedback.error{color:#ef4444;}
        .feedback.success{color:#22c55e;}

        .actions{display:flex;gap:4px;padding-top:8px;border-top:1px solid rgba(255,255,255,.1);}
        .settings-button{flex:1;background:rgba(255,255,255,.1);
            border:1px solid rgba(255,255,255,.2);border-radius:4px;
            color:white;padding:5px 10px;font-size:11px;cursor:pointer;transition:.15s;}
        .settings-button:hover{background:rgba(255,255,255,.15);}
        .settings-button.primary{background:rgba(0,122,255,.25);border-color:rgba(0,122,255,.6);}
        .settings-button.primary:hover{background:rgba(0,122,255,.35);}
        .settings-button.danger{background:rgba(255,59,48,.1);border-color:rgba(255,59,48,.3);
            color:rgba(255,59,48,.9);}
        .settings-button.danger:hover{background:rgba(255,59,48,.15);}
    `;
  static properties = {
    shortcuts: { type: Object, state: true },
    isLoading: { type: Boolean, state: true },
    capturingKey: { type: String, state: true },
    feedback: { type: Object, state: true }
  };
  constructor() {
    super();
    this.shortcuts = {};
    this.feedback = {};
    this.isLoading = true;
    this.capturingKey = null;
    this.ipcRenderer = window.require ? window.require("electron").ipcRenderer : null;
  }
  connectedCallback() {
    super.connectedCallback();
    if (!this.ipcRenderer) return;
    this.loadShortcutsHandler = (event, keybinds) => {
      this.shortcuts = keybinds;
      this.isLoading = false;
    };
    this.ipcRenderer.on("load-shortcuts", this.loadShortcutsHandler);
  }
  disconnectedCallback() {
    super.disconnectedCallback();
    if (this.ipcRenderer && this.loadShortcutsHandler) {
      this.ipcRenderer.removeListener("load-shortcuts", this.loadShortcutsHandler);
    }
  }
  handleKeydown(e2, shortcutKey) {
    e2.preventDefault();
    e2.stopPropagation();
    const result = this._parseAccelerator(e2);
    if (!result) return;
    const { accel, error } = result;
    if (error) {
      this.feedback = { ...this.feedback, [shortcutKey]: { type: "error", msg: error } };
      return;
    }
    this.shortcuts = { ...this.shortcuts, [shortcutKey]: accel };
    this.feedback = { ...this.feedback, [shortcutKey]: { type: "success", msg: "Shortcut set" } };
    this.stopCapture();
  }
  _parseAccelerator(e2) {
    const parts = [];
    if (e2.metaKey) parts.push("Cmd");
    if (e2.ctrlKey) parts.push("Ctrl");
    if (e2.altKey) parts.push("Alt");
    if (e2.shiftKey) parts.push("Shift");
    const isModifier = ["Meta", "Control", "Alt", "Shift"].includes(e2.key);
    if (isModifier) return null;
    const map = { ArrowUp: "Up", ArrowDown: "Down", ArrowLeft: "Left", ArrowRight: "Right", " ": "Space" };
    parts.push(e2.key.length === 1 ? e2.key.toUpperCase() : map[e2.key] || e2.key);
    const accel = parts.join("+");
    if (parts.length === 1) return { error: "Invalid shortcut: needs a modifier" };
    if (parts.length > 4) return { error: "Invalid shortcut: max 4 keys" };
    if (commonSystemShortcuts.has(accel)) return { error: "Invalid shortcut: system reserved" };
    return { accel };
  }
  startCapture(key) {
    this.capturingKey = key;
    this.feedback = { ...this.feedback, [key]: void 0 };
  }
  disableShortcut(key) {
    this.shortcuts = { ...this.shortcuts, [key]: "" };
    this.feedback = { ...this.feedback, [key]: { type: "success", msg: "Shortcut disabled" } };
  }
  stopCapture() {
    this.capturingKey = null;
  }
  async handleSave() {
    if (!this.ipcRenderer) return;
    const result = await this.ipcRenderer.invoke("save-shortcuts", this.shortcuts);
    if (!result.success) {
      alert("Failed to save shortcuts: " + result.error);
    }
  }
  handleClose() {
    if (!this.ipcRenderer) return;
    this.ipcRenderer.send("close-shortcut-editor");
  }
  async handleResetToDefault() {
    if (!this.ipcRenderer) return;
    const confirmation = confirm("Are you sure you want to reset all shortcuts to their default values?");
    if (!confirmation) return;
    try {
      const defaultShortcuts = await this.ipcRenderer.invoke("get-default-shortcuts");
      this.shortcuts = defaultShortcuts;
    } catch (error) {
      alert("Failed to load default settings.");
    }
  }
  formatShortcutName(name) {
    if (displayNameMap[name]) {
      return displayNameMap[name];
    }
    const result = name.replace(/([A-Z])/g, " $1");
    return result.charAt(0).toUpperCase() + result.slice(1);
  }
  render() {
    if (this.isLoading) {
      return H`<div class="container"><div class="loading-state">Loading Shortcuts...</div></div>`;
    }
    return H`
          <div class="container">
            <button class="close-button" @click=${this.handleClose} title="Close">&times;</button>
            <h1 class="title">Edit Shortcuts</h1>
    
            <div class="scroll-area">
              ${Object.keys(this.shortcuts).map((key) => H`
                <div>
                  <div class="shortcut-entry">
                    <span class="shortcut-name">${this.formatShortcutName(key)}</span>
    
                    <!-- Edit & Disable 버튼 -->
                    <button class="action-btn" @click=${() => this.startCapture(key)}>Edit</button>
                    <button class="action-btn" @click=${() => this.disableShortcut(key)}>Disable</button>
    
                    <input readonly
                      class="shortcut-input ${this.capturingKey === key ? "capturing" : ""}"
                      .value=${this.shortcuts[key] || ""}
                      placeholder=${this.capturingKey === key ? "Press new shortcut\u2026" : "Click to edit"}
                      @click=${() => this.startCapture(key)}
                      @keydown=${(e2) => this.handleKeydown(e2, key)}
                      @blur=${() => this.stopCapture()}
                    />
                  </div>
    
                  ${this.feedback[key] ? H`
                    <div class="feedback ${this.feedback[key].type}">
                      ${this.feedback[key].msg}
                    </div>` : H`<div class="feedback"></div>`}
                </div>
              `)}
            </div>
    
            <div class="actions">
              <button class="settings-button" @click=${this.handleClose}>Cancel</button>
              <button class="settings-button danger" @click=${this.handleResetToDefault}>Reset to Default</button>
              <button class="settings-button primary" @click=${this.handleSave}>Save</button>
            </div>
          </div>
        `;
  }
};
customElements.define("shortcut-settings-view", ShortcutSettingsView);

// src/features/listen/renderer/renderer.js
var { ipcRenderer } = __require("electron");
var listenCapture = require_listenCapture();
var realtimeConversationHistory = [];
function pickleGlassElement() {
  return document.getElementById("pickle-glass");
}
async function initializeopenai(profile = "interview", language = "en") {
  try {
    console.log(`Requesting OpenAI initialization with profile: ${profile}, language: ${language}`);
    const success = await ipcRenderer.invoke("initialize-openai", profile, language);
    if (success) {
      console.log("OpenAI initialization successful.");
    } else {
      console.error("OpenAI initialization failed.");
      const appElement = pickleGlassElement();
      if (appElement && typeof appElement.setStatus === "function") {
        appElement.setStatus("Initialization Failed");
      }
    }
  } catch (error) {
    console.error("Error during OpenAI initialization IPC call:", error);
    const appElement = pickleGlassElement();
    if (appElement && typeof appElement.setStatus === "function") {
      appElement.setStatus("Error");
    }
  }
}
ipcRenderer.on("update-status", (event, status) => {
  console.log("Status update:", status);
  pickleGlass.e().setStatus(status);
});
ipcRenderer.on("stt-update", (event, data) => {
  console.log("Renderer.js stt-update", data);
  const { speaker, text, isFinal, isPartial, timestamp } = data;
  if (isPartial) {
    console.log(`\u{1F504} [${speaker} - partial]: ${text}`);
  } else if (isFinal) {
    console.log(`\u2705 [${speaker} - final]: ${text}`);
    const speakerText = speaker.toLowerCase();
    const conversationText = `${speakerText}: ${text.trim()}`;
    realtimeConversationHistory.push(conversationText);
    if (realtimeConversationHistory.length > 30) {
      realtimeConversationHistory = realtimeConversationHistory.slice(-30);
    }
    console.log(`\u{1F4DD} Updated realtime conversation history: ${realtimeConversationHistory.length} texts`);
    console.log(`\u{1F4CB} Latest text: ${conversationText}`);
  }
  if (pickleGlass.e() && typeof pickleGlass.e().updateRealtimeTranscription === "function") {
    pickleGlass.e().updateRealtimeTranscription({
      speaker,
      text,
      isFinal,
      isPartial,
      timestamp
    });
  }
});
ipcRenderer.on("update-structured-data", (_2, structuredData) => {
  console.log("\u{1F4E5} Received structured data update:", structuredData);
  window.pickleGlass.structuredData = structuredData;
  window.pickleGlass.setStructuredData(structuredData);
});
window.pickleGlass.structuredData = {
  summary: [],
  topic: { header: "", bullets: [] },
  actions: []
};
window.pickleGlass.setStructuredData = (data) => {
  window.pickleGlass.structuredData = data;
  pickleGlass.e()?.updateStructuredData?.(data);
};
window.pickleGlass = {
  initializeopenai,
  startCapture: listenCapture.startCapture,
  stopCapture: listenCapture.stopCapture,
  isLinux: listenCapture.isLinux,
  isMacOS: listenCapture.isMacOS,
  captureManualScreenshot: listenCapture.captureManualScreenshot,
  getCurrentScreenshot: listenCapture.getCurrentScreenshot,
  e: pickleGlassElement
};
ipcRenderer.on("session-state-changed", (_event, { isActive }) => {
  if (!isActive) {
    console.log("[Renderer] Session ended \u2013 stopping local capture");
    listenCapture.stopCapture();
  } else {
    console.log("[Renderer] New session started \u2013 clearing in-memory history and summaries");
    realtimeConversationHistory = [];
    const blankData = {
      summary: [],
      topic: { header: "", bullets: [] },
      actions: [],
      followUps: []
    };
    window.pickleGlass.setStructuredData(blankData);
  }
});

// src/app/PickleGlassApp.js
var PickleGlassApp = class extends ut {
  static styles = r`
        :host {
            display: block;
            width: 100%;
            height: 100%;
            color: var(--text-color);
            background: transparent;
            border-radius: 7px;
        }

        assistant-view {
            display: block;
            width: 100%;
            height: 100%;
        }

        ask-view, settings-view, history-view, help-view, setup-view {
            display: block;
            width: 100%;
            height: 100%;
        }

    `;
  static properties = {
    currentView: { type: String },
    statusText: { type: String },
    startTime: { type: Number },
    currentResponseIndex: { type: Number },
    isMainViewVisible: { type: Boolean },
    selectedProfile: { type: String },
    selectedLanguage: { type: String },
    selectedScreenshotInterval: { type: String },
    selectedImageQuality: { type: String },
    isClickThrough: { type: Boolean, state: true },
    layoutMode: { type: String },
    _viewInstances: { type: Object, state: true },
    _isClickThrough: { state: true },
    structuredData: { type: Object }
  };
  constructor() {
    super();
    const urlParams = new URLSearchParams(window.location.search);
    this.currentView = urlParams.get("view") || "listen";
    this.currentResponseIndex = -1;
    this.selectedProfile = localStorage.getItem("selectedProfile") || "interview";
    let lang = localStorage.getItem("selectedLanguage") || "en";
    if (lang.includes("-")) {
      const newLang = lang.split("-")[0];
      console.warn(`[Migration] Correcting language format from "${lang}" to "${newLang}".`);
      localStorage.setItem("selectedLanguage", newLang);
      lang = newLang;
    }
    this.selectedLanguage = lang;
    this.selectedScreenshotInterval = localStorage.getItem("selectedScreenshotInterval") || "5";
    this.selectedImageQuality = localStorage.getItem("selectedImageQuality") || "medium";
    this._isClickThrough = false;
    this.outlines = [];
    this.analysisRequests = [];
    window.pickleGlass.setStructuredData = (data) => {
      this.updateStructuredData(data);
    };
  }
  connectedCallback() {
    super.connectedCallback();
    if (window.require) {
      const { ipcRenderer: ipcRenderer2 } = window.require("electron");
      ipcRenderer2.on("update-status", (_2, status) => this.setStatus(status));
      ipcRenderer2.on("click-through-toggled", (_2, isEnabled) => {
        this._isClickThrough = isEnabled;
      });
      ipcRenderer2.on("start-listening-session", () => {
        console.log("Received start-listening-session command, calling handleListenClick.");
        this.handleListenClick();
      });
    }
  }
  disconnectedCallback() {
    super.disconnectedCallback();
    if (window.require) {
      const { ipcRenderer: ipcRenderer2 } = window.require("electron");
      ipcRenderer2.removeAllListeners("update-status");
      ipcRenderer2.removeAllListeners("click-through-toggled");
      ipcRenderer2.removeAllListeners("start-listening-session");
    }
  }
  updated(changedProperties) {
    if (changedProperties.has("isMainViewVisible") || changedProperties.has("currentView")) {
      this.requestWindowResize();
    }
    if (changedProperties.has("currentView")) {
      const viewContainer = this.shadowRoot?.querySelector(".view-container");
      if (viewContainer) {
        viewContainer.classList.add("entering");
        requestAnimationFrame(() => {
          viewContainer.classList.remove("entering");
        });
      }
    }
    if (changedProperties.has("selectedProfile")) {
      localStorage.setItem("selectedProfile", this.selectedProfile);
    }
    if (changedProperties.has("selectedLanguage")) {
      localStorage.setItem("selectedLanguage", this.selectedLanguage);
    }
    if (changedProperties.has("selectedScreenshotInterval")) {
      localStorage.setItem("selectedScreenshotInterval", this.selectedScreenshotInterval);
    }
    if (changedProperties.has("selectedImageQuality")) {
      localStorage.setItem("selectedImageQuality", this.selectedImageQuality);
    }
    if (changedProperties.has("layoutMode")) {
      this.updateLayoutMode();
    }
  }
  requestWindowResize() {
    if (window.require) {
      const { ipcRenderer: ipcRenderer2 } = window.require("electron");
      ipcRenderer2.invoke("resize-window", {
        isMainViewVisible: this.isMainViewVisible,
        view: this.currentView
      });
    }
  }
  setStatus(text) {
    this.statusText = text;
  }
  async handleListenClick() {
    if (window.require) {
      const { ipcRenderer: ipcRenderer2 } = window.require("electron");
      const isActive = await ipcRenderer2.invoke("is-session-active");
      if (isActive) {
        console.log("Session is already active. No action needed.");
        return;
      }
    }
    if (window.pickleGlass) {
      await window.pickleGlass.initializeopenai(this.selectedProfile, this.selectedLanguage);
      window.pickleGlass.startCapture(this.selectedScreenshotInterval, this.selectedImageQuality);
    }
    this.structuredData = {
      summary: [],
      topic: { header: "", bullets: [] },
      actions: [],
      followUps: []
    };
    this.currentResponseIndex = -1;
    this.startTime = Date.now();
    this.currentView = "listen";
    this.isMainViewVisible = true;
  }
  handleShowHideClick() {
    this.isMainViewVisible = !this.isMainViewVisible;
  }
  handleSettingsClick() {
    this.currentView = "settings";
    this.isMainViewVisible = true;
  }
  handleHelpClick() {
    this.currentView = "help";
    this.isMainViewVisible = true;
  }
  handleHistoryClick() {
    this.currentView = "history";
    this.isMainViewVisible = true;
  }
  async handleClose() {
    if (window.require) {
      const { ipcRenderer: ipcRenderer2 } = window.require("electron");
      await ipcRenderer2.invoke("quit-application");
    }
  }
  handleBackClick() {
    this.currentView = "listen";
  }
  async handleSendText(message) {
    if (window.pickleGlass) {
      const result = await window.pickleGlass.sendTextMessage(message);
      if (!result.success) {
        console.error("Failed to send message:", result.error);
        this.setStatus("Error sending message: " + result.error);
      } else {
        this.setStatus("Message sent...");
      }
    }
  }
  // updateOutline(outline) {
  //     console.log('📝 PickleGlassApp updateOutline:', outline);
  //     this.outlines = [...outline];
  //     this.requestUpdate();
  // }
  // updateAnalysisRequests(requests) {
  //     console.log('📝 PickleGlassApp updateAnalysisRequests:', requests);
  //     this.analysisRequests = [...requests];
  //     this.requestUpdate();
  // }
  updateStructuredData(data) {
    console.log("\u{1F4DD} PickleGlassApp updateStructuredData:", data);
    this.structuredData = data;
    this.requestUpdate();
    const assistantView = this.shadowRoot?.querySelector("assistant-view");
    if (assistantView) {
      assistantView.structuredData = data;
      console.log("\u2705 Structured data passed to AssistantView");
    }
  }
  handleResponseIndexChanged(e2) {
    this.currentResponseIndex = e2.detail.index;
  }
  render() {
    switch (this.currentView) {
      case "listen":
        return H`<assistant-view
                    .currentResponseIndex=${this.currentResponseIndex}
                    .selectedProfile=${this.selectedProfile}
                    .structuredData=${this.structuredData}
                    .onSendText=${(message) => this.handleSendText(message)}
                    @response-index-changed=${(e2) => this.currentResponseIndex = e2.detail.index}
                ></assistant-view>`;
      case "ask":
        return H`<ask-view></ask-view>`;
      case "settings":
        return H`<settings-view
                    .selectedProfile=${this.selectedProfile}
                    .selectedLanguage=${this.selectedLanguage}
                    .onProfileChange=${(profile) => this.selectedProfile = profile}
                    .onLanguageChange=${(lang) => this.selectedLanguage = lang}
                ></settings-view>`;
      case "shortcut-settings":
        return H`<shortcut-settings-view></shortcut-settings-view>`;
      case "history":
        return H`<history-view></history-view>`;
      case "help":
        return H`<help-view></help-view>`;
      case "setup":
        return H`<setup-view></setup-view>`;
      default:
        return H`<div>Unknown view: ${this.currentView}</div>`;
    }
  }
};
customElements.define("pickle-glass-app", PickleGlassApp);
export {
  PickleGlassApp
};
/**
 * @license
 * Copyright 2019 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */
/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */
/**
 * @license
 * Copyright 2022 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */
//# sourceMappingURL=content.js.map

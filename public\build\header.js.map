{"version": 3, "sources": ["../../src/assets/lit-core-2.7.4.min.js", "../../src/app/MainHeader.js", "../../src/app/ApiKeyHeader.js", "../../src/app/PermissionHeader.js", "../../src/app/HeaderController.js"], "sourcesContent": ["/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n * SPDX-License-Identifier: BSD-3-Clause\r\n */\r\nconst t=window,i=t.ShadowRoot&&(void 0===t.ShadyCSS||t.ShadyCSS.nativeShadow)&&\"adoptedStyleSheets\"in Document.prototype&&\"replace\"in CSSStyleSheet.prototype,s=Symbol(),e=new WeakMap;class o{constructor(t,e,i){if(this._$cssResult$=!0,i!==s)throw Error(\"CSSResult is not constructable. Use `unsafeCSS` or `css` instead.\");this.cssText=t,this.t=e}get styleSheet(){let t=this.i;const s=this.t;if(i&&void 0===t){const i=void 0!==s&&1===s.length;i&&(t=e.get(s)),void 0===t&&((this.i=t=new CSSStyleSheet).replaceSync(this.cssText),i&&e.set(s,t))}return t}toString(){return this.cssText}}const n=t=>new o(\"string\"==typeof t?t:t+\"\",void 0,s),r=(t,...e)=>{const i=1===t.length?t[0]:e.reduce(((e,s,i)=>e+(t=>{if(!0===t._$cssResult$)return t.cssText;if(\"number\"==typeof t)return t;throw Error(\"Value passed to 'css' function must be a 'css' function result: \"+t+\". Use 'unsafeCSS' to pass non-literal values, but take care to ensure page security.\")})(s)+t[i+1]),t[0]);return new o(i,t,s)},h=(e,s)=>{i?e.adoptedStyleSheets=s.map((t=>t instanceof CSSStyleSheet?t:t.styleSheet)):s.forEach((s=>{const i=document.createElement(\"style\"),n=t.litNonce;void 0!==n&&i.setAttribute(\"nonce\",n),i.textContent=s.cssText,e.appendChild(i)}))},l=i?t=>t:t=>t instanceof CSSStyleSheet?(t=>{let e=\"\";for(const s of t.cssRules)e+=s.cssText;return n(e)})(t):t\r\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n * SPDX-License-Identifier: BSD-3-Clause\r\n */;var a;const u=window,c=u.trustedTypes,d=c?c.emptyScript:\"\",v=u.reactiveElementPolyfillSupport,p={toAttribute(t,e){switch(e){case Boolean:t=t?d:null;break;case Object:case Array:t=null==t?t:JSON.stringify(t)}return t},fromAttribute(t,e){let s=t;switch(e){case Boolean:s=null!==t;break;case Number:s=null===t?null:Number(t);break;case Object:case Array:try{s=JSON.parse(t)}catch(t){s=null}}return s}},f=(t,e)=>e!==t&&(e==e||t==t),m={attribute:!0,type:String,converter:p,reflect:!1,hasChanged:f},y=\"finalized\";class _ extends HTMLElement{constructor(){super(),this.o=new Map,this.isUpdatePending=!1,this.hasUpdated=!1,this.l=null,this.u()}static addInitializer(t){var e;this.finalize(),(null!==(e=this.v)&&void 0!==e?e:this.v=[]).push(t)}static get observedAttributes(){this.finalize();const t=[];return this.elementProperties.forEach(((e,s)=>{const i=this.p(s,e);void 0!==i&&(this.m.set(i,s),t.push(i))})),t}static createProperty(t,e=m){if(e.state&&(e.attribute=!1),this.finalize(),this.elementProperties.set(t,e),!e.noAccessor&&!this.prototype.hasOwnProperty(t)){const s=\"symbol\"==typeof t?Symbol():\"__\"+t,i=this.getPropertyDescriptor(t,s,e);void 0!==i&&Object.defineProperty(this.prototype,t,i)}}static getPropertyDescriptor(t,e,s){return{get(){return this[e]},set(i){const n=this[t];this[e]=i,this.requestUpdate(t,n,s)},configurable:!0,enumerable:!0}}static getPropertyOptions(t){return this.elementProperties.get(t)||m}static finalize(){if(this.hasOwnProperty(y))return!1;this[y]=!0;const t=Object.getPrototypeOf(this);if(t.finalize(),void 0!==t.v&&(this.v=[...t.v]),this.elementProperties=new Map(t.elementProperties),this.m=new Map,this.hasOwnProperty(\"properties\")){const t=this.properties,e=[...Object.getOwnPropertyNames(t),...Object.getOwnPropertySymbols(t)];for(const s of e)this.createProperty(s,t[s])}return this.elementStyles=this.finalizeStyles(this.styles),!0}static finalizeStyles(t){const e=[];if(Array.isArray(t)){const s=new Set(t.flat(1/0).reverse());for(const t of s)e.unshift(l(t))}else void 0!==t&&e.push(l(t));return e}static p(t,e){const s=e.attribute;return!1===s?void 0:\"string\"==typeof s?s:\"string\"==typeof t?t.toLowerCase():void 0}u(){var t;this._=new Promise((t=>this.enableUpdating=t)),this._$AL=new Map,this.g(),this.requestUpdate(),null===(t=this.constructor.v)||void 0===t||t.forEach((t=>t(this)))}addController(t){var e,s;(null!==(e=this.S)&&void 0!==e?e:this.S=[]).push(t),void 0!==this.renderRoot&&this.isConnected&&(null===(s=t.hostConnected)||void 0===s||s.call(t))}removeController(t){var e;null===(e=this.S)||void 0===e||e.splice(this.S.indexOf(t)>>>0,1)}g(){this.constructor.elementProperties.forEach(((t,e)=>{this.hasOwnProperty(e)&&(this.o.set(e,this[e]),delete this[e])}))}createRenderRoot(){var t;const e=null!==(t=this.shadowRoot)&&void 0!==t?t:this.attachShadow(this.constructor.shadowRootOptions);return h(e,this.constructor.elementStyles),e}connectedCallback(){var t;void 0===this.renderRoot&&(this.renderRoot=this.createRenderRoot()),this.enableUpdating(!0),null===(t=this.S)||void 0===t||t.forEach((t=>{var e;return null===(e=t.hostConnected)||void 0===e?void 0:e.call(t)}))}enableUpdating(t){}disconnectedCallback(){var t;null===(t=this.S)||void 0===t||t.forEach((t=>{var e;return null===(e=t.hostDisconnected)||void 0===e?void 0:e.call(t)}))}attributeChangedCallback(t,e,s){this._$AK(t,s)}$(t,e,s=m){var i;const n=this.constructor.p(t,s);if(void 0!==n&&!0===s.reflect){const o=(void 0!==(null===(i=s.converter)||void 0===i?void 0:i.toAttribute)?s.converter:p).toAttribute(e,s.type);this.l=t,null==o?this.removeAttribute(n):this.setAttribute(n,o),this.l=null}}_$AK(t,e){var s;const i=this.constructor,n=i.m.get(t);if(void 0!==n&&this.l!==n){const t=i.getPropertyOptions(n),o=\"function\"==typeof t.converter?{fromAttribute:t.converter}:void 0!==(null===(s=t.converter)||void 0===s?void 0:s.fromAttribute)?t.converter:p;this.l=n,this[n]=o.fromAttribute(e,t.type),this.l=null}}requestUpdate(t,e,s){let i=!0;void 0!==t&&(((s=s||this.constructor.getPropertyOptions(t)).hasChanged||f)(this[t],e)?(this._$AL.has(t)||this._$AL.set(t,e),!0===s.reflect&&this.l!==t&&(void 0===this.C&&(this.C=new Map),this.C.set(t,s))):i=!1),!this.isUpdatePending&&i&&(this._=this.T())}async T(){this.isUpdatePending=!0;try{await this._}catch(t){Promise.reject(t)}const t=this.scheduleUpdate();return null!=t&&await t,!this.isUpdatePending}scheduleUpdate(){return this.performUpdate()}performUpdate(){var t;if(!this.isUpdatePending)return;this.hasUpdated,this.o&&(this.o.forEach(((t,e)=>this[e]=t)),this.o=void 0);let e=!1;const s=this._$AL;try{e=this.shouldUpdate(s),e?(this.willUpdate(s),null===(t=this.S)||void 0===t||t.forEach((t=>{var e;return null===(e=t.hostUpdate)||void 0===e?void 0:e.call(t)})),this.update(s)):this.P()}catch(t){throw e=!1,this.P(),t}e&&this._$AE(s)}willUpdate(t){}_$AE(t){var e;null===(e=this.S)||void 0===e||e.forEach((t=>{var e;return null===(e=t.hostUpdated)||void 0===e?void 0:e.call(t)})),this.hasUpdated||(this.hasUpdated=!0,this.firstUpdated(t)),this.updated(t)}P(){this._$AL=new Map,this.isUpdatePending=!1}get updateComplete(){return this.getUpdateComplete()}getUpdateComplete(){return this._}shouldUpdate(t){return!0}update(t){void 0!==this.C&&(this.C.forEach(((t,e)=>this.$(e,this[e],t))),this.C=void 0),this.P()}updated(t){}firstUpdated(t){}}\r\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n * SPDX-License-Identifier: BSD-3-Clause\r\n */var b;_[y]=!0,_.elementProperties=new Map,_.elementStyles=[],_.shadowRootOptions={mode:\"open\"},null==v||v({ReactiveElement:_}),(null!==(a=u.reactiveElementVersions)&&void 0!==a?a:u.reactiveElementVersions=[]).push(\"1.6.1\");const g=window,w=g.trustedTypes,S=w?w.createPolicy(\"lit-html\",{createHTML:t=>t}):void 0,$=\"$lit$\",C=`lit$${(Math.random()+\"\").slice(9)}$`,T=\"?\"+C,P=`<${T}>`,x=document,A=()=>x.createComment(\"\"),k=t=>null===t||\"object\"!=typeof t&&\"function\"!=typeof t,E=Array.isArray,M=t=>E(t)||\"function\"==typeof(null==t?void 0:t[Symbol.iterator]),U=\"[ \\t\\n\\f\\r]\",N=/<(?:(!--|\\/[^a-zA-Z])|(\\/?[a-zA-Z][^>\\s]*)|(\\/?$))/g,R=/-->/g,O=/>/g,V=RegExp(`>|${U}(?:([^\\\\s\"'>=/]+)(${U}*=${U}*(?:[^ \\t\\n\\f\\r\"'\\`<>=]|(\"|')|))|$)`,\"g\"),j=/'/g,z=/\"/g,L=/^(?:script|style|textarea|title)$/i,I=t=>(e,...s)=>({_$litType$:t,strings:e,values:s}),H=I(1),B=I(2),D=Symbol.for(\"lit-noChange\"),q=Symbol.for(\"lit-nothing\"),J=new WeakMap,W=x.createTreeWalker(x,129,null,!1),Z=(t,e)=>{const s=t.length-1,i=[];let n,o=2===e?\"<svg>\":\"\",r=N;for(let e=0;e<s;e++){const s=t[e];let l,h,a=-1,d=0;for(;d<s.length&&(r.lastIndex=d,h=r.exec(s),null!==h);)d=r.lastIndex,r===N?\"!--\"===h[1]?r=R:void 0!==h[1]?r=O:void 0!==h[2]?(L.test(h[2])&&(n=RegExp(\"</\"+h[2],\"g\")),r=V):void 0!==h[3]&&(r=V):r===V?\">\"===h[0]?(r=null!=n?n:N,a=-1):void 0===h[1]?a=-2:(a=r.lastIndex-h[2].length,l=h[1],r=void 0===h[3]?V:'\"'===h[3]?z:j):r===z||r===j?r=V:r===R||r===O?r=N:(r=V,n=void 0);const c=r===V&&t[e+1].startsWith(\"/>\")?\" \":\"\";o+=r===N?s+P:a>=0?(i.push(l),s.slice(0,a)+$+s.slice(a)+C+c):s+C+(-2===a?(i.push(void 0),e):c)}const l=o+(t[s]||\"<?>\")+(2===e?\"</svg>\":\"\");if(!Array.isArray(t)||!t.hasOwnProperty(\"raw\"))throw Error(\"invalid template strings array\");return[void 0!==S?S.createHTML(l):l,i]};class F{constructor({strings:t,_$litType$:e},s){let i;this.parts=[];let n=0,o=0;const r=t.length-1,l=this.parts,[h,a]=Z(t,e);if(this.el=F.createElement(h,s),W.currentNode=this.el.content,2===e){const t=this.el.content,e=t.firstChild;e.remove(),t.append(...e.childNodes)}for(;null!==(i=W.nextNode())&&l.length<r;){if(1===i.nodeType){if(i.hasAttributes()){const t=[];for(const e of i.getAttributeNames())if(e.endsWith($)||e.startsWith(C)){const s=a[o++];if(t.push(e),void 0!==s){const t=i.getAttribute(s.toLowerCase()+$).split(C),e=/([.?@])?(.*)/.exec(s);l.push({type:1,index:n,name:e[2],strings:t,ctor:\".\"===e[1]?Y:\"?\"===e[1]?it:\"@\"===e[1]?st:X})}else l.push({type:6,index:n})}for(const e of t)i.removeAttribute(e)}if(L.test(i.tagName)){const t=i.textContent.split(C),e=t.length-1;if(e>0){i.textContent=w?w.emptyScript:\"\";for(let s=0;s<e;s++)i.append(t[s],A()),W.nextNode(),l.push({type:2,index:++n});i.append(t[e],A())}}}else if(8===i.nodeType)if(i.data===T)l.push({type:2,index:n});else{let t=-1;for(;-1!==(t=i.data.indexOf(C,t+1));)l.push({type:7,index:n}),t+=C.length-1}n++}}static createElement(t,e){const s=x.createElement(\"template\");return s.innerHTML=t,s}}function G(t,e,s=t,i){var n,o,r,l;if(e===D)return e;let h=void 0!==i?null===(n=s.A)||void 0===n?void 0:n[i]:s.k;const a=k(e)?void 0:e._$litDirective$;return(null==h?void 0:h.constructor)!==a&&(null===(o=null==h?void 0:h._$AO)||void 0===o||o.call(h,!1),void 0===a?h=void 0:(h=new a(t),h._$AT(t,s,i)),void 0!==i?(null!==(r=(l=s).A)&&void 0!==r?r:l.A=[])[i]=h:s.k=h),void 0!==h&&(e=G(t,h._$AS(t,e.values),h,i)),e}class K{constructor(t,e){this._$AV=[],this._$AN=void 0,this._$AD=t,this._$AM=e}get parentNode(){return this._$AM.parentNode}get _$AU(){return this._$AM._$AU}M(t){var e;const{el:{content:s},parts:i}=this._$AD,n=(null!==(e=null==t?void 0:t.creationScope)&&void 0!==e?e:x).importNode(s,!0);W.currentNode=n;let o=W.nextNode(),r=0,l=0,h=i[0];for(;void 0!==h;){if(r===h.index){let e;2===h.type?e=new Q(o,o.nextSibling,this,t):1===h.type?e=new h.ctor(o,h.name,h.strings,this,t):6===h.type&&(e=new et(o,this,t)),this._$AV.push(e),h=i[++l]}r!==(null==h?void 0:h.index)&&(o=W.nextNode(),r++)}return n}U(t){let e=0;for(const s of this._$AV)void 0!==s&&(void 0!==s.strings?(s._$AI(t,s,e),e+=s.strings.length-2):s._$AI(t[e])),e++}}class Q{constructor(t,e,s,i){var n;this.type=2,this._$AH=q,this._$AN=void 0,this._$AA=t,this._$AB=e,this._$AM=s,this.options=i,this.N=null===(n=null==i?void 0:i.isConnected)||void 0===n||n}get _$AU(){var t,e;return null!==(e=null===(t=this._$AM)||void 0===t?void 0:t._$AU)&&void 0!==e?e:this.N}get parentNode(){let t=this._$AA.parentNode;const e=this._$AM;return void 0!==e&&11===(null==t?void 0:t.nodeType)&&(t=e.parentNode),t}get startNode(){return this._$AA}get endNode(){return this._$AB}_$AI(t,e=this){t=G(this,t,e),k(t)?t===q||null==t||\"\"===t?(this._$AH!==q&&this._$AR(),this._$AH=q):t!==this._$AH&&t!==D&&this.R(t):void 0!==t._$litType$?this.O(t):void 0!==t.nodeType?this.V(t):M(t)?this.j(t):this.R(t)}L(t){return this._$AA.parentNode.insertBefore(t,this._$AB)}V(t){this._$AH!==t&&(this._$AR(),this._$AH=this.L(t))}R(t){this._$AH!==q&&k(this._$AH)?this._$AA.nextSibling.data=t:this.V(x.createTextNode(t)),this._$AH=t}O(t){var e;const{values:s,_$litType$:i}=t,n=\"number\"==typeof i?this._$AC(t):(void 0===i.el&&(i.el=F.createElement(i.h,this.options)),i);if((null===(e=this._$AH)||void 0===e?void 0:e._$AD)===n)this._$AH.U(s);else{const t=new K(n,this),e=t.M(this.options);t.U(s),this.V(e),this._$AH=t}}_$AC(t){let e=J.get(t.strings);return void 0===e&&J.set(t.strings,e=new F(t)),e}j(t){E(this._$AH)||(this._$AH=[],this._$AR());const e=this._$AH;let s,i=0;for(const n of t)i===e.length?e.push(s=new Q(this.L(A()),this.L(A()),this,this.options)):s=e[i],s._$AI(n),i++;i<e.length&&(this._$AR(s&&s._$AB.nextSibling,i),e.length=i)}_$AR(t=this._$AA.nextSibling,e){var s;for(null===(s=this._$AP)||void 0===s||s.call(this,!1,!0,e);t&&t!==this._$AB;){const e=t.nextSibling;t.remove(),t=e}}setConnected(t){var e;void 0===this._$AM&&(this.N=t,null===(e=this._$AP)||void 0===e||e.call(this,t))}}class X{constructor(t,e,s,i,n){this.type=1,this._$AH=q,this._$AN=void 0,this.element=t,this.name=e,this._$AM=i,this.options=n,s.length>2||\"\"!==s[0]||\"\"!==s[1]?(this._$AH=Array(s.length-1).fill(new String),this.strings=s):this._$AH=q}get tagName(){return this.element.tagName}get _$AU(){return this._$AM._$AU}_$AI(t,e=this,s,i){const n=this.strings;let o=!1;if(void 0===n)t=G(this,t,e,0),o=!k(t)||t!==this._$AH&&t!==D,o&&(this._$AH=t);else{const i=t;let r,l;for(t=n[0],r=0;r<n.length-1;r++)l=G(this,i[s+r],e,r),l===D&&(l=this._$AH[r]),o||(o=!k(l)||l!==this._$AH[r]),l===q?t=q:t!==q&&(t+=(null!=l?l:\"\")+n[r+1]),this._$AH[r]=l}o&&!i&&this.I(t)}I(t){t===q?this.element.removeAttribute(this.name):this.element.setAttribute(this.name,null!=t?t:\"\")}}class Y extends X{constructor(){super(...arguments),this.type=3}I(t){this.element[this.name]=t===q?void 0:t}}const tt=w?w.emptyScript:\"\";class it extends X{constructor(){super(...arguments),this.type=4}I(t){t&&t!==q?this.element.setAttribute(this.name,tt):this.element.removeAttribute(this.name)}}class st extends X{constructor(t,e,s,i,n){super(t,e,s,i,n),this.type=5}_$AI(t,e=this){var s;if((t=null!==(s=G(this,t,e,0))&&void 0!==s?s:q)===D)return;const i=this._$AH,n=t===q&&i!==q||t.capture!==i.capture||t.once!==i.once||t.passive!==i.passive,o=t!==q&&(i===q||n);n&&this.element.removeEventListener(this.name,this,i),o&&this.element.addEventListener(this.name,this,t),this._$AH=t}handleEvent(t){var e,s;\"function\"==typeof this._$AH?this._$AH.call(null!==(s=null===(e=this.options)||void 0===e?void 0:e.host)&&void 0!==s?s:this.element,t):this._$AH.handleEvent(t)}}class et{constructor(t,e,s){this.element=t,this.type=6,this._$AN=void 0,this._$AM=e,this.options=s}get _$AU(){return this._$AM._$AU}_$AI(t){G(this,t)}}const ot={H:$,B:C,D:T,q:1,J:Z,W:K,Z:M,F:G,G:Q,K:X,X:it,Y:st,tt:Y,it:et},nt=g.litHtmlPolyfillSupport;null==nt||nt(F,Q),(null!==(b=g.litHtmlVersions)&&void 0!==b?b:g.litHtmlVersions=[]).push(\"2.7.3\");const rt=(t,e,s)=>{var i,n;const o=null!==(i=null==s?void 0:s.renderBefore)&&void 0!==i?i:e;let r=o._$litPart$;if(void 0===r){const t=null!==(n=null==s?void 0:s.renderBefore)&&void 0!==n?n:null;o._$litPart$=r=new Q(e.insertBefore(A(),t),t,void 0,null!=s?s:{})}return r._$AI(t),r};\r\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n * SPDX-License-Identifier: BSD-3-Clause\r\n */var ht,lt;const at=_;class ut extends _{constructor(){super(...arguments),this.renderOptions={host:this},this.st=void 0}createRenderRoot(){var t,e;const s=super.createRenderRoot();return null!==(t=(e=this.renderOptions).renderBefore)&&void 0!==t||(e.renderBefore=s.firstChild),s}update(t){const e=this.render();this.hasUpdated||(this.renderOptions.isConnected=this.isConnected),super.update(t),this.st=rt(e,this.renderRoot,this.renderOptions)}connectedCallback(){var t;super.connectedCallback(),null===(t=this.st)||void 0===t||t.setConnected(!0)}disconnectedCallback(){var t;super.disconnectedCallback(),null===(t=this.st)||void 0===t||t.setConnected(!1)}render(){return D}}ut.finalized=!0,ut._$litElement$=!0,null===(ht=globalThis.litElementHydrateSupport)||void 0===ht||ht.call(globalThis,{LitElement:ut});const ct=globalThis.litElementPolyfillSupport;null==ct||ct({LitElement:ut});const dt={_$AK:(t,e,s)=>{t._$AK(e,s)},_$AL:t=>t._$AL};(null!==(lt=globalThis.litElementVersions)&&void 0!==lt?lt:globalThis.litElementVersions=[]).push(\"3.3.2\");\r\n/**\r\n * @license\r\n * Copyright 2022 Google LLC\r\n * SPDX-License-Identifier: BSD-3-Clause\r\n */\r\nconst vt=!1;export{o as CSSResult,ut as LitElement,_ as ReactiveElement,at as UpdatingElement,dt as _$LE,ot as _$LH,h as adoptStyles,r as css,p as defaultConverter,l as getCompatibleStyle,H as html,vt as isServer,D as noChange,f as notEqual,q as nothing,rt as render,i as supportsAdoptingStyleSheets,B as svg,n as unsafeCSS};\r\n", "import { html, css, LitElement } from '../assets/lit-core-2.7.4.min.js';\r\n\r\nexport class MainHeader extends LitElement {\r\n    static properties = {\r\n        isSessionActive: { type: Boolean, state: true },\r\n        shortcuts: { type: Object, state: true },\r\n    };\r\n\r\n    static styles = css`\r\n        :host {\r\n            display: flex;\r\n            transform: translate3d(0, 0, 0);\r\n            backface-visibility: hidden;\r\n            transition: transform 0.2s cubic-bezier(0.23, 1, 0.32, 1), opacity 0.2s ease-out;\r\n            will-change: transform, opacity;\r\n        }\r\n\r\n        :host(.hiding) {\r\n            animation: slideUp 0.3s cubic-bezier(0.4, 0, 0.6, 1) forwards;\r\n        }\r\n\r\n        :host(.showing) {\r\n            animation: slideDown 0.35s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;\r\n        }\r\n\r\n        :host(.sliding-in) {\r\n            animation: fadeIn 0.2s ease-out forwards;\r\n        }\r\n\r\n        :host(.hidden) {\r\n            opacity: 0;\r\n            transform: translateY(-150%) scale(0.85);\r\n            pointer-events: none;\r\n        }\r\n\r\n        @keyframes slideUp {\r\n            0% {\r\n                opacity: 1;\r\n                transform: translateY(0) scale(1);\r\n                filter: blur(0px);\r\n            }\r\n            30% {\r\n                opacity: 0.7;\r\n                transform: translateY(-20%) scale(0.98);\r\n                filter: blur(0.5px);\r\n            }\r\n            70% {\r\n                opacity: 0.3;\r\n                transform: translateY(-80%) scale(0.92);\r\n                filter: blur(1.5px);\r\n            }\r\n            100% {\r\n                opacity: 0;\r\n                transform: translateY(-150%) scale(0.85);\r\n                filter: blur(2px);\r\n            }\r\n        }\r\n\r\n        @keyframes slideDown {\r\n            0% {\r\n                opacity: 0;\r\n                transform: translateY(-150%) scale(0.85);\r\n                filter: blur(2px);\r\n            }\r\n            30% {\r\n                opacity: 0.5;\r\n                transform: translateY(-50%) scale(0.92);\r\n                filter: blur(1px);\r\n            }\r\n            65% {\r\n                opacity: 0.9;\r\n                transform: translateY(-5%) scale(0.99);\r\n                filter: blur(0.2px);\r\n            }\r\n            85% {\r\n                opacity: 0.98;\r\n                transform: translateY(2%) scale(1.005);\r\n                filter: blur(0px);\r\n            }\r\n            100% {\r\n                opacity: 1;\r\n                transform: translateY(0) scale(1);\r\n                filter: blur(0px);\r\n            }\r\n        }\r\n\r\n        @keyframes fadeIn {\r\n            0% {\r\n                opacity: 0;\r\n            }\r\n            100% {\r\n                opacity: 1;\r\n            }\r\n        }\r\n\r\n        * {\r\n            font-family: 'Helvetica Neue', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\r\n            cursor: default;\r\n            user-select: none;\r\n        }\r\n\r\n        .header {\r\n            width: max-content;\r\n            height: 47px;\r\n            padding: 2px 10px 2px 13px;\r\n            background: transparent;\r\n            overflow: hidden;\r\n            border-radius: 9000px;\r\n            /* backdrop-filter: blur(1px); */\r\n            justify-content: space-between;\r\n            align-items: center;\r\n            display: inline-flex;\r\n            box-sizing: border-box;\r\n            position: relative;\r\n        }\r\n\r\n        .header::before {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0; left: 0; right: 0; bottom: 0;\r\n            width: 100%;\r\n            height: 100%;\r\n            background: rgba(0, 0, 0, 0.6);\r\n            border-radius: 9000px;\r\n            z-index: -1;\r\n        }\r\n\r\n        .header::after {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0; left: 0; right: 0; bottom: 0;\r\n            border-radius: 9000px;\r\n            padding: 1px;\r\n            background: linear-gradient(169deg, rgba(255, 255, 255, 0.17) 0%, rgba(255, 255, 255, 0.08) 50%, rgba(255, 255, 255, 0.17) 100%); \r\n            -webkit-mask:\r\n                linear-gradient(#fff 0 0) content-box,\r\n                linear-gradient(#fff 0 0);\r\n            -webkit-mask-composite: destination-out;\r\n            mask-composite: exclude;\r\n            pointer-events: none;\r\n        }\r\n\r\n        .listen-button {\r\n            height: 26px;\r\n            padding: 0 13px;\r\n            background: transparent;\r\n            border-radius: 9000px;\r\n            justify-content: center;\r\n            width: 78px;\r\n            align-items: center;\r\n            gap: 6px;\r\n            display: flex;\r\n            border: none;\r\n            cursor: pointer;\r\n            position: relative;\r\n        }\r\n\r\n        .listen-button.active::before {\r\n            background: rgba(215, 0, 0, 0.5);\r\n        }\r\n\r\n        .listen-button.active:hover::before {\r\n            background: rgba(255, 20, 20, 0.6);\r\n        }\r\n\r\n        .listen-button:hover::before {\r\n            background: rgba(255, 255, 255, 0.18);\r\n        }\r\n\r\n        .listen-button::before {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0; left: 0; right: 0; bottom: 0;\r\n            background: rgba(255, 255, 255, 0.14);\r\n            border-radius: 9000px;\r\n            z-index: -1;\r\n            transition: background 0.15s ease;\r\n        }\r\n\r\n        .listen-button::after {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0; left: 0; right: 0; bottom: 0;\r\n            border-radius: 9000px;\r\n            padding: 1px;\r\n            background: linear-gradient(169deg, rgba(255, 255, 255, 0.17) 0%, rgba(255, 255, 255, 0.08) 50%, rgba(255, 255, 255, 0.17) 100%);\r\n            -webkit-mask:\r\n                linear-gradient(#fff 0 0) content-box,\r\n                linear-gradient(#fff 0 0);\r\n            -webkit-mask-composite: destination-out;\r\n            mask-composite: exclude;\r\n            pointer-events: none;\r\n        }\r\n\r\n        .header-actions {\r\n            height: 26px;\r\n            box-sizing: border-box;\r\n            justify-content: flex-start;\r\n            align-items: center;\r\n            gap: 9px;\r\n            display: flex;\r\n            padding: 0 8px;\r\n            border-radius: 6px;\r\n            transition: background 0.15s ease;\r\n        }\r\n\r\n        .header-actions:hover {\r\n            background: rgba(255, 255, 255, 0.1);\r\n        }\r\n\r\n        .ask-action {\r\n            margin-left: 4px;\r\n        }\r\n\r\n        .action-button,\r\n        .action-text {\r\n            padding-bottom: 1px;\r\n            justify-content: center;\r\n            align-items: center;\r\n            gap: 10px;\r\n            display: flex;\r\n        }\r\n\r\n        .action-text-content {\r\n            color: white;\r\n            font-size: 12px;\r\n            font-family: 'Helvetica Neue', sans-serif;\r\n            font-weight: 500; /* Medium */\r\n            word-wrap: break-word;\r\n        }\r\n\r\n        .icon-container {\r\n            justify-content: flex-start;\r\n            align-items: center;\r\n            gap: 4px;\r\n            display: flex;\r\n        }\r\n\r\n        .icon-container.ask-icons svg,\r\n        .icon-container.showhide-icons svg {\r\n            width: 12px;\r\n            height: 12px;\r\n        }\r\n\r\n        .listen-icon svg {\r\n            width: 12px;\r\n            height: 11px;\r\n            position: relative;\r\n            top: 1px;\r\n        }\r\n\r\n        .icon-box {\r\n            color: white;\r\n            font-size: 12px;\r\n            font-family: 'Helvetica Neue', sans-serif;\r\n            font-weight: 500;\r\n            background-color: rgba(255, 255, 255, 0.1);\r\n            border-radius: 13%;\r\n            width: 18px;\r\n            height: 18px;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n        }\r\n\r\n        .settings-button {\r\n            padding: 5px;\r\n            border-radius: 50%;\r\n            background: transparent;\r\n            transition: background 0.15s ease;\r\n            color: white;\r\n            border: none;\r\n            cursor: pointer;\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 6px;\r\n        }\r\n\r\n        .settings-button:hover {\r\n            background: rgba(255, 255, 255, 0.1);\r\n        }\r\n\r\n        .settings-icon {\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            padding: 3px;\r\n        }\r\n\r\n        .settings-icon svg {\r\n            width: 16px;\r\n            height: 16px;\r\n        }\r\n\r\n        /* ────────────────[ GLASS BYPASS ]─────────────── */\r\n        :host-context(body.has-glass) .header,\r\n        :host-context(body.has-glass) .listen-button,\r\n        :host-context(body.has-glass) .header-actions,\r\n        :host-context(body.has-glass) .settings-button {\r\n            background: transparent !important;\r\n            filter: none !important;\r\n            box-shadow: none !important;\r\n            backdrop-filter: none !important;\r\n        }\r\n        :host-context(body.has-glass) .icon-box {\r\n            background: transparent !important;\r\n            border: none !important;\r\n        }\r\n\r\n        :host-context(body.has-glass) .header::before,\r\n        :host-context(body.has-glass) .header::after,\r\n        :host-context(body.has-glass) .listen-button::before,\r\n        :host-context(body.has-glass) .listen-button::after {\r\n            display: none !important;\r\n        }\r\n\r\n        :host-context(body.has-glass) .header-actions:hover,\r\n        :host-context(body.has-glass) .settings-button:hover,\r\n        :host-context(body.has-glass) .listen-button:hover::before {\r\n            background: transparent !important;\r\n        }\r\n        :host-context(body.has-glass) * {\r\n            animation: none !important;\r\n            transition: none !important;\r\n            transform: none !important;\r\n            filter: none !important;\r\n            backdrop-filter: none !important;\r\n            box-shadow: none !important;\r\n        }\r\n\r\n        :host-context(body.has-glass) .header,\r\n        :host-context(body.has-glass) .listen-button,\r\n        :host-context(body.has-glass) .header-actions,\r\n        :host-context(body.has-glass) .settings-button,\r\n        :host-context(body.has-glass) .icon-box {\r\n            border-radius: 0 !important;\r\n        }\r\n        :host-context(body.has-glass) {\r\n            animation: none !important;\r\n            transition: none !important;\r\n            transform: none !important;\r\n            will-change: auto !important;\r\n        }\r\n        `;\r\n\r\n    constructor() {\r\n        super();\r\n        this.shortcuts = {};\r\n        this.dragState = null;\r\n        this.wasJustDragged = false;\r\n        this.isVisible = true;\r\n        this.isAnimating = false;\r\n        this.hasSlidIn = false;\r\n        this.settingsHideTimer = null;\r\n        this.isSessionActive = false;\r\n        this.animationEndTimer = null;\r\n        this.handleMouseMove = this.handleMouseMove.bind(this);\r\n        this.handleMouseUp = this.handleMouseUp.bind(this);\r\n        this.handleAnimationEnd = this.handleAnimationEnd.bind(this);\r\n    }\r\n\r\n    async handleMouseDown(e) {\r\n        e.preventDefault();\r\n\r\n        const { ipcRenderer } = window.require('electron');\r\n        const initialPosition = await ipcRenderer.invoke('get-header-position');\r\n\r\n        this.dragState = {\r\n            initialMouseX: e.screenX,\r\n            initialMouseY: e.screenY,\r\n            initialWindowX: initialPosition.x,\r\n            initialWindowY: initialPosition.y,\r\n            moved: false,\r\n        };\r\n\r\n        window.addEventListener('mousemove', this.handleMouseMove, { capture: true });\r\n        window.addEventListener('mouseup', this.handleMouseUp, { once: true, capture: true });\r\n    }\r\n\r\n    handleMouseMove(e) {\r\n        if (!this.dragState) return;\r\n\r\n        const deltaX = Math.abs(e.screenX - this.dragState.initialMouseX);\r\n        const deltaY = Math.abs(e.screenY - this.dragState.initialMouseY);\r\n        \r\n        if (deltaX > 3 || deltaY > 3) {\r\n            this.dragState.moved = true;\r\n        }\r\n\r\n        const newWindowX = this.dragState.initialWindowX + (e.screenX - this.dragState.initialMouseX);\r\n        const newWindowY = this.dragState.initialWindowY + (e.screenY - this.dragState.initialMouseY);\r\n\r\n        const { ipcRenderer } = window.require('electron');\r\n        ipcRenderer.invoke('move-header-to', newWindowX, newWindowY);\r\n    }\r\n\r\n    handleMouseUp(e) {\r\n        if (!this.dragState) return;\r\n\r\n        const wasDragged = this.dragState.moved;\r\n\r\n        window.removeEventListener('mousemove', this.handleMouseMove, { capture: true });\r\n        this.dragState = null;\r\n\r\n        if (wasDragged) {\r\n            this.wasJustDragged = true;\r\n            setTimeout(() => {\r\n                this.wasJustDragged = false;\r\n            }, 0);\r\n        }\r\n    }\r\n\r\n    toggleVisibility() {\r\n        if (this.isAnimating) {\r\n            console.log('[MainHeader] Animation already in progress, ignoring toggle');\r\n            return;\r\n        }\r\n        \r\n        if (this.animationEndTimer) {\r\n            clearTimeout(this.animationEndTimer);\r\n            this.animationEndTimer = null;\r\n        }\r\n        \r\n        this.isAnimating = true;\r\n        \r\n        if (this.isVisible) {\r\n            this.hide();\r\n        } else {\r\n            this.show();\r\n        }\r\n    }\r\n\r\n    hide() {\r\n        this.classList.remove('showing', 'hidden');\r\n        this.classList.add('hiding');\r\n        this.isVisible = false;\r\n        \r\n        this.animationEndTimer = setTimeout(() => {\r\n            if (this.classList.contains('hiding')) {\r\n                this.handleAnimationEnd({ target: this });\r\n            }\r\n        }, 350);\r\n    }\r\n\r\n    show() {\r\n        this.classList.remove('hiding', 'hidden');\r\n        this.classList.add('showing');\r\n        this.isVisible = true;\r\n        \r\n        this.animationEndTimer = setTimeout(() => {\r\n            if (this.classList.contains('showing')) {\r\n                this.handleAnimationEnd({ target: this });\r\n            }\r\n        }, 400);\r\n    }\r\n\r\n    handleAnimationEnd(e) {\r\n        if (e.target !== this) return;\r\n        \r\n        if (this.animationEndTimer) {\r\n            clearTimeout(this.animationEndTimer);\r\n            this.animationEndTimer = null;\r\n        }\r\n        \r\n        this.isAnimating = false;\r\n        \r\n        if (this.classList.contains('hiding')) {\r\n            this.classList.remove('hiding');\r\n            this.classList.add('hidden');\r\n            \r\n            if (window.require) {\r\n                const { ipcRenderer } = window.require('electron');\r\n                ipcRenderer.send('header-animation-complete', 'hidden');\r\n            }\r\n        } else if (this.classList.contains('showing')) {\r\n            this.classList.remove('showing');\r\n            \r\n            if (window.require) {\r\n                const { ipcRenderer } = window.require('electron');\r\n                ipcRenderer.send('header-animation-complete', 'visible');\r\n            }\r\n        } else if (this.classList.contains('sliding-in')) {\r\n            this.classList.remove('sliding-in');\r\n            this.hasSlidIn = true;\r\n            console.log('[MainHeader] Slide-in animation completed');\r\n        }\r\n    }\r\n\r\n    startSlideInAnimation() {\r\n        if (this.hasSlidIn) return;\r\n        this.classList.add('sliding-in');\r\n    }\r\n\r\n    connectedCallback() {\r\n        super.connectedCallback();\r\n        this.addEventListener('animationend', this.handleAnimationEnd);\r\n\r\n        if (window.require) {\r\n            const { ipcRenderer } = window.require('electron');\r\n            this._sessionStateListener = (event, { isActive }) => {\r\n                this.isSessionActive = isActive;\r\n            };\r\n            ipcRenderer.on('session-state-changed', this._sessionStateListener);\r\n            this._shortcutListener = (event, keybinds) => {\r\n                console.log('[MainHeader] Received updated shortcuts:', keybinds);\r\n                this.shortcuts = keybinds;\r\n            };\r\n            ipcRenderer.on('shortcuts-updated', this._shortcutListener);\r\n        }\r\n    }\r\n\r\n    disconnectedCallback() {\r\n        super.disconnectedCallback();\r\n        this.removeEventListener('animationend', this.handleAnimationEnd);\r\n        \r\n        if (this.animationEndTimer) {\r\n            clearTimeout(this.animationEndTimer);\r\n            this.animationEndTimer = null;\r\n        }\r\n        \r\n        if (window.require) {\r\n            const { ipcRenderer } = window.require('electron');\r\n            if (this._sessionStateListener) {\r\n                ipcRenderer.removeListener('session-state-changed', this._sessionStateListener);\r\n            }\r\n            if (this._shortcutListener) {\r\n                ipcRenderer.removeListener('shortcuts-updated', this._shortcutListener);\r\n            }\r\n        }\r\n    }\r\n\r\n    invoke(channel, ...args) {\r\n        if (this.wasJustDragged) {\r\n            return;\r\n        }\r\n        if (window.require) {\r\n            window.require('electron').ipcRenderer.invoke(channel, ...args);\r\n        }\r\n    }\r\n\r\n    showWindow(name, element) {\r\n        if (this.wasJustDragged) return;\r\n        if (window.require) {\r\n            const { ipcRenderer } = window.require('electron');\r\n            console.log(`[MainHeader] showWindow('${name}') called at ${Date.now()}`);\r\n            \r\n            ipcRenderer.send('cancel-hide-window', name);\r\n\r\n            if (name === 'settings' && element) {\r\n                const rect = element.getBoundingClientRect();\r\n                ipcRenderer.send('show-window', {\r\n                    name: 'settings',\r\n                    bounds: {\r\n                        x: rect.left,\r\n                        y: rect.top,\r\n                        width: rect.width,\r\n                        height: rect.height\r\n                    }\r\n                });\r\n            } else {\r\n                ipcRenderer.send('show-window', name);\r\n            }\r\n        }\r\n    }\r\n\r\n    hideWindow(name) {\r\n        if (this.wasJustDragged) return;\r\n        if (window.require) {\r\n            console.log(`[MainHeader] hideWindow('${name}') called at ${Date.now()}`);\r\n            window.require('electron').ipcRenderer.send('hide-window', name);\r\n        }\r\n    }\r\n\r\n    cancelHideWindow(name) {\r\n\r\n    }\r\n\r\n    renderShortcut(accelerator) {\r\n        if (!accelerator) return html``;\r\n\r\n        const keyMap = {\r\n            'Cmd': '⌘', 'Command': '⌘',\r\n            'Ctrl': '⌃', 'Control': '⌃',\r\n            'Alt': '⌥', 'Option': '⌥',\r\n            'Shift': '⇧',\r\n            'Enter': '↵',\r\n            'Backspace': '⌫',\r\n            'Delete': '⌦',\r\n            'Tab': '⇥',\r\n            'Escape': '⎋',\r\n            'Up': '↑', 'Down': '↓', 'Left': '←', 'Right': '→',\r\n            '\\\\': html`<svg viewBox=\"0 0 6 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" style=\"width:6px; height:12px;\"><path d=\"M1.5 1.3L5.1 10.6\" stroke=\"white\" stroke-width=\"1.4\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>`,\r\n        };\r\n\r\n        const keys = accelerator.split('+');\r\n        return html`${keys.map(key => html`\r\n            <div class=\"icon-box\">${keyMap[key] || key}</div>\r\n        `)}`;\r\n    }\r\n\r\n    render() {\r\n        return html`\r\n            <div class=\"header\" @mousedown=${this.handleMouseDown}>\r\n                <button \r\n                    class=\"listen-button ${this.isSessionActive ? 'active' : ''}\"\r\n                    @click=${() => this.invoke(this.isSessionActive ? 'close-session' : 'toggle-feature', 'listen')}\r\n                >\r\n                    <div class=\"action-text\">\r\n                        <div class=\"action-text-content\">${this.isSessionActive ? 'Stop' : 'Listen'}</div>\r\n                    </div>\r\n                    <div class=\"listen-icon\">\r\n                        ${this.isSessionActive\r\n                            ? html`\r\n                                <svg width=\"9\" height=\"9\" viewBox=\"0 0 9 9\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                                    <rect width=\"9\" height=\"9\" rx=\"1\" fill=\"white\"/>\r\n                                </svg>\r\n\r\n                            `\r\n                            : html`\r\n                                <svg width=\"12\" height=\"11\" viewBox=\"0 0 12 11\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                                    <path d=\"M1.69922 2.7515C1.69922 2.37153 2.00725 2.0635 2.38722 2.0635H2.73122C3.11119 2.0635 3.41922 2.37153 3.41922 2.7515V8.2555C3.41922 8.63547 3.11119 8.9435 2.73122 8.9435H2.38722C2.00725 8.9435 1.69922 8.63547 1.69922 8.2555V2.7515Z\" fill=\"white\"/>\r\n                                    <path d=\"M5.13922 1.3755C5.13922 0.995528 5.44725 0.6875 5.82722 0.6875H6.17122C6.55119 0.6875 6.85922 0.995528 6.85922 1.3755V9.6315C6.85922 10.0115 6.55119 10.3195 6.17122 10.3195H5.82722C5.44725 10.3195 5.13922 10.0115 5.13922 9.6315V1.3755Z\" fill=\"white\"/>\r\n                                    <path d=\"M8.57922 3.0955C8.57922 2.71553 8.88725 2.4075 9.26722 2.4075H9.61122C9.99119 2.4075 10.2992 2.71553 10.2992 3.0955V7.9115C10.2992 8.29147 9.99119 8.5995 9.61122 8.5995H9.26722C8.88725 8.5995 8.57922 8.29147 8.57922 7.9115V3.0955Z\" fill=\"white\"/>\r\n                                </svg>\r\n                            `}\r\n                    </div>\r\n                </button>\r\n\r\n                <div class=\"header-actions ask-action\" @click=${() => this.invoke('toggle-feature', 'ask')}>\r\n                    <div class=\"action-text\">\r\n                        <div class=\"action-text-content\">Ask</div>\r\n                    </div>\r\n                    <div class=\"icon-container\">\r\n                        ${this.renderShortcut(this.shortcuts.nextStep)}\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"header-actions\" @click=${() => this.invoke('toggle-all-windows-visibility')}>\r\n                    <div class=\"action-text\">\r\n                        <div class=\"action-text-content\">Show/Hide</div>\r\n                    </div>\r\n                    <div class=\"icon-container\">\r\n                        ${this.renderShortcut(this.shortcuts.toggleVisibility)}\r\n                    </div>\r\n                </div>\r\n\r\n                <button \r\n                    class=\"settings-button\"\r\n                    @mouseenter=${(e) => this.showWindow('settings', e.currentTarget)}\r\n                    @mouseleave=${() => this.hideWindow('settings')}\r\n                >\r\n                    <div class=\"settings-icon\">\r\n                        <svg width=\"16\" height=\"17\" viewBox=\"0 0 16 17\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                            <path d=\"M8.0013 3.16406C7.82449 3.16406 7.65492 3.2343 7.5299 3.35932C7.40487 3.48435 7.33464 3.65392 7.33464 3.83073C7.33464 4.00754 7.40487 4.17711 7.5299 4.30213C7.65492 4.42716 7.82449 4.4974 8.0013 4.4974C8.17811 4.4974 8.34768 4.42716 8.47271 4.30213C8.59773 4.17711 8.66797 4.00754 8.66797 3.83073C8.66797 3.65392 8.59773 3.48435 8.47271 3.35932C8.34768 3.2343 8.17811 3.16406 8.0013 3.16406ZM8.0013 7.83073C7.82449 7.83073 7.65492 7.90097 7.5299 8.02599C7.40487 8.15102 7.33464 8.32058 7.33464 8.4974C7.33464 8.67421 7.40487 8.84378 7.5299 8.9688C7.65492 9.09382 7.82449 9.16406 8.0013 9.16406C8.17811 9.16406 8.34768 9.09382 8.47271 8.9688C8.59773 8.84378 8.66797 8.67421 8.66797 8.4974C8.66797 8.32058 8.59773 8.15102 8.47271 8.02599C8.34768 7.90097 8.17811 7.83073 8.0013 7.83073ZM8.0013 12.4974C7.82449 12.4974 7.65492 12.5676 7.5299 12.6927C7.40487 12.8177 7.33464 12.9873 7.33464 13.1641C7.33464 13.3409 7.40487 13.5104 7.5299 13.6355C7.65492 13.7605 7.82449 13.8307 8.0013 13.8307C8.17811 13.8307 8.34768 13.7605 8.47271 13.6355C8.59773 13.5104 8.66797 13.3409 8.66797 13.1641C8.66797 12.9873 8.59773 12.8177 8.47271 12.6927C8.34768 12.5676 8.17811 12.4974 8.0013 12.4974Z\" fill=\"white\" stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                        </svg>\r\n                    </div>\r\n                </button>\r\n            </div>\r\n        `;\r\n    }\r\n}\r\n\r\ncustomElements.define('main-header', MainHeader);\r\n", "import { html, css, LitElement } from \"../assets/lit-core-2.7.4.min.js\"\r\n\r\nexport class <PERSON><PERSON><PERSON>eyHeader extends LitElement {\r\n  //////// after_modelStateService ////////\r\n  static properties = {\r\n    llmApiKey: { type: String },\r\n    sttApiKey: { type: String },\r\n    llmProvider: { type: String },\r\n    sttProvider: { type: String },\r\n    isLoading: { type: Boolean },\r\n    errorMessage: { type: String },\r\n    providers: { type: Object, state: true },\r\n  }\r\n  //////// after_modelStateService ////////\r\n\r\n  static styles = css`\r\n        :host {\r\n            display: block;\r\n            transform: translate3d(0, 0, 0);\r\n            backface-visibility: hidden;\r\n            transition: opacity 0.25s ease-out;\r\n        }\r\n\r\n        :host(.sliding-out) {\r\n            animation: slideOutUp 0.3s ease-in forwards;\r\n            will-change: opacity, transform;\r\n        }\r\n\r\n        :host(.hidden) {\r\n            opacity: 0;\r\n            pointer-events: none;\r\n        }\r\n\r\n        @keyframes slideOutUp {\r\n            from {\r\n                opacity: 1;\r\n                transform: translateY(0);\r\n            }\r\n            to {\r\n                opacity: 0;\r\n                transform: translateY(-20px);\r\n            }\r\n        }\r\n\r\n        * {\r\n            font-family: 'Helvetica Neue', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\r\n            cursor: default;\r\n            user-select: none;\r\n            box-sizing: border-box;\r\n        }\r\n\r\n        .container {\r\n            width: 350px;\r\n            min-height: 260px;\r\n            padding: 18px 20px;\r\n            background: rgba(0, 0, 0, 0.3);\r\n            border-radius: 16px;\r\n            overflow: visible;\r\n            position: relative;\r\n            display: flex;\r\n            flex-direction: column;\r\n            align-items: center;\r\n        }\r\n\r\n        .container::after {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0;\r\n            left: 0;\r\n            right: 0;\r\n            bottom: 0;\r\n            border-radius: 16px;\r\n            padding: 1px;\r\n            background: linear-gradient(169deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0) 50%, rgba(255, 255, 255, 0.5) 100%);\r\n            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\r\n            -webkit-mask-composite: destination-out;\r\n            mask-composite: exclude;\r\n            pointer-events: none;\r\n        }\r\n\r\n        .close-button {\r\n            position: absolute;\r\n            top: 10px;\r\n            right: 10px;\r\n            width: 14px;\r\n            height: 14px;\r\n            background: rgba(255, 255, 255, 0.1);\r\n            border: none;\r\n            border-radius: 3px;\r\n            color: rgba(255, 255, 255, 0.7);\r\n            cursor: pointer;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            transition: all 0.15s ease;\r\n            z-index: 10;\r\n            font-size: 14px;\r\n            line-height: 1;\r\n            padding: 0;\r\n        }\r\n\r\n        .close-button:hover {\r\n            background: rgba(255, 255, 255, 0.2);\r\n            color: rgba(255, 255, 255, 0.9);\r\n        }\r\n\r\n        .close-button:active {\r\n            transform: scale(0.95);\r\n        }\r\n\r\n        .title {\r\n            color: white;\r\n            font-size: 16px;\r\n            font-weight: 500; /* Medium */\r\n            margin: 0;\r\n            text-align: center;\r\n            flex-shrink: 0;\r\n        }\r\n\r\n        .form-content {\r\n            display: flex;\r\n            flex-direction: column;\r\n            align-items: center;\r\n            width: 100%;\r\n            margin-top: auto;\r\n        }\r\n\r\n        .error-message {\r\n            color: rgba(239, 68, 68, 0.9);\r\n            font-weight: 500;\r\n            font-size: 11px;\r\n            height: 14px;\r\n            text-align: center;\r\n            margin-bottom: 4px;\r\n        }\r\n\r\n        .api-input {\r\n            width: 100%;\r\n            height: 34px;\r\n            background: rgba(255, 255, 255, 0.1);\r\n            border-radius: 10px;\r\n            border: none;\r\n            padding: 0 10px;\r\n            color: white;\r\n            font-size: 12px;\r\n            font-weight: 400; /* Regular */\r\n            margin-bottom: 6px;\r\n            text-align: center;\r\n            user-select: text;\r\n            cursor: text;\r\n        }\r\n\r\n        .api-input::placeholder {\r\n            color: rgba(255, 255, 255, 0.6);\r\n        }\r\n\r\n        .api-input:focus {\r\n            outline: none;\r\n        }\r\n\r\n        .providers-container { display: flex; gap: 12px; width: 100%; }\r\n        .provider-column { flex: 1; display: flex; flex-direction: column; align-items: center; }\r\n        .provider-label { color: rgba(255, 255, 255, 0.7); font-size: 11px; font-weight: 500; margin-bottom: 6px; }\r\n        .api-input, .provider-select {\r\n            width: 100%;\r\n            height: 34px;\r\n            text-align: center;\r\n            background: rgba(255, 255, 255, 0.1);\r\n            border-radius: 10px;\r\n            border: 1px solid rgba(255, 255, 255, 0.2);\r\n            padding: 0 10px;\r\n            color: white;\r\n            font-size: 12px;\r\n            margin-bottom: 6px;\r\n        }\r\n        .provider-select option { background: #1a1a1a; color: white; }\r\n\r\n        .provider-select:hover {\r\n            background-color: rgba(255, 255, 255, 0.15);\r\n            border-color: rgba(255, 255, 255, 0.3);\r\n        }\r\n\r\n        .provider-select:focus {\r\n            outline: none;\r\n            background-color: rgba(255, 255, 255, 0.15);\r\n            border-color: rgba(255, 255, 255, 0.4);\r\n        }\r\n\r\n\r\n        .action-button {\r\n            width: 100%;\r\n            height: 34px;\r\n            background: rgba(255, 255, 255, 0.2);\r\n            border: none;\r\n            border-radius: 10px;\r\n            color: white;\r\n            font-size: 12px;\r\n            font-weight: 500; /* Medium */\r\n            cursor: pointer;\r\n            transition: background 0.15s ease;\r\n            position: relative;\r\n            overflow: visible;\r\n        }\r\n\r\n        .action-button::after {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0;\r\n            left: 0;\r\n            right: 0;\r\n            bottom: 0;\r\n            border-radius: 10px;\r\n            padding: 1px;\r\n            background: linear-gradient(169deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0) 50%, rgba(255, 255, 255, 0.5) 100%);\r\n            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\r\n            -webkit-mask-composite: destination-out;\r\n            mask-composite: exclude;\r\n            pointer-events: none;\r\n        }\r\n\r\n        .action-button:hover {\r\n            background: rgba(255, 255, 255, 0.3);\r\n        }\r\n\r\n        .action-button:disabled {\r\n            opacity: 0.5;\r\n            cursor: not-allowed;\r\n        }\r\n\r\n        .or-text {\r\n            color: rgba(255, 255, 255, 0.5);\r\n            font-size: 12px;\r\n            font-weight: 500; /* Medium */\r\n            margin: 10px 0;\r\n        }\r\n\r\n        \r\n        /* ────────────────[ GLASS BYPASS ]─────────────── */\r\n        :host-context(body.has-glass) .container,\r\n        :host-context(body.has-glass) .api-input,\r\n        :host-context(body.has-glass) .provider-select,\r\n        :host-context(body.has-glass) .action-button,\r\n        :host-context(body.has-glass) .close-button {\r\n            background: transparent !important;\r\n            border: none !important;\r\n            box-shadow: none !important;\r\n            filter: none !important;\r\n            backdrop-filter: none !important;\r\n        }\r\n\r\n        :host-context(body.has-glass) .container::after,\r\n        :host-context(body.has-glass) .action-button::after {\r\n            display: none !important;\r\n        }\r\n\r\n        :host-context(body.has-glass) .action-button:hover,\r\n        :host-context(body.has-glass) .provider-select:hover,\r\n        :host-context(body.has-glass) .close-button:hover {\r\n            background: transparent !important;\r\n        }\r\n    `\r\n\r\n  constructor() {\r\n    super()\r\n    this.dragState = null\r\n    this.wasJustDragged = false\r\n    this.isLoading = false\r\n    this.errorMessage = \"\"\r\n    //////// after_modelStateService ////////\r\n    this.llmApiKey = \"\";\r\n    this.sttApiKey = \"\";\r\n    this.llmProvider = \"openai\";\r\n    this.sttProvider = \"openai\";\r\n    this.providers = { llm: [], stt: [] }; // 초기화\r\n    this.loadProviderConfig();\r\n    //////// after_modelStateService ////////\r\n\r\n    this.handleMouseMove = this.handleMouseMove.bind(this)\r\n    this.handleMouseUp = this.handleMouseUp.bind(this)\r\n    this.handleKeyPress = this.handleKeyPress.bind(this)\r\n    this.handleSubmit = this.handleSubmit.bind(this)\r\n    this.handleInput = this.handleInput.bind(this)\r\n    this.handleAnimationEnd = this.handleAnimationEnd.bind(this)\r\n    this.handleUsePicklesKey = this.handleUsePicklesKey.bind(this)\r\n    this.handleProviderChange = this.handleProviderChange.bind(this)\r\n  }\r\n\r\n  reset() {\r\n    this.apiKey = \"\"\r\n    this.isLoading = false\r\n    this.errorMessage = \"\"\r\n    this.validatedApiKey = null\r\n    this.selectedProvider = \"openai\"\r\n    this.requestUpdate()\r\n  }\r\n\r\n  async loadProviderConfig() {\r\n    if (!window.require) return;\r\n    const { ipcRenderer } = window.require('electron');\r\n    const config = await ipcRenderer.invoke('model:get-provider-config');\r\n    \r\n    const llmProviders = [];\r\n    const sttProviders = [];\r\n\r\n    for (const id in config) {\r\n        // 'openai-glass' 같은 가상 Provider는 UI에 표시하지 않음\r\n        if (id.includes('-glass')) continue;\r\n\r\n        if (config[id].llmModels.length > 0) {\r\n            llmProviders.push({ id, name: config[id].name });\r\n        }\r\n        if (config[id].sttModels.length > 0) {\r\n            sttProviders.push({ id, name: config[id].name });\r\n        }\r\n    }\r\n    \r\n    this.providers = { llm: llmProviders, stt: sttProviders };\r\n    \r\n    // 기본 선택 값 설정\r\n    if (llmProviders.length > 0) this.llmProvider = llmProviders[0].id;\r\n    if (sttProviders.length > 0) this.sttProvider = sttProviders[0].id;\r\n    \r\n    this.requestUpdate();\r\n}\r\n\r\n  async handleMouseDown(e) {\r\n    if (e.target.tagName === \"INPUT\" || e.target.tagName === \"BUTTON\" || e.target.tagName === \"SELECT\") {\r\n      return\r\n    }\r\n\r\n    e.preventDefault()\r\n\r\n    const { ipcRenderer } = window.require(\"electron\")\r\n    const initialPosition = await ipcRenderer.invoke(\"get-header-position\")\r\n\r\n    this.dragState = {\r\n      initialMouseX: e.screenX,\r\n      initialMouseY: e.screenY,\r\n      initialWindowX: initialPosition.x,\r\n      initialWindowY: initialPosition.y,\r\n      moved: false,\r\n    }\r\n\r\n    window.addEventListener(\"mousemove\", this.handleMouseMove)\r\n    window.addEventListener(\"mouseup\", this.handleMouseUp, { once: true })\r\n  }\r\n\r\n  handleMouseMove(e) {\r\n    if (!this.dragState) return\r\n\r\n    const deltaX = Math.abs(e.screenX - this.dragState.initialMouseX)\r\n    const deltaY = Math.abs(e.screenY - this.dragState.initialMouseY)\r\n\r\n    if (deltaX > 3 || deltaY > 3) {\r\n      this.dragState.moved = true\r\n    }\r\n\r\n    const newWindowX = this.dragState.initialWindowX + (e.screenX - this.dragState.initialMouseX)\r\n    const newWindowY = this.dragState.initialWindowY + (e.screenY - this.dragState.initialMouseY)\r\n\r\n    const { ipcRenderer } = window.require(\"electron\")\r\n    ipcRenderer.invoke(\"move-header-to\", newWindowX, newWindowY)\r\n  }\r\n\r\n  handleMouseUp(e) {\r\n    if (!this.dragState) return\r\n\r\n    const wasDragged = this.dragState.moved\r\n\r\n    window.removeEventListener(\"mousemove\", this.handleMouseMove)\r\n    this.dragState = null\r\n\r\n    if (wasDragged) {\r\n      this.wasJustDragged = true\r\n      setTimeout(() => {\r\n        this.wasJustDragged = false\r\n      }, 200)\r\n    }\r\n  }\r\n\r\n  handleInput(e) {\r\n    this.apiKey = e.target.value\r\n    this.errorMessage = \"\"\r\n    console.log(\"Input changed:\", this.apiKey?.length || 0, \"chars\")\r\n\r\n    this.requestUpdate()\r\n    this.updateComplete.then(() => {\r\n      const inputField = this.shadowRoot?.querySelector(\".apikey-input\")\r\n      if (inputField && this.isInputFocused) {\r\n        inputField.focus()\r\n      }\r\n    })\r\n  }\r\n\r\n  handleProviderChange(e) {\r\n    this.selectedProvider = e.target.value\r\n    this.errorMessage = \"\"\r\n    console.log(\"Provider changed to:\", this.selectedProvider)\r\n    this.requestUpdate()\r\n  }\r\n\r\n  handlePaste(e) {\r\n    e.preventDefault()\r\n    this.errorMessage = \"\"\r\n    const clipboardText = (e.clipboardData || window.clipboardData).getData(\"text\")\r\n    console.log(\"Paste event detected:\", clipboardText?.substring(0, 10) + \"...\")\r\n\r\n    if (clipboardText) {\r\n      this.apiKey = clipboardText.trim()\r\n\r\n      const inputElement = e.target\r\n      inputElement.value = this.apiKey\r\n    }\r\n\r\n    this.requestUpdate()\r\n    this.updateComplete.then(() => {\r\n      const inputField = this.shadowRoot?.querySelector(\".apikey-input\")\r\n      if (inputField) {\r\n        inputField.focus()\r\n        inputField.setSelectionRange(inputField.value.length, inputField.value.length)\r\n      }\r\n    })\r\n  }\r\n\r\n  handleKeyPress(e) {\r\n    if (e.key === \"Enter\") {\r\n      e.preventDefault()\r\n      this.handleSubmit()\r\n    }\r\n  }\r\n\r\n  //////// after_modelStateService ////////\r\n  async handleSubmit() {\r\n    console.log('[ApiKeyHeader] handleSubmit: Submitting API keys...');\r\n    if (this.isLoading || !this.llmApiKey.trim() || !this.sttApiKey.trim()) {\r\n        this.errorMessage = \"Please enter keys for both LLM and STT.\";\r\n        return;\r\n    }\r\n\r\n    this.isLoading = true;\r\n    this.errorMessage = \"\";\r\n    this.requestUpdate();\r\n\r\n    const { ipcRenderer } = window.require('electron');\r\n\r\n    console.log('[ApiKeyHeader] handleSubmit: Validating LLM key...');\r\n    const llmValidation = ipcRenderer.invoke('model:validate-key', { provider: this.llmProvider, key: this.llmApiKey.trim() });\r\n    const sttValidation = ipcRenderer.invoke('model:validate-key', { provider: this.sttProvider, key: this.sttApiKey.trim() });\r\n\r\n    const [llmResult, sttResult] = await Promise.all([llmValidation, sttValidation]);\r\n\r\n    if (llmResult.success && sttResult.success) {\r\n        console.log('[ApiKeyHeader] handleSubmit: Both LLM and STT keys are valid.');\r\n        this.startSlideOutAnimation();\r\n    } else {\r\n        console.log('[ApiKeyHeader] handleSubmit: Validation failed.');\r\n        let errorParts = [];\r\n        if (!llmResult.success) errorParts.push(`LLM Key: ${llmResult.error || 'Invalid'}`);\r\n        if (!sttResult.success) errorParts.push(`STT Key: ${sttResult.error || 'Invalid'}`);\r\n        this.errorMessage = errorParts.join(' | ');\r\n    }\r\n\r\n    this.isLoading = false;\r\n    this.requestUpdate();\r\n}\r\n//////// after_modelStateService ////////\r\n\r\n\r\n  startSlideOutAnimation() {\r\n    console.log('[ApiKeyHeader] startSlideOutAnimation: Starting slide out animation.');\r\n    this.classList.add(\"sliding-out\")\r\n  }\r\n\r\n  handleUsePicklesKey(e) {\r\n    e.preventDefault()\r\n    if (this.wasJustDragged) return\r\n\r\n    console.log(\"Requesting Firebase authentication from main process...\")\r\n    if (window.require) {\r\n      window.require(\"electron\").ipcRenderer.invoke(\"start-firebase-auth\")\r\n    }\r\n  }\r\n\r\n  handleClose() {\r\n    console.log(\"Close button clicked\")\r\n    if (window.require) {\r\n      window.require(\"electron\").ipcRenderer.invoke(\"quit-application\")\r\n    }\r\n  }\r\n\r\n\r\n  //////// after_modelStateService ////////\r\n  handleAnimationEnd(e) {\r\n    if (e.target !== this || !this.classList.contains('sliding-out')) return;\r\n    this.classList.remove(\"sliding-out\");\r\n    this.classList.add(\"hidden\");\r\n    window.require('electron').ipcRenderer.invoke('get-current-user').then(userState => {\r\n        console.log('[ApiKeyHeader] handleAnimationEnd: User state updated:', userState);\r\n        this.stateUpdateCallback?.(userState);\r\n    });\r\n  }\r\n//////// after_modelStateService ////////\r\n\r\n  connectedCallback() {\r\n    super.connectedCallback()\r\n    this.addEventListener(\"animationend\", this.handleAnimationEnd)\r\n  }\r\n\r\n  disconnectedCallback() {\r\n    super.disconnectedCallback()\r\n    this.removeEventListener(\"animationend\", this.handleAnimationEnd)\r\n  }\r\n\r\n  render() {\r\n    const isButtonDisabled = this.isLoading || !this.llmApiKey.trim() || !this.sttApiKey.trim();\r\n\r\n    return html`\r\n        <div class=\"container\" @mousedown=${this.handleMouseDown}>\r\n            <h1 class=\"title\">Enter Your API Keys</h1>\r\n\r\n            <div class=\"providers-container\">\r\n                <div class=\"provider-column\">\r\n                    <div class=\"provider-label\"></div>\r\n                    <select class=\"provider-select\" .value=${this.llmProvider} @change=${e => this.llmProvider = e.target.value} ?disabled=${this.isLoading}>\r\n                        ${this.providers.llm.map(p => html`<option value=${p.id}>${p.name}</option>`)}\r\n                    </select>\r\n                    <input type=\"password\" class=\"api-input\" placeholder=\"LLM Provider API Key\" .value=${this.llmApiKey} @input=${e => this.llmApiKey = e.target.value} ?disabled=${this.isLoading}>\r\n                </div>\r\n\r\n                <div class=\"provider-column\">\r\n                    <div class=\"provider-label\"></div>\r\n                    <select class=\"provider-select\" .value=${this.sttProvider} @change=${e => this.sttProvider = e.target.value} ?disabled=${this.isLoading}>\r\n                        ${this.providers.stt.map(p => html`<option value=${p.id}>${p.name}</option>`)}\r\n                    </select>\r\n                    <input type=\"password\" class=\"api-input\" placeholder=\"STT Provider API Key\" .value=${this.sttApiKey} @input=${e => this.sttApiKey = e.target.value} ?disabled=${this.isLoading}>\r\n                </div>\r\n            </div>\r\n            \r\n            <div class=\"error-message\">${this.errorMessage}</div>\r\n\r\n            <button class=\"action-button\" @click=${this.handleSubmit} ?disabled=${isButtonDisabled}>\r\n                ${this.isLoading ? \"Validating...\" : \"Confirm\"}\r\n            </button>\r\n            <div class=\"or-text\">or</div>\r\n            <button class=\"action-button\" @click=${this.handleUsePicklesKey}>Use Pickle's Key (Login)</button>\r\n        </div>\r\n    `;\r\n}\r\n}\r\n\r\ncustomElements.define(\"apikey-header\", ApiKeyHeader)\r\n", "import { LitElement, html, css } from '../assets/lit-core-2.7.4.min.js';\r\n\r\nexport class PermissionHeader extends LitElement {\r\n    static styles = css`\r\n        :host {\r\n            display: block;\r\n            transform: translate3d(0, 0, 0);\r\n            backface-visibility: hidden;\r\n            transition: opacity 0.25s ease-out;\r\n        }\r\n\r\n        :host(.sliding-out) {\r\n            animation: slideOutUp 0.3s ease-in forwards;\r\n            will-change: opacity, transform;\r\n        }\r\n\r\n        :host(.hidden) {\r\n            opacity: 0;\r\n            pointer-events: none;\r\n        }\r\n\r\n        @keyframes slideOutUp {\r\n            from {\r\n                opacity: 1;\r\n                transform: translateY(0);\r\n            }\r\n            to {\r\n                opacity: 0;\r\n                transform: translateY(-20px);\r\n            }\r\n        }\r\n\r\n        * {\r\n            font-family: 'Helvetica Neue', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\r\n            cursor: default;\r\n            user-select: none;\r\n            box-sizing: border-box;\r\n        }\r\n\r\n        .container {\r\n            width: 285px;\r\n            height: 220px;\r\n            padding: 18px 20px;\r\n            background: rgba(0, 0, 0, 0.3);\r\n            border-radius: 16px;\r\n            overflow: hidden;\r\n            position: relative;\r\n            display: flex;\r\n            flex-direction: column;\r\n            align-items: center;\r\n        }\r\n\r\n        .container::after {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0;\r\n            left: 0;\r\n            right: 0;\r\n            bottom: 0;\r\n            border-radius: 16px;\r\n            padding: 1px;\r\n            background: linear-gradient(169deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0) 50%, rgba(255, 255, 255, 0.5) 100%);\r\n            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\r\n            -webkit-mask-composite: destination-out;\r\n            mask-composite: exclude;\r\n            pointer-events: none;\r\n        }\r\n\r\n        .close-button {\r\n            position: absolute;\r\n            top: 10px;\r\n            right: 10px;\r\n            width: 14px;\r\n            height: 14px;\r\n            background: rgba(255, 255, 255, 0.1);\r\n            border: none;\r\n            border-radius: 3px;\r\n            color: rgba(255, 255, 255, 0.7);\r\n            cursor: pointer;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            transition: all 0.15s ease;\r\n            z-index: 10;\r\n            font-size: 14px;\r\n            line-height: 1;\r\n            padding: 0;\r\n        }\r\n\r\n        .close-button:hover {\r\n            background: rgba(255, 255, 255, 0.2);\r\n            color: rgba(255, 255, 255, 0.9);\r\n        }\r\n\r\n        .close-button:active {\r\n            transform: scale(0.95);\r\n        }\r\n\r\n        .title {\r\n            color: white;\r\n            font-size: 16px;\r\n            font-weight: 500;\r\n            margin: 0;\r\n            text-align: center;\r\n            flex-shrink: 0;\r\n        }\r\n\r\n        .form-content {\r\n            display: flex;\r\n            flex-direction: column;\r\n            align-items: center;\r\n            width: 100%;\r\n            margin-top: auto;\r\n        }\r\n\r\n        .subtitle {\r\n            color: rgba(255, 255, 255, 0.7);\r\n            font-size: 11px;\r\n            font-weight: 400;\r\n            text-align: center;\r\n            margin-bottom: 12px;\r\n            line-height: 1.3;\r\n        }\r\n\r\n        .permission-status {\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            gap: 8px;\r\n            margin-bottom: 12px;\r\n            min-height: 20px;\r\n        }\r\n\r\n        .permission-item {\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 6px;\r\n            color: rgba(255, 255, 255, 0.8);\r\n            font-size: 11px;\r\n            font-weight: 400;\r\n        }\r\n\r\n        .permission-item.granted {\r\n            color: rgba(34, 197, 94, 0.9);\r\n        }\r\n\r\n        .permission-icon {\r\n            width: 12px;\r\n            height: 12px;\r\n            opacity: 0.8;\r\n        }\r\n\r\n        .check-icon {\r\n            width: 12px;\r\n            height: 12px;\r\n            color: rgba(34, 197, 94, 0.9);\r\n        }\r\n\r\n        .action-button {\r\n            width: 100%;\r\n            height: 34px;\r\n            background: rgba(255, 255, 255, 0.2);\r\n            border: none;\r\n            border-radius: 10px;\r\n            color: white;\r\n            font-size: 12px;\r\n            font-weight: 500;\r\n            cursor: pointer;\r\n            transition: background 0.15s ease;\r\n            position: relative;\r\n            overflow: hidden;\r\n            margin-bottom: 6px;\r\n        }\r\n\r\n        .action-button::after {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0;\r\n            left: 0;\r\n            right: 0;\r\n            bottom: 0;\r\n            border-radius: 10px;\r\n            padding: 1px;\r\n            background: linear-gradient(169deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0) 50%, rgba(255, 255, 255, 0.5) 100%);\r\n            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\r\n            -webkit-mask-composite: destination-out;\r\n            mask-composite: exclude;\r\n            pointer-events: none;\r\n        }\r\n\r\n        .action-button:hover:not(:disabled) {\r\n            background: rgba(255, 255, 255, 0.3);\r\n        }\r\n\r\n        .action-button:disabled {\r\n            opacity: 0.5;\r\n            cursor: not-allowed;\r\n        }\r\n\r\n        .continue-button {\r\n            width: 100%;\r\n            height: 34px;\r\n            background: rgba(34, 197, 94, 0.8);\r\n            border: none;\r\n            border-radius: 10px;\r\n            color: white;\r\n            font-size: 12px;\r\n            font-weight: 500;\r\n            cursor: pointer;\r\n            transition: background 0.15s ease;\r\n            position: relative;\r\n            overflow: hidden;\r\n            margin-top: 4px;\r\n        }\r\n\r\n        .continue-button::after {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0;\r\n            left: 0;\r\n            right: 0;\r\n            bottom: 0;\r\n            border-radius: 10px;\r\n            padding: 1px;\r\n            background: linear-gradient(169deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0) 50%, rgba(255, 255, 255, 0.5) 100%);\r\n            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\r\n            -webkit-mask-composite: destination-out;\r\n            mask-composite: exclude;\r\n            pointer-events: none;\r\n        }\r\n\r\n        .continue-button:hover:not(:disabled) {\r\n            background: rgba(34, 197, 94, 0.9);\r\n        }\r\n\r\n        .continue-button:disabled {\r\n            background: rgba(255, 255, 255, 0.2);\r\n            cursor: not-allowed;\r\n        }\r\n\r\n        /* ────────────────[ GLASS BYPASS ]─────────────── */\r\n        :host-context(body.has-glass) .container,\r\n        :host-context(body.has-glass) .action-button,\r\n        :host-context(body.has-glass) .continue-button,\r\n        :host-context(body.has-glass) .close-button {\r\n            background: transparent !important;\r\n            border: none !important;\r\n            box-shadow: none !important;\r\n            filter: none !important;\r\n            backdrop-filter: none !important;\r\n        }\r\n\r\n        :host-context(body.has-glass) .container::after,\r\n        :host-context(body.has-glass) .action-button::after,\r\n        :host-context(body.has-glass) .continue-button::after {\r\n            display: none !important;\r\n        }\r\n\r\n        :host-context(body.has-glass) .action-button:hover,\r\n        :host-context(body.has-glass) .continue-button:hover,\r\n        :host-context(body.has-glass) .close-button:hover {\r\n            background: transparent !important;\r\n        }\r\n    `;\r\n\r\n    static properties = {\r\n        microphoneGranted: { type: String },\r\n        screenGranted: { type: String },\r\n        isChecking: { type: String },\r\n        continueCallback: { type: Function }\r\n    };\r\n\r\n    constructor() {\r\n        super();\r\n        this.microphoneGranted = 'unknown';\r\n        this.screenGranted = 'unknown';\r\n        this.isChecking = false;\r\n        this.continueCallback = null;\r\n\r\n        this.handleMouseMove = this.handleMouseMove.bind(this);\r\n        this.handleMouseUp = this.handleMouseUp.bind(this);\r\n    }\r\n\r\n    async connectedCallback() {\r\n        super.connectedCallback();\r\n        await this.checkPermissions();\r\n        \r\n        // Set up periodic permission check\r\n        this.permissionCheckInterval = setInterval(() => {\r\n            this.checkPermissions();\r\n        }, 1000);\r\n    }\r\n\r\n    disconnectedCallback() {\r\n        super.disconnectedCallback();\r\n        if (this.permissionCheckInterval) {\r\n            clearInterval(this.permissionCheckInterval);\r\n        }\r\n    }\r\n\r\n    async handleMouseDown(e) {\r\n        if (e.target.tagName === 'BUTTON') {\r\n            return;\r\n        }\r\n\r\n        e.preventDefault();\r\n\r\n        const { ipcRenderer } = window.require('electron');\r\n        const initialPosition = await ipcRenderer.invoke('get-header-position');\r\n\r\n        this.dragState = {\r\n            initialMouseX: e.screenX,\r\n            initialMouseY: e.screenY,\r\n            initialWindowX: initialPosition.x,\r\n            initialWindowY: initialPosition.y,\r\n            moved: false,\r\n        };\r\n\r\n        window.addEventListener('mousemove', this.handleMouseMove);\r\n        window.addEventListener('mouseup', this.handleMouseUp, { once: true });\r\n    }\r\n\r\n    handleMouseMove(e) {\r\n        if (!this.dragState) return;\r\n\r\n        const deltaX = Math.abs(e.screenX - this.dragState.initialMouseX);\r\n        const deltaY = Math.abs(e.screenY - this.dragState.initialMouseY);\r\n\r\n        if (deltaX > 3 || deltaY > 3) {\r\n            this.dragState.moved = true;\r\n        }\r\n\r\n        const newWindowX = this.dragState.initialWindowX + (e.screenX - this.dragState.initialMouseX);\r\n        const newWindowY = this.dragState.initialWindowY + (e.screenY - this.dragState.initialMouseY);\r\n\r\n        const { ipcRenderer } = window.require('electron');\r\n        ipcRenderer.invoke('move-header-to', newWindowX, newWindowY);\r\n    }\r\n\r\n    handleMouseUp(e) {\r\n        if (!this.dragState) return;\r\n\r\n        const wasDragged = this.dragState.moved;\r\n\r\n        window.removeEventListener('mousemove', this.handleMouseMove);\r\n        this.dragState = null;\r\n\r\n        if (wasDragged) {\r\n            this.wasJustDragged = true;\r\n            setTimeout(() => {\r\n                this.wasJustDragged = false;\r\n            }, 200);\r\n        }\r\n    }\r\n\r\n    async checkPermissions() {\r\n        if (!window.require || this.isChecking) return;\r\n        \r\n        this.isChecking = true;\r\n        const { ipcRenderer } = window.require('electron');\r\n        \r\n        try {\r\n            const permissions = await ipcRenderer.invoke('check-system-permissions');\r\n            console.log('[PermissionHeader] Permission check result:', permissions);\r\n            \r\n            const prevMic = this.microphoneGranted;\r\n            const prevScreen = this.screenGranted;\r\n            \r\n            this.microphoneGranted = permissions.microphone;\r\n            this.screenGranted = permissions.screen;\r\n            \r\n            // if permissions changed == UI update\r\n            if (prevMic !== this.microphoneGranted || prevScreen !== this.screenGranted) {\r\n                console.log('[PermissionHeader] Permission status changed, updating UI');\r\n                this.requestUpdate();\r\n            }\r\n            \r\n            // if all permissions granted == automatically continue\r\n            if (this.microphoneGranted === 'granted' && \r\n                this.screenGranted === 'granted' && \r\n                this.continueCallback) {\r\n                console.log('[PermissionHeader] All permissions granted, proceeding automatically');\r\n                setTimeout(() => this.handleContinue(), 500);\r\n            }\r\n        } catch (error) {\r\n            console.error('[PermissionHeader] Error checking permissions:', error);\r\n        } finally {\r\n            this.isChecking = false;\r\n        }\r\n    }\r\n\r\n    async handleMicrophoneClick() {\r\n        if (!window.require || this.microphoneGranted === 'granted' || this.wasJustDragged) return;\r\n        \r\n        console.log('[PermissionHeader] Requesting microphone permission...');\r\n        const { ipcRenderer } = window.require('electron');\r\n        \r\n        try {\r\n            const result = await ipcRenderer.invoke('check-system-permissions');\r\n            console.log('[PermissionHeader] Microphone permission result:', result);\r\n            \r\n            if (result.microphone === 'granted') {\r\n                this.microphoneGranted = 'granted';\r\n                this.requestUpdate();\r\n                return;\r\n              }\r\n            \r\n              if (result.microphone === 'not-determined' || result.microphone === 'denied' || result.microphone === 'unknown' || result.microphone === 'restricted') {\r\n                const res = await ipcRenderer.invoke('request-microphone-permission');\r\n                if (res.status === 'granted' || res.success === true) {\r\n                    this.microphoneGranted = 'granted';\r\n                    this.requestUpdate();\r\n                    return;\r\n                }\r\n              }\r\n            \r\n            \r\n            // Check permissions again after a delay\r\n            // setTimeout(() => this.checkPermissions(), 1000);\r\n        } catch (error) {\r\n            console.error('[PermissionHeader] Error requesting microphone permission:', error);\r\n        }\r\n    }\r\n\r\n    async handleScreenClick() {\r\n        if (!window.require || this.screenGranted === 'granted' || this.wasJustDragged) return;\r\n        \r\n        console.log('[PermissionHeader] Checking screen recording permission...');\r\n        const { ipcRenderer } = window.require('electron');\r\n        \r\n        try {\r\n            const permissions = await ipcRenderer.invoke('check-system-permissions');\r\n            console.log('[PermissionHeader] Screen permission check result:', permissions);\r\n            \r\n            if (permissions.screen === 'granted') {\r\n                this.screenGranted = 'granted';\r\n                this.requestUpdate();\r\n                return;\r\n            }\r\n            if (permissions.screen === 'not-determined' || permissions.screen === 'denied' || permissions.screen === 'unknown' || permissions.screen === 'restricted') {\r\n            console.log('[PermissionHeader] Opening screen recording preferences...');\r\n            await ipcRenderer.invoke('open-system-preferences', 'screen-recording');\r\n            }\r\n            \r\n            // Check permissions again after a delay\r\n            // (This may not execute if app restarts after permission grant)\r\n            // setTimeout(() => this.checkPermissions(), 2000);\r\n        } catch (error) {\r\n            console.error('[PermissionHeader] Error opening screen recording preferences:', error);\r\n        }\r\n    }\r\n\r\n    async handleContinue() {\r\n        if (this.continueCallback && \r\n            this.microphoneGranted === 'granted' && \r\n            this.screenGranted === 'granted' && \r\n            !this.wasJustDragged) {\r\n            // Mark permissions as completed\r\n            if (window.require) {\r\n                const { ipcRenderer } = window.require('electron');\r\n                try {\r\n                    await ipcRenderer.invoke('mark-permissions-completed');\r\n                    console.log('[PermissionHeader] Marked permissions as completed');\r\n                } catch (error) {\r\n                    console.error('[PermissionHeader] Error marking permissions as completed:', error);\r\n                }\r\n            }\r\n            \r\n            this.continueCallback();\r\n        }\r\n    }\r\n\r\n    handleClose() {\r\n        console.log('Close button clicked');\r\n        if (window.require) {\r\n            window.require('electron').ipcRenderer.invoke('quit-application');\r\n        }\r\n    }\r\n\r\n    render() {\r\n        const allGranted = this.microphoneGranted === 'granted' && this.screenGranted === 'granted';\r\n\r\n        return html`\r\n            <div class=\"container\" @mousedown=${this.handleMouseDown}>\r\n                <button class=\"close-button\" @click=${this.handleClose} title=\"Close application\">\r\n                    <svg width=\"8\" height=\"8\" viewBox=\"0 0 10 10\" fill=\"currentColor\">\r\n                        <path d=\"M1 1L9 9M9 1L1 9\" stroke=\"currentColor\" stroke-width=\"1.2\" />\r\n                    </svg>\r\n                </button>\r\n                <h1 class=\"title\">Permission Setup Required</h1>\r\n\r\n                <div class=\"form-content\">\r\n                    <div class=\"subtitle\">Grant access to microphone and screen recording to continue</div>\r\n                    \r\n                    <div class=\"permission-status\">\r\n                        <div class=\"permission-item ${this.microphoneGranted === 'granted' ? 'granted' : ''}\">\r\n                            ${this.microphoneGranted === 'granted' ? html`\r\n                                <svg class=\"check-icon\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                                    <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clip-rule=\"evenodd\" />\r\n                                </svg>\r\n                                <span>Microphone ✓</span>\r\n                            ` : html`\r\n                                <svg class=\"permission-icon\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                                    <path fill-rule=\"evenodd\" d=\"M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z\" clip-rule=\"evenodd\" />\r\n                                </svg>\r\n                                <span>Microphone</span>\r\n                            `}\r\n                        </div>\r\n                        \r\n                        <div class=\"permission-item ${this.screenGranted === 'granted' ? 'granted' : ''}\">\r\n                            ${this.screenGranted === 'granted' ? html`\r\n                                <svg class=\"check-icon\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                                    <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clip-rule=\"evenodd\" />\r\n                                </svg>\r\n                                <span>Screen ✓</span>\r\n                            ` : html`\r\n                                <svg class=\"permission-icon\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                                    <path fill-rule=\"evenodd\" d=\"M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z\" clip-rule=\"evenodd\" />\r\n                                </svg>\r\n                                <span>Screen Recording</span>\r\n                            `}\r\n                        </div>\r\n                    </div>\r\n\r\n                    ${this.microphoneGranted !== 'granted' ? html`\r\n                        <button \r\n                            class=\"action-button\" \r\n                            @click=${this.handleMicrophoneClick}\r\n                        >\r\n                            Grant Microphone Access\r\n                        </button>\r\n                    ` : ''}\r\n\r\n                    ${this.screenGranted !== 'granted' ? html`\r\n                        <button \r\n                            class=\"action-button\" \r\n                            @click=${this.handleScreenClick}\r\n                        >\r\n                            Grant Screen Recording Access\r\n                        </button>\r\n                    ` : ''}\r\n\r\n                    ${allGranted ? html`\r\n                        <button \r\n                            class=\"continue-button\" \r\n                            @click=${this.handleContinue}\r\n                        >\r\n                            Continue to Pickle Glass\r\n                        </button>\r\n                    ` : ''}\r\n                </div>\r\n            </div>\r\n        `;\r\n    }\r\n}\r\n\r\ncustomElements.define('permission-setup', PermissionHeader); ", "import './MainHeader.js';\r\nimport './ApiKeyHeader.js';\r\nimport './PermissionHeader.js';\r\n\r\nclass HeaderTransitionManager {\r\n    constructor() {\r\n        this.headerContainer      = document.getElementById('header-container');\r\n        this.currentHeaderType    = null;   // 'apikey' | 'main' | 'permission'\r\n        this.apiKeyHeader         = null;\r\n        this.mainHeader            = null;\r\n        this.permissionHeader      = null;\r\n\r\n        /**\r\n         * only one header window is allowed\r\n         * @param {'apikey'|'main'|'permission'} type\r\n         */\r\n        this.ensureHeader = (type) => {\r\n            console.log('[HeaderController] ensureHeader: Ensuring header of type:', type);\r\n            if (this.currentHeaderType === type) {\r\n                console.log('[HeaderController] ensureHeader: Header of type:', type, 'already exists.');\r\n                return;\r\n            }\r\n\r\n            this.headerContainer.innerHTML = '';\r\n            \r\n            this.apiKeyHeader = null;\r\n            this.mainHeader = null;\r\n            this.permissionHeader = null;\r\n\r\n            // Create new header element\r\n            if (type === 'apikey') {\r\n                this.apiKeyHeader = document.createElement('apikey-header');\r\n                this.apiKeyHeader.stateUpdateCallback = (userState) => this.handleStateUpdate(userState);\r\n                this.headerContainer.appendChild(this.apiKeyHeader);\r\n            } else if (type === 'permission') {\r\n                this.permissionHeader = document.createElement('permission-setup');\r\n                this.permissionHeader.continueCallback = () => this.transitionToMainHeader();\r\n                this.headerContainer.appendChild(this.permissionHeader);\r\n            } else {\r\n                this.mainHeader = document.createElement('main-header');\r\n                this.headerContainer.appendChild(this.mainHeader);\r\n                this.mainHeader.startSlideInAnimation?.();\r\n            }\r\n\r\n            this.currentHeaderType = type;\r\n            this.notifyHeaderState(type === 'permission' ? 'apikey' : type); // Keep permission state as apikey for compatibility\r\n        };\r\n\r\n        console.log('[HeaderController] Manager initialized');\r\n\r\n        this._bootstrap();\r\n\r\n        if (window.require) {\r\n            const { ipcRenderer } = window.require('electron');\r\n\r\n            ipcRenderer.on('user-state-changed', (event, userState) => {\r\n                console.log('[HeaderController] Received user state change:', userState);\r\n                this.handleStateUpdate(userState);\r\n            });\r\n\r\n            ipcRenderer.on('auth-failed', (event, { message }) => {\r\n                console.error('[HeaderController] Received auth failure from main process:', message);\r\n                if (this.apiKeyHeader) {\r\n                    this.apiKeyHeader.errorMessage = 'Authentication failed. Please try again.';\r\n                    this.apiKeyHeader.isLoading = false;\r\n                }\r\n            });\r\n            ipcRenderer.on('force-show-apikey-header', async () => {\r\n                console.log('[HeaderController] Received broadcast to show apikey header. Switching now.');\r\n                await this._resizeForApiKey();\r\n                this.ensureHeader('apikey');\r\n            });\r\n        }\r\n    }\r\n\r\n    notifyHeaderState(stateOverride) {\r\n        const state = stateOverride || this.currentHeaderType || 'apikey';\r\n        if (window.require) {\r\n            window.require('electron').ipcRenderer.send('header-state-changed', state);\r\n        }\r\n    }\r\n\r\n    async _bootstrap() {\r\n        // The initial state will be sent by the main process via 'user-state-changed'\r\n        // We just need to request it.\r\n        if (window.require) {\r\n            const userState = await window.require('electron').ipcRenderer.invoke('get-current-user');\r\n            console.log('[HeaderController] Bootstrapping with initial user state:', userState);\r\n            this.handleStateUpdate(userState);\r\n        } else {\r\n            // Fallback for non-electron environment (testing/web)\r\n            this.ensureHeader('apikey');\r\n        }\r\n    }\r\n\r\n\r\n    //////// after_modelStateService ////////\r\n    async handleStateUpdate(userState) {\r\n        const { ipcRenderer } = window.require('electron');\r\n        const isConfigured = await ipcRenderer.invoke('model:are-providers-configured');\r\n\r\n        if (isConfigured) {\r\n            const { isLoggedIn } = userState;\r\n            if (isLoggedIn) {\r\n                const permissionResult = await this.checkPermissions();\r\n                if (permissionResult.success) {\r\n                    this.transitionToMainHeader();\r\n                } else {\r\n                    this.transitionToPermissionHeader();\r\n                }\r\n            } else {\r\n                this.transitionToMainHeader();\r\n            }\r\n        } else {\r\n            await this._resizeForApiKey();\r\n            this.ensureHeader('apikey');\r\n        }\r\n    }\r\n    //////// after_modelStateService ////////\r\n\r\n    async transitionToPermissionHeader() {\r\n        // Prevent duplicate transitions\r\n        if (this.currentHeaderType === 'permission') {\r\n            console.log('[HeaderController] Already showing permission setup, skipping transition');\r\n            return;\r\n        }\r\n\r\n        // Check if permissions were previously completed\r\n        if (window.require) {\r\n            const { ipcRenderer } = window.require('electron');\r\n            try {\r\n                const permissionsCompleted = await ipcRenderer.invoke('check-permissions-completed');\r\n                if (permissionsCompleted) {\r\n                    console.log('[HeaderController] Permissions were previously completed, checking current status...');\r\n                    \r\n                    // Double check current permission status\r\n                    const permissionResult = await this.checkPermissions();\r\n                    if (permissionResult.success) {\r\n                        // Skip permission setup if already granted\r\n                        this.transitionToMainHeader();\r\n                        return;\r\n                    }\r\n                    \r\n                    console.log('[HeaderController] Permissions were revoked, showing setup again');\r\n                }\r\n            } catch (error) {\r\n                console.error('[HeaderController] Error checking permissions completed status:', error);\r\n            }\r\n        }\r\n\r\n        await this._resizeForPermissionHeader();\r\n        this.ensureHeader('permission');\r\n    }\r\n\r\n    async transitionToMainHeader(animate = true) {\r\n        if (this.currentHeaderType === 'main') {\r\n            return this._resizeForMain();\r\n        }\r\n\r\n        await this._resizeForMain();\r\n        this.ensureHeader('main');\r\n    }\r\n\r\n    _resizeForMain() {\r\n        if (!window.require) return;\r\n        return window\r\n            .require('electron')\r\n            .ipcRenderer.invoke('resize-header-window', { width: 353, height: 47 })\r\n            .catch(() => {});\r\n    }\r\n\r\n    async _resizeForApiKey() {\r\n        if (!window.require) return;\r\n        return window\r\n            .require('electron')\r\n            .ipcRenderer.invoke('resize-header-window', { width: 350, height: 300 })\r\n            .catch(() => {});\r\n    }\r\n\r\n    async _resizeForPermissionHeader() {\r\n        if (!window.require) return;\r\n        return window\r\n            .require('electron')\r\n            .ipcRenderer.invoke('resize-header-window', { width: 285, height: 220 })\r\n            .catch(() => {});\r\n    }\r\n\r\n    async checkPermissions() {\r\n        if (!window.require) {\r\n            return { success: true };\r\n        }\r\n\r\n        const { ipcRenderer } = window.require('electron');\r\n        \r\n        try {\r\n            const permissions = await ipcRenderer.invoke('check-system-permissions');\r\n            console.log('[HeaderController] Current permissions:', permissions);\r\n            \r\n            if (!permissions.needsSetup) {\r\n                return { success: true };\r\n            }\r\n\r\n            let errorMessage = '';\r\n            if (!permissions.microphone && !permissions.screen) {\r\n                errorMessage = 'Microphone and screen recording access required';\r\n            }\r\n            \r\n            return { \r\n                success: false, \r\n                error: errorMessage\r\n            };\r\n        } catch (error) {\r\n            console.error('[HeaderController] Error checking permissions:', error);\r\n            return { \r\n                success: false, \r\n                error: 'Failed to check permissions' \r\n            };\r\n        }\r\n    }\r\n}\r\n\r\nwindow.addEventListener('DOMContentLoaded', () => {\r\n    new HeaderTransitionManager();\r\n});\r\n"], "mappings": ";AAKA,IAAM,IAAE;AAAR,IAAe,IAAE,EAAE,eAAa,WAAS,EAAE,YAAU,EAAE,SAAS,iBAAe,wBAAuB,SAAS,aAAW,aAAY,cAAc;AAApJ,IAA8J,IAAE,OAAO;AAAvK,IAAyK,IAAE,oBAAI;AAAQ,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYA,IAAEC,IAAEC,IAAE;AAAC,QAAG,KAAK,eAAa,MAAGA,OAAI,EAAE,OAAM,MAAM,mEAAmE;AAAE,SAAK,UAAQF,IAAE,KAAK,IAAEC;AAAA,EAAC;AAAA,EAAC,IAAI,aAAY;AAAC,QAAID,KAAE,KAAK;AAAE,UAAMG,KAAE,KAAK;AAAE,QAAG,KAAG,WAASH,IAAE;AAAC,YAAME,KAAE,WAASC,MAAG,MAAIA,GAAE;AAAO,MAAAD,OAAIF,KAAE,EAAE,IAAIG,EAAC,IAAG,WAASH,QAAK,KAAK,IAAEA,KAAE,IAAI,iBAAe,YAAY,KAAK,OAAO,GAAEE,MAAG,EAAE,IAAIC,IAAEH,EAAC;AAAA,IAAE;AAAC,WAAOA;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,WAAO,KAAK;AAAA,EAAO;AAAC;AAAC,IAAM,IAAE,CAAAA,OAAG,IAAI,EAAE,YAAU,OAAOA,KAAEA,KAAEA,KAAE,IAAG,QAAO,CAAC;AAAnD,IAAqD,IAAE,CAACA,OAAKC,OAAI;AAAC,QAAMC,KAAE,MAAIF,GAAE,SAAOA,GAAE,CAAC,IAAEC,GAAE,OAAQ,CAACA,IAAEE,IAAED,OAAID,MAAG,CAAAD,OAAG;AAAC,QAAG,SAAKA,GAAE,aAAa,QAAOA,GAAE;AAAQ,QAAG,YAAU,OAAOA,GAAE,QAAOA;AAAE,UAAM,MAAM,qEAAmEA,KAAE,sFAAsF;AAAA,EAAC,GAAGG,EAAC,IAAEH,GAAEE,KAAE,CAAC,GAAGF,GAAE,CAAC,CAAC;AAAE,SAAO,IAAI,EAAEE,IAAEF,IAAE,CAAC;AAAC;AAA5Y,IAA8Y,IAAE,CAACC,IAAEE,OAAI;AAAC,MAAEF,GAAE,qBAAmBE,GAAE,IAAK,CAAAH,OAAGA,cAAa,gBAAcA,KAAEA,GAAE,UAAW,IAAEG,GAAE,QAAS,CAAAA,OAAG;AAAC,UAAMD,KAAE,SAAS,cAAc,OAAO,GAAEE,KAAE,EAAE;AAAS,eAASA,MAAGF,GAAE,aAAa,SAAQE,EAAC,GAAEF,GAAE,cAAYC,GAAE,SAAQF,GAAE,YAAYC,EAAC;AAAA,EAAC,CAAE;AAAC;AAA1nB,IAA4nB,IAAE,IAAE,CAAAF,OAAGA,KAAE,CAAAA,OAAGA,cAAa,iBAAe,CAAAA,OAAG;AAAC,MAAIC,KAAE;AAAG,aAAUE,MAAKH,GAAE,SAAS,CAAAC,MAAGE,GAAE;AAAQ,SAAO,EAAEF,EAAC;AAAC,GAAGD,EAAC,IAAEA;AAK1yC,IAAI;AAAE,IAAM,IAAE;AAAR,IAAe,IAAE,EAAE;AAAnB,IAAgC,IAAE,IAAE,EAAE,cAAY;AAAlD,IAAqD,IAAE,EAAE;AAAzD,IAAwF,IAAE,EAAC,YAAYA,IAAEC,IAAE;AAAC,UAAOA,IAAE;AAAA,IAAC,KAAK;AAAQ,MAAAD,KAAEA,KAAE,IAAE;AAAK;AAAA,IAAM,KAAK;AAAA,IAAO,KAAK;AAAM,MAAAA,KAAE,QAAMA,KAAEA,KAAE,KAAK,UAAUA,EAAC;AAAA,EAAC;AAAC,SAAOA;AAAC,GAAE,cAAcA,IAAEC,IAAE;AAAC,MAAIE,KAAEH;AAAE,UAAOC,IAAE;AAAA,IAAC,KAAK;AAAQ,MAAAE,KAAE,SAAOH;AAAE;AAAA,IAAM,KAAK;AAAO,MAAAG,KAAE,SAAOH,KAAE,OAAK,OAAOA,EAAC;AAAE;AAAA,IAAM,KAAK;AAAA,IAAO,KAAK;AAAM,UAAG;AAAC,QAAAG,KAAE,KAAK,MAAMH,EAAC;AAAA,MAAC,SAAOA,IAAE;AAAC,QAAAG,KAAE;AAAA,MAAI;AAAA,EAAC;AAAC,SAAOA;AAAC,EAAC;AAAvY,IAAyY,IAAE,CAACH,IAAEC,OAAIA,OAAID,OAAIC,MAAGA,MAAGD,MAAGA;AAAna,IAAsa,IAAE,EAAC,WAAU,MAAG,MAAK,QAAO,WAAU,GAAE,SAAQ,OAAG,YAAW,EAAC;AAAre,IAAue,IAAE;AAAY,IAAM,IAAN,cAAgB,YAAW;AAAA,EAAC,cAAa;AAAC,UAAM,GAAE,KAAK,IAAE,oBAAI,OAAI,KAAK,kBAAgB,OAAG,KAAK,aAAW,OAAG,KAAK,IAAE,MAAK,KAAK,EAAE;AAAA,EAAC;AAAA,EAAC,OAAO,eAAeA,IAAE;AAAC,QAAIC;AAAE,SAAK,SAAS,IAAG,UAAQA,KAAE,KAAK,MAAI,WAASA,KAAEA,KAAE,KAAK,IAAE,CAAC,GAAG,KAAKD,EAAC;AAAA,EAAC;AAAA,EAAC,WAAW,qBAAoB;AAAC,SAAK,SAAS;AAAE,UAAMA,KAAE,CAAC;AAAE,WAAO,KAAK,kBAAkB,QAAS,CAACC,IAAEE,OAAI;AAAC,YAAMD,KAAE,KAAK,EAAEC,IAAEF,EAAC;AAAE,iBAASC,OAAI,KAAK,EAAE,IAAIA,IAAEC,EAAC,GAAEH,GAAE,KAAKE,EAAC;AAAA,IAAE,CAAE,GAAEF;AAAA,EAAC;AAAA,EAAC,OAAO,eAAeA,IAAEC,KAAE,GAAE;AAAC,QAAGA,GAAE,UAAQA,GAAE,YAAU,QAAI,KAAK,SAAS,GAAE,KAAK,kBAAkB,IAAID,IAAEC,EAAC,GAAE,CAACA,GAAE,cAAY,CAAC,KAAK,UAAU,eAAeD,EAAC,GAAE;AAAC,YAAMG,KAAE,YAAU,OAAOH,KAAE,OAAO,IAAE,OAAKA,IAAEE,KAAE,KAAK,sBAAsBF,IAAEG,IAAEF,EAAC;AAAE,iBAASC,MAAG,OAAO,eAAe,KAAK,WAAUF,IAAEE,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,OAAO,sBAAsBF,IAAEC,IAAEE,IAAE;AAAC,WAAM,EAAC,MAAK;AAAC,aAAO,KAAKF,EAAC;AAAA,IAAC,GAAE,IAAIC,IAAE;AAAC,YAAME,KAAE,KAAKJ,EAAC;AAAE,WAAKC,EAAC,IAAEC,IAAE,KAAK,cAAcF,IAAEI,IAAED,EAAC;AAAA,IAAC,GAAE,cAAa,MAAG,YAAW,KAAE;AAAA,EAAC;AAAA,EAAC,OAAO,mBAAmBH,IAAE;AAAC,WAAO,KAAK,kBAAkB,IAAIA,EAAC,KAAG;AAAA,EAAC;AAAA,EAAC,OAAO,WAAU;AAAC,QAAG,KAAK,eAAe,CAAC,EAAE,QAAM;AAAG,SAAK,CAAC,IAAE;AAAG,UAAMA,KAAE,OAAO,eAAe,IAAI;AAAE,QAAGA,GAAE,SAAS,GAAE,WAASA,GAAE,MAAI,KAAK,IAAE,CAAC,GAAGA,GAAE,CAAC,IAAG,KAAK,oBAAkB,IAAI,IAAIA,GAAE,iBAAiB,GAAE,KAAK,IAAE,oBAAI,OAAI,KAAK,eAAe,YAAY,GAAE;AAAC,YAAMA,KAAE,KAAK,YAAWC,KAAE,CAAC,GAAG,OAAO,oBAAoBD,EAAC,GAAE,GAAG,OAAO,sBAAsBA,EAAC,CAAC;AAAE,iBAAUG,MAAKF,GAAE,MAAK,eAAeE,IAAEH,GAAEG,EAAC,CAAC;AAAA,IAAC;AAAC,WAAO,KAAK,gBAAc,KAAK,eAAe,KAAK,MAAM,GAAE;AAAA,EAAE;AAAA,EAAC,OAAO,eAAeH,IAAE;AAAC,UAAMC,KAAE,CAAC;AAAE,QAAG,MAAM,QAAQD,EAAC,GAAE;AAAC,YAAMG,KAAE,IAAI,IAAIH,GAAE,KAAK,IAAE,CAAC,EAAE,QAAQ,CAAC;AAAE,iBAAUA,MAAKG,GAAE,CAAAF,GAAE,QAAQ,EAAED,EAAC,CAAC;AAAA,IAAC,MAAM,YAASA,MAAGC,GAAE,KAAK,EAAED,EAAC,CAAC;AAAE,WAAOC;AAAA,EAAC;AAAA,EAAC,OAAO,EAAED,IAAEC,IAAE;AAAC,UAAME,KAAEF,GAAE;AAAU,WAAM,UAAKE,KAAE,SAAO,YAAU,OAAOA,KAAEA,KAAE,YAAU,OAAOH,KAAEA,GAAE,YAAY,IAAE;AAAA,EAAM;AAAA,EAAC,IAAG;AAAC,QAAIA;AAAE,SAAK,IAAE,IAAI,QAAS,CAAAA,OAAG,KAAK,iBAAeA,EAAE,GAAE,KAAK,OAAK,oBAAI,OAAI,KAAK,EAAE,GAAE,KAAK,cAAc,GAAE,UAAQA,KAAE,KAAK,YAAY,MAAI,WAASA,MAAGA,GAAE,QAAS,CAAAA,OAAGA,GAAE,IAAI,CAAE;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAE;AAAC,QAAIC,IAAEE;AAAE,KAAC,UAAQF,KAAE,KAAK,MAAI,WAASA,KAAEA,KAAE,KAAK,IAAE,CAAC,GAAG,KAAKD,EAAC,GAAE,WAAS,KAAK,cAAY,KAAK,gBAAc,UAAQG,KAAEH,GAAE,kBAAgB,WAASG,MAAGA,GAAE,KAAKH,EAAC;AAAA,EAAE;AAAA,EAAC,iBAAiBA,IAAE;AAAC,QAAIC;AAAE,cAAQA,KAAE,KAAK,MAAI,WAASA,MAAGA,GAAE,OAAO,KAAK,EAAE,QAAQD,EAAC,MAAI,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,SAAK,YAAY,kBAAkB,QAAS,CAACA,IAAEC,OAAI;AAAC,WAAK,eAAeA,EAAC,MAAI,KAAK,EAAE,IAAIA,IAAE,KAAKA,EAAC,CAAC,GAAE,OAAO,KAAKA,EAAC;AAAA,IAAE,CAAE;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,QAAID;AAAE,UAAMC,KAAE,UAAQD,KAAE,KAAK,eAAa,WAASA,KAAEA,KAAE,KAAK,aAAa,KAAK,YAAY,iBAAiB;AAAE,WAAO,EAAEC,IAAE,KAAK,YAAY,aAAa,GAAEA;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,QAAID;AAAE,eAAS,KAAK,eAAa,KAAK,aAAW,KAAK,iBAAiB,IAAG,KAAK,eAAe,IAAE,GAAE,UAAQA,KAAE,KAAK,MAAI,WAASA,MAAGA,GAAE,QAAS,CAAAA,OAAG;AAAC,UAAIC;AAAE,aAAO,UAAQA,KAAED,GAAE,kBAAgB,WAASC,KAAE,SAAOA,GAAE,KAAKD,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,eAAeA,IAAE;AAAA,EAAC;AAAA,EAAC,uBAAsB;AAAC,QAAIA;AAAE,cAAQA,KAAE,KAAK,MAAI,WAASA,MAAGA,GAAE,QAAS,CAAAA,OAAG;AAAC,UAAIC;AAAE,aAAO,UAAQA,KAAED,GAAE,qBAAmB,WAASC,KAAE,SAAOA,GAAE,KAAKD,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,yBAAyBA,IAAEC,IAAEE,IAAE;AAAC,SAAK,KAAKH,IAAEG,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEH,IAAEC,IAAEE,KAAE,GAAE;AAAC,QAAID;AAAE,UAAME,KAAE,KAAK,YAAY,EAAEJ,IAAEG,EAAC;AAAE,QAAG,WAASC,MAAG,SAAKD,GAAE,SAAQ;AAAC,YAAME,MAAG,YAAU,UAAQH,KAAEC,GAAE,cAAY,WAASD,KAAE,SAAOA,GAAE,eAAaC,GAAE,YAAU,GAAG,YAAYF,IAAEE,GAAE,IAAI;AAAE,WAAK,IAAEH,IAAE,QAAMK,KAAE,KAAK,gBAAgBD,EAAC,IAAE,KAAK,aAAaA,IAAEC,EAAC,GAAE,KAAK,IAAE;AAAA,IAAI;AAAA,EAAC;AAAA,EAAC,KAAKL,IAAEC,IAAE;AAAC,QAAIE;AAAE,UAAMD,KAAE,KAAK,aAAYE,KAAEF,GAAE,EAAE,IAAIF,EAAC;AAAE,QAAG,WAASI,MAAG,KAAK,MAAIA,IAAE;AAAC,YAAMJ,KAAEE,GAAE,mBAAmBE,EAAC,GAAEC,KAAE,cAAY,OAAOL,GAAE,YAAU,EAAC,eAAcA,GAAE,UAAS,IAAE,YAAU,UAAQG,KAAEH,GAAE,cAAY,WAASG,KAAE,SAAOA,GAAE,iBAAeH,GAAE,YAAU;AAAE,WAAK,IAAEI,IAAE,KAAKA,EAAC,IAAEC,GAAE,cAAcJ,IAAED,GAAE,IAAI,GAAE,KAAK,IAAE;AAAA,IAAI;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAEC,IAAEE,IAAE;AAAC,QAAID,KAAE;AAAG,eAASF,SAAMG,KAAEA,MAAG,KAAK,YAAY,mBAAmBH,EAAC,GAAG,cAAY,GAAG,KAAKA,EAAC,GAAEC,EAAC,KAAG,KAAK,KAAK,IAAID,EAAC,KAAG,KAAK,KAAK,IAAIA,IAAEC,EAAC,GAAE,SAAKE,GAAE,WAAS,KAAK,MAAIH,OAAI,WAAS,KAAK,MAAI,KAAK,IAAE,oBAAI,QAAK,KAAK,EAAE,IAAIA,IAAEG,EAAC,MAAID,KAAE,QAAI,CAAC,KAAK,mBAAiBA,OAAI,KAAK,IAAE,KAAK,EAAE;AAAA,EAAE;AAAA,EAAC,MAAM,IAAG;AAAC,SAAK,kBAAgB;AAAG,QAAG;AAAC,YAAM,KAAK;AAAA,IAAC,SAAOF,IAAE;AAAC,cAAQ,OAAOA,EAAC;AAAA,IAAC;AAAC,UAAMA,KAAE,KAAK,eAAe;AAAE,WAAO,QAAMA,MAAG,MAAMA,IAAE,CAAC,KAAK;AAAA,EAAe;AAAA,EAAC,iBAAgB;AAAC,WAAO,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,gBAAe;AAAC,QAAIA;AAAE,QAAG,CAAC,KAAK,gBAAgB;AAAO,SAAK,YAAW,KAAK,MAAI,KAAK,EAAE,QAAS,CAACA,IAAEC,OAAI,KAAKA,EAAC,IAAED,EAAE,GAAE,KAAK,IAAE;AAAQ,QAAIC,KAAE;AAAG,UAAME,KAAE,KAAK;AAAK,QAAG;AAAC,MAAAF,KAAE,KAAK,aAAaE,EAAC,GAAEF,MAAG,KAAK,WAAWE,EAAC,GAAE,UAAQH,KAAE,KAAK,MAAI,WAASA,MAAGA,GAAE,QAAS,CAAAA,OAAG;AAAC,YAAIC;AAAE,eAAO,UAAQA,KAAED,GAAE,eAAa,WAASC,KAAE,SAAOA,GAAE,KAAKD,EAAC;AAAA,MAAC,CAAE,GAAE,KAAK,OAAOG,EAAC,KAAG,KAAK,EAAE;AAAA,IAAC,SAAOH,IAAE;AAAC,YAAMC,KAAE,OAAG,KAAK,EAAE,GAAED;AAAA,IAAC;AAAC,IAAAC,MAAG,KAAK,KAAKE,EAAC;AAAA,EAAC;AAAA,EAAC,WAAWH,IAAE;AAAA,EAAC;AAAA,EAAC,KAAKA,IAAE;AAAC,QAAIC;AAAE,cAAQA,KAAE,KAAK,MAAI,WAASA,MAAGA,GAAE,QAAS,CAAAD,OAAG;AAAC,UAAIC;AAAE,aAAO,UAAQA,KAAED,GAAE,gBAAc,WAASC,KAAE,SAAOA,GAAE,KAAKD,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,eAAa,KAAK,aAAW,MAAG,KAAK,aAAaA,EAAC,IAAG,KAAK,QAAQA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,SAAK,OAAK,oBAAI,OAAI,KAAK,kBAAgB;AAAA,EAAE;AAAA,EAAC,IAAI,iBAAgB;AAAC,WAAO,KAAK,kBAAkB;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,WAAO,KAAK;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAE;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,OAAOA,IAAE;AAAC,eAAS,KAAK,MAAI,KAAK,EAAE,QAAS,CAACA,IAAEC,OAAI,KAAK,EAAEA,IAAE,KAAKA,EAAC,GAAED,EAAC,CAAE,GAAE,KAAK,IAAE,SAAQ,KAAK,EAAE;AAAA,EAAC;AAAA,EAAC,QAAQA,IAAE;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAE;AAAA,EAAC;AAAC;AAK1rK,IAAI;AAAE,EAAE,CAAC,IAAE,MAAG,EAAE,oBAAkB,oBAAI,OAAI,EAAE,gBAAc,CAAC,GAAE,EAAE,oBAAkB,EAAC,MAAK,OAAM,GAAE,QAAM,KAAG,EAAE,EAAC,iBAAgB,EAAC,CAAC,IAAG,UAAQ,IAAE,EAAE,4BAA0B,WAAS,IAAE,IAAE,EAAE,0BAAwB,CAAC,GAAG,KAAK,OAAO;AAAE,IAAM,IAAE;AAAR,IAAe,IAAE,EAAE;AAAnB,IAAgC,IAAE,IAAE,EAAE,aAAa,YAAW,EAAC,YAAW,CAAAA,OAAGA,GAAC,CAAC,IAAE;AAAjF,IAAwF,IAAE;AAA1F,IAAkG,IAAE,QAAQ,KAAK,OAAO,IAAE,IAAI,MAAM,CAAC,CAAC;AAAtI,IAA0I,IAAE,MAAI;AAAhJ,IAAkJ,IAAE,IAAI,CAAC;AAAzJ,IAA6J,IAAE;AAA/J,IAAwK,IAAE,MAAI,EAAE,cAAc,EAAE;AAAhM,IAAkM,IAAE,CAAAA,OAAG,SAAOA,MAAG,YAAU,OAAOA,MAAG,cAAY,OAAOA;AAAxP,IAA0P,IAAE,MAAM;AAAlQ,IAA0Q,IAAE,CAAAA,OAAG,EAAEA,EAAC,KAAG,cAAY,QAAO,QAAMA,KAAE,SAAOA,GAAE,OAAO,QAAQ;AAAxU,IAA2U,IAAE;AAA7U,IAA2V,IAAE;AAA7V,IAAmZ,IAAE;AAArZ,IAA4Z,IAAE;AAA9Z,IAAma,IAAE,OAAO,KAAK,CAAC,qBAAqB,CAAC,KAAK,CAAC;AAAA,2BAAsC,GAAG;AAAvf,IAAyf,IAAE;AAA3f,IAAggB,IAAE;AAAlgB,IAAugB,IAAE;AAAzgB,IAA8iB,IAAE,CAAAA,OAAG,CAACC,OAAKE,QAAK,EAAC,YAAWH,IAAE,SAAQC,IAAE,QAAOE,GAAC;AAA9lB,IAAimB,IAAE,EAAE,CAAC;AAAtmB,IAAwmB,IAAE,EAAE,CAAC;AAA7mB,IAA+mB,IAAE,OAAO,IAAI,cAAc;AAA1oB,IAA4oB,IAAE,OAAO,IAAI,aAAa;AAAtqB,IAAwqB,IAAE,oBAAI;AAA9qB,IAAsrB,IAAE,EAAE,iBAAiB,GAAE,KAAI,MAAK,KAAE;AAAxtB,IAA0tB,IAAE,CAACH,IAAEC,OAAI;AAAC,QAAME,KAAEH,GAAE,SAAO,GAAEE,KAAE,CAAC;AAAE,MAAIE,IAAEC,KAAE,MAAIJ,KAAE,UAAQ,IAAGK,KAAE;AAAE,WAAQL,KAAE,GAAEA,KAAEE,IAAEF,MAAI;AAAC,UAAME,KAAEH,GAAEC,EAAC;AAAE,QAAIM,IAAEC,IAAEC,KAAE,IAAGC,KAAE;AAAE,WAAKA,KAAEP,GAAE,WAASG,GAAE,YAAUI,IAAEF,KAAEF,GAAE,KAAKH,EAAC,GAAE,SAAOK,MAAI,CAAAE,KAAEJ,GAAE,WAAUA,OAAI,IAAE,UAAQE,GAAE,CAAC,IAAEF,KAAE,IAAE,WAASE,GAAE,CAAC,IAAEF,KAAE,IAAE,WAASE,GAAE,CAAC,KAAG,EAAE,KAAKA,GAAE,CAAC,CAAC,MAAIJ,KAAE,OAAO,OAAKI,GAAE,CAAC,GAAE,GAAG,IAAGF,KAAE,KAAG,WAASE,GAAE,CAAC,MAAIF,KAAE,KAAGA,OAAI,IAAE,QAAME,GAAE,CAAC,KAAGF,KAAE,QAAMF,KAAEA,KAAE,GAAEK,KAAE,MAAI,WAASD,GAAE,CAAC,IAAEC,KAAE,MAAIA,KAAEH,GAAE,YAAUE,GAAE,CAAC,EAAE,QAAOD,KAAEC,GAAE,CAAC,GAAEF,KAAE,WAASE,GAAE,CAAC,IAAE,IAAE,QAAMA,GAAE,CAAC,IAAE,IAAE,KAAGF,OAAI,KAAGA,OAAI,IAAEA,KAAE,IAAEA,OAAI,KAAGA,OAAI,IAAEA,KAAE,KAAGA,KAAE,GAAEF,KAAE;AAAQ,UAAMO,KAAEL,OAAI,KAAGN,GAAEC,KAAE,CAAC,EAAE,WAAW,IAAI,IAAE,MAAI;AAAG,IAAAI,MAAGC,OAAI,IAAEH,KAAE,IAAEM,MAAG,KAAGP,GAAE,KAAKK,EAAC,GAAEJ,GAAE,MAAM,GAAEM,EAAC,IAAE,IAAEN,GAAE,MAAMM,EAAC,IAAE,IAAEE,MAAGR,KAAE,KAAG,OAAKM,MAAGP,GAAE,KAAK,MAAM,GAAED,MAAGU;AAAA,EAAE;AAAC,QAAMJ,KAAEF,MAAGL,GAAEG,EAAC,KAAG,UAAQ,MAAIF,KAAE,WAAS;AAAI,MAAG,CAAC,MAAM,QAAQD,EAAC,KAAG,CAACA,GAAE,eAAe,KAAK,EAAE,OAAM,MAAM,gCAAgC;AAAE,SAAM,CAAC,WAAS,IAAE,EAAE,WAAWO,EAAC,IAAEA,IAAEL,EAAC;AAAC;AAAE,IAAM,IAAN,MAAM,GAAC;AAAA,EAAC,YAAY,EAAC,SAAQF,IAAE,YAAWC,GAAC,GAAEE,IAAE;AAAC,QAAID;AAAE,SAAK,QAAM,CAAC;AAAE,QAAIE,KAAE,GAAEC,KAAE;AAAE,UAAMC,KAAEN,GAAE,SAAO,GAAEO,KAAE,KAAK,OAAM,CAACC,IAAEC,EAAC,IAAE,EAAET,IAAEC,EAAC;AAAE,QAAG,KAAK,KAAG,GAAE,cAAcO,IAAEL,EAAC,GAAE,EAAE,cAAY,KAAK,GAAG,SAAQ,MAAIF,IAAE;AAAC,YAAMD,KAAE,KAAK,GAAG,SAAQC,KAAED,GAAE;AAAW,MAAAC,GAAE,OAAO,GAAED,GAAE,OAAO,GAAGC,GAAE,UAAU;AAAA,IAAC;AAAC,WAAK,UAAQC,KAAE,EAAE,SAAS,MAAIK,GAAE,SAAOD,MAAG;AAAC,UAAG,MAAIJ,GAAE,UAAS;AAAC,YAAGA,GAAE,cAAc,GAAE;AAAC,gBAAMF,KAAE,CAAC;AAAE,qBAAUC,MAAKC,GAAE,kBAAkB,EAAE,KAAGD,GAAE,SAAS,CAAC,KAAGA,GAAE,WAAW,CAAC,GAAE;AAAC,kBAAME,KAAEM,GAAEJ,IAAG;AAAE,gBAAGL,GAAE,KAAKC,EAAC,GAAE,WAASE,IAAE;AAAC,oBAAMH,KAAEE,GAAE,aAAaC,GAAE,YAAY,IAAE,CAAC,EAAE,MAAM,CAAC,GAAEF,KAAE,eAAe,KAAKE,EAAC;AAAE,cAAAI,GAAE,KAAK,EAAC,MAAK,GAAE,OAAMH,IAAE,MAAKH,GAAE,CAAC,GAAE,SAAQD,IAAE,MAAK,QAAMC,GAAE,CAAC,IAAE,IAAE,QAAMA,GAAE,CAAC,IAAE,KAAG,QAAMA,GAAE,CAAC,IAAE,KAAG,EAAC,CAAC;AAAA,YAAC,MAAM,CAAAM,GAAE,KAAK,EAAC,MAAK,GAAE,OAAMH,GAAC,CAAC;AAAA,UAAC;AAAC,qBAAUH,MAAKD,GAAE,CAAAE,GAAE,gBAAgBD,EAAC;AAAA,QAAC;AAAC,YAAG,EAAE,KAAKC,GAAE,OAAO,GAAE;AAAC,gBAAMF,KAAEE,GAAE,YAAY,MAAM,CAAC,GAAED,KAAED,GAAE,SAAO;AAAE,cAAGC,KAAE,GAAE;AAAC,YAAAC,GAAE,cAAY,IAAE,EAAE,cAAY;AAAG,qBAAQC,KAAE,GAAEA,KAAEF,IAAEE,KAAI,CAAAD,GAAE,OAAOF,GAAEG,EAAC,GAAE,EAAE,CAAC,GAAE,EAAE,SAAS,GAAEI,GAAE,KAAK,EAAC,MAAK,GAAE,OAAM,EAAEH,GAAC,CAAC;AAAE,YAAAF,GAAE,OAAOF,GAAEC,EAAC,GAAE,EAAE,CAAC;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC,WAAS,MAAIC,GAAE,SAAS,KAAGA,GAAE,SAAO,EAAE,CAAAK,GAAE,KAAK,EAAC,MAAK,GAAE,OAAMH,GAAC,CAAC;AAAA,WAAM;AAAC,YAAIJ,KAAE;AAAG,eAAK,QAAMA,KAAEE,GAAE,KAAK,QAAQ,GAAEF,KAAE,CAAC,KAAI,CAAAO,GAAE,KAAK,EAAC,MAAK,GAAE,OAAMH,GAAC,CAAC,GAAEJ,MAAG,EAAE,SAAO;AAAA,MAAC;AAAC,MAAAI;AAAA,IAAG;AAAA,EAAC;AAAA,EAAC,OAAO,cAAcJ,IAAEC,IAAE;AAAC,UAAME,KAAE,EAAE,cAAc,UAAU;AAAE,WAAOA,GAAE,YAAUH,IAAEG;AAAA,EAAC;AAAC;AAAC,SAAS,EAAEH,IAAEC,IAAEE,KAAEH,IAAEE,IAAE;AAAC,MAAIE,IAAEC,IAAEC,IAAEC;AAAE,MAAGN,OAAI,EAAE,QAAOA;AAAE,MAAIO,KAAE,WAASN,KAAE,UAAQE,KAAED,GAAE,MAAI,WAASC,KAAE,SAAOA,GAAEF,EAAC,IAAEC,GAAE;AAAE,QAAMM,KAAE,EAAER,EAAC,IAAE,SAAOA,GAAE;AAAgB,UAAO,QAAMO,KAAE,SAAOA,GAAE,iBAAeC,OAAI,UAAQJ,KAAE,QAAMG,KAAE,SAAOA,GAAE,SAAO,WAASH,MAAGA,GAAE,KAAKG,IAAE,KAAE,GAAE,WAASC,KAAED,KAAE,UAAQA,KAAE,IAAIC,GAAET,EAAC,GAAEQ,GAAE,KAAKR,IAAEG,IAAED,EAAC,IAAG,WAASA,MAAG,UAAQI,MAAGC,KAAEJ,IAAG,MAAI,WAASG,KAAEA,KAAEC,GAAE,IAAE,CAAC,GAAGL,EAAC,IAAEM,KAAEL,GAAE,IAAEK,KAAG,WAASA,OAAIP,KAAE,EAAED,IAAEQ,GAAE,KAAKR,IAAEC,GAAE,MAAM,GAAEO,IAAEN,EAAC,IAAGD;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYD,IAAEC,IAAE;AAAC,SAAK,OAAK,CAAC,GAAE,KAAK,OAAK,QAAO,KAAK,OAAKD,IAAE,KAAK,OAAKC;AAAA,EAAC;AAAA,EAAC,IAAI,aAAY;AAAC,WAAO,KAAK,KAAK;AAAA,EAAU;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK,KAAK;AAAA,EAAI;AAAA,EAAC,EAAED,IAAE;AAAC,QAAIC;AAAE,UAAK,EAAC,IAAG,EAAC,SAAQE,GAAC,GAAE,OAAMD,GAAC,IAAE,KAAK,MAAKE,MAAG,UAAQH,KAAE,QAAMD,KAAE,SAAOA,GAAE,kBAAgB,WAASC,KAAEA,KAAE,GAAG,WAAWE,IAAE,IAAE;AAAE,MAAE,cAAYC;AAAE,QAAIC,KAAE,EAAE,SAAS,GAAEC,KAAE,GAAEC,KAAE,GAAEC,KAAEN,GAAE,CAAC;AAAE,WAAK,WAASM,MAAG;AAAC,UAAGF,OAAIE,GAAE,OAAM;AAAC,YAAIP;AAAE,cAAIO,GAAE,OAAKP,KAAE,IAAI,EAAEI,IAAEA,GAAE,aAAY,MAAKL,EAAC,IAAE,MAAIQ,GAAE,OAAKP,KAAE,IAAIO,GAAE,KAAKH,IAAEG,GAAE,MAAKA,GAAE,SAAQ,MAAKR,EAAC,IAAE,MAAIQ,GAAE,SAAOP,KAAE,IAAI,GAAGI,IAAE,MAAKL,EAAC,IAAG,KAAK,KAAK,KAAKC,EAAC,GAAEO,KAAEN,GAAE,EAAEK,EAAC;AAAA,MAAC;AAAC,MAAAD,QAAK,QAAME,KAAE,SAAOA,GAAE,WAASH,KAAE,EAAE,SAAS,GAAEC;AAAA,IAAI;AAAC,WAAOF;AAAA,EAAC;AAAA,EAAC,EAAEJ,IAAE;AAAC,QAAIC,KAAE;AAAE,eAAUE,MAAK,KAAK,KAAK,YAASA,OAAI,WAASA,GAAE,WAASA,GAAE,KAAKH,IAAEG,IAAEF,EAAC,GAAEA,MAAGE,GAAE,QAAQ,SAAO,KAAGA,GAAE,KAAKH,GAAEC,EAAC,CAAC,IAAGA;AAAA,EAAG;AAAC;AAAC,IAAM,IAAN,MAAM,GAAC;AAAA,EAAC,YAAYD,IAAEC,IAAEE,IAAED,IAAE;AAAC,QAAIE;AAAE,SAAK,OAAK,GAAE,KAAK,OAAK,GAAE,KAAK,OAAK,QAAO,KAAK,OAAKJ,IAAE,KAAK,OAAKC,IAAE,KAAK,OAAKE,IAAE,KAAK,UAAQD,IAAE,KAAK,IAAE,UAAQE,KAAE,QAAMF,KAAE,SAAOA,GAAE,gBAAc,WAASE,MAAGA;AAAA,EAAC;AAAA,EAAC,IAAI,OAAM;AAAC,QAAIJ,IAAEC;AAAE,WAAO,UAAQA,KAAE,UAAQD,KAAE,KAAK,SAAO,WAASA,KAAE,SAAOA,GAAE,SAAO,WAASC,KAAEA,KAAE,KAAK;AAAA,EAAC;AAAA,EAAC,IAAI,aAAY;AAAC,QAAID,KAAE,KAAK,KAAK;AAAW,UAAMC,KAAE,KAAK;AAAK,WAAO,WAASA,MAAG,QAAM,QAAMD,KAAE,SAAOA,GAAE,cAAYA,KAAEC,GAAE,aAAYD;AAAA,EAAC;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,KAAK;AAAA,EAAI;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK;AAAA,EAAI;AAAA,EAAC,KAAKA,IAAEC,KAAE,MAAK;AAAC,IAAAD,KAAE,EAAE,MAAKA,IAAEC,EAAC,GAAE,EAAED,EAAC,IAAEA,OAAI,KAAG,QAAMA,MAAG,OAAKA,MAAG,KAAK,SAAO,KAAG,KAAK,KAAK,GAAE,KAAK,OAAK,KAAGA,OAAI,KAAK,QAAMA,OAAI,KAAG,KAAK,EAAEA,EAAC,IAAE,WAASA,GAAE,aAAW,KAAK,EAAEA,EAAC,IAAE,WAASA,GAAE,WAAS,KAAK,EAAEA,EAAC,IAAE,EAAEA,EAAC,IAAE,KAAK,EAAEA,EAAC,IAAE,KAAK,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAO,KAAK,KAAK,WAAW,aAAaA,IAAE,KAAK,IAAI;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,SAAK,SAAOA,OAAI,KAAK,KAAK,GAAE,KAAK,OAAK,KAAK,EAAEA,EAAC;AAAA,EAAE;AAAA,EAAC,EAAEA,IAAE;AAAC,SAAK,SAAO,KAAG,EAAE,KAAK,IAAI,IAAE,KAAK,KAAK,YAAY,OAAKA,KAAE,KAAK,EAAE,EAAE,eAAeA,EAAC,CAAC,GAAE,KAAK,OAAKA;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,QAAIC;AAAE,UAAK,EAAC,QAAOE,IAAE,YAAWD,GAAC,IAAEF,IAAEI,KAAE,YAAU,OAAOF,KAAE,KAAK,KAAKF,EAAC,KAAG,WAASE,GAAE,OAAKA,GAAE,KAAG,EAAE,cAAcA,GAAE,GAAE,KAAK,OAAO,IAAGA;AAAG,SAAI,UAAQD,KAAE,KAAK,SAAO,WAASA,KAAE,SAAOA,GAAE,UAAQG,GAAE,MAAK,KAAK,EAAED,EAAC;AAAA,SAAM;AAAC,YAAMH,KAAE,IAAI,EAAEI,IAAE,IAAI,GAAEH,KAAED,GAAE,EAAE,KAAK,OAAO;AAAE,MAAAA,GAAE,EAAEG,EAAC,GAAE,KAAK,EAAEF,EAAC,GAAE,KAAK,OAAKD;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,KAAKA,IAAE;AAAC,QAAIC,KAAE,EAAE,IAAID,GAAE,OAAO;AAAE,WAAO,WAASC,MAAG,EAAE,IAAID,GAAE,SAAQC,KAAE,IAAI,EAAED,EAAC,CAAC,GAAEC;AAAA,EAAC;AAAA,EAAC,EAAED,IAAE;AAAC,MAAE,KAAK,IAAI,MAAI,KAAK,OAAK,CAAC,GAAE,KAAK,KAAK;AAAG,UAAMC,KAAE,KAAK;AAAK,QAAIE,IAAED,KAAE;AAAE,eAAUE,MAAKJ,GAAE,CAAAE,OAAID,GAAE,SAAOA,GAAE,KAAKE,KAAE,IAAI,GAAE,KAAK,EAAE,EAAE,CAAC,GAAE,KAAK,EAAE,EAAE,CAAC,GAAE,MAAK,KAAK,OAAO,CAAC,IAAEA,KAAEF,GAAEC,EAAC,GAAEC,GAAE,KAAKC,EAAC,GAAEF;AAAI,IAAAA,KAAED,GAAE,WAAS,KAAK,KAAKE,MAAGA,GAAE,KAAK,aAAYD,EAAC,GAAED,GAAE,SAAOC;AAAA,EAAE;AAAA,EAAC,KAAKF,KAAE,KAAK,KAAK,aAAYC,IAAE;AAAC,QAAIE;AAAE,SAAI,UAAQA,KAAE,KAAK,SAAO,WAASA,MAAGA,GAAE,KAAK,MAAK,OAAG,MAAGF,EAAC,GAAED,MAAGA,OAAI,KAAK,QAAM;AAAC,YAAMC,KAAED,GAAE;AAAY,MAAAA,GAAE,OAAO,GAAEA,KAAEC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,aAAaD,IAAE;AAAC,QAAIC;AAAE,eAAS,KAAK,SAAO,KAAK,IAAED,IAAE,UAAQC,KAAE,KAAK,SAAO,WAASA,MAAGA,GAAE,KAAK,MAAKD,EAAC;AAAA,EAAE;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYA,IAAEC,IAAEE,IAAED,IAAEE,IAAE;AAAC,SAAK,OAAK,GAAE,KAAK,OAAK,GAAE,KAAK,OAAK,QAAO,KAAK,UAAQJ,IAAE,KAAK,OAAKC,IAAE,KAAK,OAAKC,IAAE,KAAK,UAAQE,IAAED,GAAE,SAAO,KAAG,OAAKA,GAAE,CAAC,KAAG,OAAKA,GAAE,CAAC,KAAG,KAAK,OAAK,MAAMA,GAAE,SAAO,CAAC,EAAE,KAAK,IAAI,QAAM,GAAE,KAAK,UAAQA,MAAG,KAAK,OAAK;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK,QAAQ;AAAA,EAAO;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK,KAAK;AAAA,EAAI;AAAA,EAAC,KAAKH,IAAEC,KAAE,MAAKE,IAAED,IAAE;AAAC,UAAME,KAAE,KAAK;AAAQ,QAAIC,KAAE;AAAG,QAAG,WAASD,GAAE,CAAAJ,KAAE,EAAE,MAAKA,IAAEC,IAAE,CAAC,GAAEI,KAAE,CAAC,EAAEL,EAAC,KAAGA,OAAI,KAAK,QAAMA,OAAI,GAAEK,OAAI,KAAK,OAAKL;AAAA,SAAO;AAAC,YAAME,KAAEF;AAAE,UAAIM,IAAEC;AAAE,WAAIP,KAAEI,GAAE,CAAC,GAAEE,KAAE,GAAEA,KAAEF,GAAE,SAAO,GAAEE,KAAI,CAAAC,KAAE,EAAE,MAAKL,GAAEC,KAAEG,EAAC,GAAEL,IAAEK,EAAC,GAAEC,OAAI,MAAIA,KAAE,KAAK,KAAKD,EAAC,IAAGD,OAAIA,KAAE,CAAC,EAAEE,EAAC,KAAGA,OAAI,KAAK,KAAKD,EAAC,IAAGC,OAAI,IAAEP,KAAE,IAAEA,OAAI,MAAIA,OAAI,QAAMO,KAAEA,KAAE,MAAIH,GAAEE,KAAE,CAAC,IAAG,KAAK,KAAKA,EAAC,IAAEC;AAAA,IAAC;AAAC,IAAAF,MAAG,CAACH,MAAG,KAAK,EAAEF,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,IAAAA,OAAI,IAAE,KAAK,QAAQ,gBAAgB,KAAK,IAAI,IAAE,KAAK,QAAQ,aAAa,KAAK,MAAK,QAAMA,KAAEA,KAAE,EAAE;AAAA,EAAC;AAAC;AAAC,IAAM,IAAN,cAAgB,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,OAAK;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,SAAK,QAAQ,KAAK,IAAI,IAAEA,OAAI,IAAE,SAAOA;AAAA,EAAC;AAAC;AAAC,IAAM,KAAG,IAAE,EAAE,cAAY;AAAG,IAAM,KAAN,cAAiB,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,OAAK;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,IAAAA,MAAGA,OAAI,IAAE,KAAK,QAAQ,aAAa,KAAK,MAAK,EAAE,IAAE,KAAK,QAAQ,gBAAgB,KAAK,IAAI;AAAA,EAAC;AAAC;AAAC,IAAM,KAAN,cAAiB,EAAC;AAAA,EAAC,YAAYA,IAAEC,IAAEE,IAAED,IAAEE,IAAE;AAAC,UAAMJ,IAAEC,IAAEE,IAAED,IAAEE,EAAC,GAAE,KAAK,OAAK;AAAA,EAAC;AAAA,EAAC,KAAKJ,IAAEC,KAAE,MAAK;AAAC,QAAIE;AAAE,SAAIH,KAAE,UAAQG,KAAE,EAAE,MAAKH,IAAEC,IAAE,CAAC,MAAI,WAASE,KAAEA,KAAE,OAAK,EAAE;AAAO,UAAMD,KAAE,KAAK,MAAKE,KAAEJ,OAAI,KAAGE,OAAI,KAAGF,GAAE,YAAUE,GAAE,WAASF,GAAE,SAAOE,GAAE,QAAMF,GAAE,YAAUE,GAAE,SAAQG,KAAEL,OAAI,MAAIE,OAAI,KAAGE;AAAG,IAAAA,MAAG,KAAK,QAAQ,oBAAoB,KAAK,MAAK,MAAKF,EAAC,GAAEG,MAAG,KAAK,QAAQ,iBAAiB,KAAK,MAAK,MAAKL,EAAC,GAAE,KAAK,OAAKA;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,QAAIC,IAAEE;AAAE,kBAAY,OAAO,KAAK,OAAK,KAAK,KAAK,KAAK,UAAQA,KAAE,UAAQF,KAAE,KAAK,YAAU,WAASA,KAAE,SAAOA,GAAE,SAAO,WAASE,KAAEA,KAAE,KAAK,SAAQH,EAAC,IAAE,KAAK,KAAK,YAAYA,EAAC;AAAA,EAAC;AAAC;AAAC,IAAM,KAAN,MAAQ;AAAA,EAAC,YAAYA,IAAEC,IAAEE,IAAE;AAAC,SAAK,UAAQH,IAAE,KAAK,OAAK,GAAE,KAAK,OAAK,QAAO,KAAK,OAAKC,IAAE,KAAK,UAAQE;AAAA,EAAC;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK,KAAK;AAAA,EAAI;AAAA,EAAC,KAAKH,IAAE;AAAC,MAAE,MAAKA,EAAC;AAAA,EAAC;AAAC;AAAC,IAAwE,KAAG,EAAE;AAAuB,QAAM,MAAI,GAAG,GAAE,CAAC,IAAG,UAAQ,IAAE,EAAE,oBAAkB,WAAS,IAAE,IAAE,EAAE,kBAAgB,CAAC,GAAG,KAAK,OAAO;AAAE,IAAM,KAAG,CAACY,IAAEC,IAAEC,OAAI;AAAC,MAAIC,IAAEC;AAAE,QAAMC,KAAE,UAAQF,KAAE,QAAMD,KAAE,SAAOA,GAAE,iBAAe,WAASC,KAAEA,KAAEF;AAAE,MAAIK,KAAED,GAAE;AAAW,MAAG,WAASC,IAAE;AAAC,UAAMN,KAAE,UAAQI,KAAE,QAAMF,KAAE,SAAOA,GAAE,iBAAe,WAASE,KAAEA,KAAE;AAAK,IAAAC,GAAE,aAAWC,KAAE,IAAI,EAAEL,GAAE,aAAa,EAAE,GAAED,EAAC,GAAEA,IAAE,QAAO,QAAME,KAAEA,KAAE,CAAC,CAAC;AAAA,EAAC;AAAC,SAAOI,GAAE,KAAKN,EAAC,GAAEM;AAAC;AAKj4P,IAAI;AAAJ,IAAO;AAAc,IAAM,KAAN,cAAiB,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,gBAAc,EAAC,MAAK,KAAI,GAAE,KAAK,KAAG;AAAA,EAAM;AAAA,EAAC,mBAAkB;AAAC,QAAIC,IAAEC;AAAE,UAAMC,KAAE,MAAM,iBAAiB;AAAE,WAAO,UAAQF,MAAGC,KAAE,KAAK,eAAe,iBAAe,WAASD,OAAIC,GAAE,eAAaC,GAAE,aAAYA;AAAA,EAAC;AAAA,EAAC,OAAOF,IAAE;AAAC,UAAMC,KAAE,KAAK,OAAO;AAAE,SAAK,eAAa,KAAK,cAAc,cAAY,KAAK,cAAa,MAAM,OAAOD,EAAC,GAAE,KAAK,KAAG,GAAGC,IAAE,KAAK,YAAW,KAAK,aAAa;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,QAAID;AAAE,UAAM,kBAAkB,GAAE,UAAQA,KAAE,KAAK,OAAK,WAASA,MAAGA,GAAE,aAAa,IAAE;AAAA,EAAC;AAAA,EAAC,uBAAsB;AAAC,QAAIA;AAAE,UAAM,qBAAqB,GAAE,UAAQA,KAAE,KAAK,OAAK,WAASA,MAAGA,GAAE,aAAa,KAAE;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,WAAO;AAAA,EAAC;AAAC;AAAC,GAAG,YAAU,MAAG,GAAG,gBAAc,MAAG,UAAQ,KAAG,WAAW,6BAA2B,WAAS,MAAI,GAAG,KAAK,YAAW,EAAC,YAAW,GAAE,CAAC;AAAE,IAAM,KAAG,WAAW;AAA0B,QAAM,MAAI,GAAG,EAAC,YAAW,GAAE,CAAC;CAAyD,UAAQ,KAAG,WAAW,uBAAqB,WAAS,KAAG,KAAG,WAAW,qBAAmB,CAAC,GAAG,KAAK,OAAO;;;AClB/gC,IAAM,aAAN,cAAyB,GAAW;AAAA,EACvC,OAAO,aAAa;AAAA,IAChB,iBAAiB,EAAE,MAAM,SAAS,OAAO,KAAK;AAAA,IAC9C,WAAW,EAAE,MAAM,QAAQ,OAAO,KAAK;AAAA,EAC3C;AAAA,EAEA,OAAO,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiVhB,cAAc;AACV,UAAM;AACN,SAAK,YAAY,CAAC;AAClB,SAAK,YAAY;AACjB,SAAK,iBAAiB;AACtB,SAAK,YAAY;AACjB,SAAK,cAAc;AACnB,SAAK,YAAY;AACjB,SAAK,oBAAoB;AACzB,SAAK,kBAAkB;AACvB,SAAK,oBAAoB;AACzB,SAAK,kBAAkB,KAAK,gBAAgB,KAAK,IAAI;AACrD,SAAK,gBAAgB,KAAK,cAAc,KAAK,IAAI;AACjD,SAAK,qBAAqB,KAAK,mBAAmB,KAAK,IAAI;AAAA,EAC/D;AAAA,EAEA,MAAM,gBAAgBG,IAAG;AACrB,IAAAA,GAAE,eAAe;AAEjB,UAAM,EAAE,YAAY,IAAI,OAAO,QAAQ,UAAU;AACjD,UAAM,kBAAkB,MAAM,YAAY,OAAO,qBAAqB;AAEtE,SAAK,YAAY;AAAA,MACb,eAAeA,GAAE;AAAA,MACjB,eAAeA,GAAE;AAAA,MACjB,gBAAgB,gBAAgB;AAAA,MAChC,gBAAgB,gBAAgB;AAAA,MAChC,OAAO;AAAA,IACX;AAEA,WAAO,iBAAiB,aAAa,KAAK,iBAAiB,EAAE,SAAS,KAAK,CAAC;AAC5E,WAAO,iBAAiB,WAAW,KAAK,eAAe,EAAE,MAAM,MAAM,SAAS,KAAK,CAAC;AAAA,EACxF;AAAA,EAEA,gBAAgBA,IAAG;AACf,QAAI,CAAC,KAAK,UAAW;AAErB,UAAM,SAAS,KAAK,IAAIA,GAAE,UAAU,KAAK,UAAU,aAAa;AAChE,UAAM,SAAS,KAAK,IAAIA,GAAE,UAAU,KAAK,UAAU,aAAa;AAEhE,QAAI,SAAS,KAAK,SAAS,GAAG;AAC1B,WAAK,UAAU,QAAQ;AAAA,IAC3B;AAEA,UAAM,aAAa,KAAK,UAAU,kBAAkBA,GAAE,UAAU,KAAK,UAAU;AAC/E,UAAM,aAAa,KAAK,UAAU,kBAAkBA,GAAE,UAAU,KAAK,UAAU;AAE/E,UAAM,EAAE,YAAY,IAAI,OAAO,QAAQ,UAAU;AACjD,gBAAY,OAAO,kBAAkB,YAAY,UAAU;AAAA,EAC/D;AAAA,EAEA,cAAcA,IAAG;AACb,QAAI,CAAC,KAAK,UAAW;AAErB,UAAM,aAAa,KAAK,UAAU;AAElC,WAAO,oBAAoB,aAAa,KAAK,iBAAiB,EAAE,SAAS,KAAK,CAAC;AAC/E,SAAK,YAAY;AAEjB,QAAI,YAAY;AACZ,WAAK,iBAAiB;AACtB,iBAAW,MAAM;AACb,aAAK,iBAAiB;AAAA,MAC1B,GAAG,CAAC;AAAA,IACR;AAAA,EACJ;AAAA,EAEA,mBAAmB;AACf,QAAI,KAAK,aAAa;AAClB,cAAQ,IAAI,6DAA6D;AACzE;AAAA,IACJ;AAEA,QAAI,KAAK,mBAAmB;AACxB,mBAAa,KAAK,iBAAiB;AACnC,WAAK,oBAAoB;AAAA,IAC7B;AAEA,SAAK,cAAc;AAEnB,QAAI,KAAK,WAAW;AAChB,WAAK,KAAK;AAAA,IACd,OAAO;AACH,WAAK,KAAK;AAAA,IACd;AAAA,EACJ;AAAA,EAEA,OAAO;AACH,SAAK,UAAU,OAAO,WAAW,QAAQ;AACzC,SAAK,UAAU,IAAI,QAAQ;AAC3B,SAAK,YAAY;AAEjB,SAAK,oBAAoB,WAAW,MAAM;AACtC,UAAI,KAAK,UAAU,SAAS,QAAQ,GAAG;AACnC,aAAK,mBAAmB,EAAE,QAAQ,KAAK,CAAC;AAAA,MAC5C;AAAA,IACJ,GAAG,GAAG;AAAA,EACV;AAAA,EAEA,OAAO;AACH,SAAK,UAAU,OAAO,UAAU,QAAQ;AACxC,SAAK,UAAU,IAAI,SAAS;AAC5B,SAAK,YAAY;AAEjB,SAAK,oBAAoB,WAAW,MAAM;AACtC,UAAI,KAAK,UAAU,SAAS,SAAS,GAAG;AACpC,aAAK,mBAAmB,EAAE,QAAQ,KAAK,CAAC;AAAA,MAC5C;AAAA,IACJ,GAAG,GAAG;AAAA,EACV;AAAA,EAEA,mBAAmBA,IAAG;AAClB,QAAIA,GAAE,WAAW,KAAM;AAEvB,QAAI,KAAK,mBAAmB;AACxB,mBAAa,KAAK,iBAAiB;AACnC,WAAK,oBAAoB;AAAA,IAC7B;AAEA,SAAK,cAAc;AAEnB,QAAI,KAAK,UAAU,SAAS,QAAQ,GAAG;AACnC,WAAK,UAAU,OAAO,QAAQ;AAC9B,WAAK,UAAU,IAAI,QAAQ;AAE3B,UAAI,OAAO,SAAS;AAChB,cAAM,EAAE,YAAY,IAAI,OAAO,QAAQ,UAAU;AACjD,oBAAY,KAAK,6BAA6B,QAAQ;AAAA,MAC1D;AAAA,IACJ,WAAW,KAAK,UAAU,SAAS,SAAS,GAAG;AAC3C,WAAK,UAAU,OAAO,SAAS;AAE/B,UAAI,OAAO,SAAS;AAChB,cAAM,EAAE,YAAY,IAAI,OAAO,QAAQ,UAAU;AACjD,oBAAY,KAAK,6BAA6B,SAAS;AAAA,MAC3D;AAAA,IACJ,WAAW,KAAK,UAAU,SAAS,YAAY,GAAG;AAC9C,WAAK,UAAU,OAAO,YAAY;AAClC,WAAK,YAAY;AACjB,cAAQ,IAAI,2CAA2C;AAAA,IAC3D;AAAA,EACJ;AAAA,EAEA,wBAAwB;AACpB,QAAI,KAAK,UAAW;AACpB,SAAK,UAAU,IAAI,YAAY;AAAA,EACnC;AAAA,EAEA,oBAAoB;AAChB,UAAM,kBAAkB;AACxB,SAAK,iBAAiB,gBAAgB,KAAK,kBAAkB;AAE7D,QAAI,OAAO,SAAS;AAChB,YAAM,EAAE,YAAY,IAAI,OAAO,QAAQ,UAAU;AACjD,WAAK,wBAAwB,CAAC,OAAO,EAAE,SAAS,MAAM;AAClD,aAAK,kBAAkB;AAAA,MAC3B;AACA,kBAAY,GAAG,yBAAyB,KAAK,qBAAqB;AAClE,WAAK,oBAAoB,CAAC,OAAO,aAAa;AAC1C,gBAAQ,IAAI,4CAA4C,QAAQ;AAChE,aAAK,YAAY;AAAA,MACrB;AACA,kBAAY,GAAG,qBAAqB,KAAK,iBAAiB;AAAA,IAC9D;AAAA,EACJ;AAAA,EAEA,uBAAuB;AACnB,UAAM,qBAAqB;AAC3B,SAAK,oBAAoB,gBAAgB,KAAK,kBAAkB;AAEhE,QAAI,KAAK,mBAAmB;AACxB,mBAAa,KAAK,iBAAiB;AACnC,WAAK,oBAAoB;AAAA,IAC7B;AAEA,QAAI,OAAO,SAAS;AAChB,YAAM,EAAE,YAAY,IAAI,OAAO,QAAQ,UAAU;AACjD,UAAI,KAAK,uBAAuB;AAC5B,oBAAY,eAAe,yBAAyB,KAAK,qBAAqB;AAAA,MAClF;AACA,UAAI,KAAK,mBAAmB;AACxB,oBAAY,eAAe,qBAAqB,KAAK,iBAAiB;AAAA,MAC1E;AAAA,IACJ;AAAA,EACJ;AAAA,EAEA,OAAO,YAAY,MAAM;AACrB,QAAI,KAAK,gBAAgB;AACrB;AAAA,IACJ;AACA,QAAI,OAAO,SAAS;AAChB,aAAO,QAAQ,UAAU,EAAE,YAAY,OAAO,SAAS,GAAG,IAAI;AAAA,IAClE;AAAA,EACJ;AAAA,EAEA,WAAW,MAAM,SAAS;AACtB,QAAI,KAAK,eAAgB;AACzB,QAAI,OAAO,SAAS;AAChB,YAAM,EAAE,YAAY,IAAI,OAAO,QAAQ,UAAU;AACjD,cAAQ,IAAI,4BAA4B,IAAI,gBAAgB,KAAK,IAAI,CAAC,EAAE;AAExE,kBAAY,KAAK,sBAAsB,IAAI;AAE3C,UAAI,SAAS,cAAc,SAAS;AAChC,cAAM,OAAO,QAAQ,sBAAsB;AAC3C,oBAAY,KAAK,eAAe;AAAA,UAC5B,MAAM;AAAA,UACN,QAAQ;AAAA,YACJ,GAAG,KAAK;AAAA,YACR,GAAG,KAAK;AAAA,YACR,OAAO,KAAK;AAAA,YACZ,QAAQ,KAAK;AAAA,UACjB;AAAA,QACJ,CAAC;AAAA,MACL,OAAO;AACH,oBAAY,KAAK,eAAe,IAAI;AAAA,MACxC;AAAA,IACJ;AAAA,EACJ;AAAA,EAEA,WAAW,MAAM;AACb,QAAI,KAAK,eAAgB;AACzB,QAAI,OAAO,SAAS;AAChB,cAAQ,IAAI,4BAA4B,IAAI,gBAAgB,KAAK,IAAI,CAAC,EAAE;AACxE,aAAO,QAAQ,UAAU,EAAE,YAAY,KAAK,eAAe,IAAI;AAAA,IACnE;AAAA,EACJ;AAAA,EAEA,iBAAiB,MAAM;AAAA,EAEvB;AAAA,EAEA,eAAe,aAAa;AACxB,QAAI,CAAC,YAAa,QAAO;AAEzB,UAAM,SAAS;AAAA,MACX,OAAO;AAAA,MAAK,WAAW;AAAA,MACvB,QAAQ;AAAA,MAAK,WAAW;AAAA,MACxB,OAAO;AAAA,MAAK,UAAU;AAAA,MACtB,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,MACV,MAAM;AAAA,MAAK,QAAQ;AAAA,MAAK,QAAQ;AAAA,MAAK,SAAS;AAAA,MAC9C,MAAM;AAAA,IACV;AAEA,UAAM,OAAO,YAAY,MAAM,GAAG;AAClC,WAAO,IAAO,KAAK,IAAI,SAAO;AAAA,oCACF,OAAO,GAAG,KAAK,GAAG;AAAA,SAC7C,CAAC;AAAA,EACN;AAAA,EAEA,SAAS;AACL,WAAO;AAAA,6CAC8B,KAAK,eAAe;AAAA;AAAA,2CAEtB,KAAK,kBAAkB,WAAW,EAAE;AAAA,6BAClD,MAAM,KAAK,OAAO,KAAK,kBAAkB,kBAAkB,kBAAkB,QAAQ,CAAC;AAAA;AAAA;AAAA,2DAGxD,KAAK,kBAAkB,SAAS,QAAQ;AAAA;AAAA;AAAA,0BAGzE,KAAK,kBACD;AAAA;AAAA;AAAA;AAAA;AAAA,gCAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,6BAMD;AAAA;AAAA;AAAA;AAAA,gEAImC,MAAM,KAAK,OAAO,kBAAkB,KAAK,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,0BAKhF,KAAK,eAAe,KAAK,UAAU,QAAQ,CAAC;AAAA;AAAA;AAAA;AAAA,qDAIjB,MAAM,KAAK,OAAO,+BAA+B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,0BAK7E,KAAK,eAAe,KAAK,UAAU,gBAAgB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kCAM5C,CAACA,OAAM,KAAK,WAAW,YAAYA,GAAE,aAAa,CAAC;AAAA,kCACnD,MAAM,KAAK,WAAW,UAAU,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAU/D;AACJ;AAEA,eAAe,OAAO,eAAe,UAAU;;;ACppBxC,IAAM,eAAN,cAA2B,GAAW;AAAA;AAAA,EAE3C,OAAO,aAAa;AAAA,IAClB,WAAW,EAAE,MAAM,OAAO;AAAA,IAC1B,WAAW,EAAE,MAAM,OAAO;AAAA,IAC1B,aAAa,EAAE,MAAM,OAAO;AAAA,IAC5B,aAAa,EAAE,MAAM,OAAO;AAAA,IAC5B,WAAW,EAAE,MAAM,QAAQ;AAAA,IAC3B,cAAc,EAAE,MAAM,OAAO;AAAA,IAC7B,WAAW,EAAE,MAAM,QAAQ,OAAO,KAAK;AAAA,EACzC;AAAA;AAAA,EAGA,OAAO,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuPhB,cAAc;AACZ,UAAM;AACN,SAAK,YAAY;AACjB,SAAK,iBAAiB;AACtB,SAAK,YAAY;AACjB,SAAK,eAAe;AAEpB,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,cAAc;AACnB,SAAK,cAAc;AACnB,SAAK,YAAY,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC,EAAE;AACpC,SAAK,mBAAmB;AAGxB,SAAK,kBAAkB,KAAK,gBAAgB,KAAK,IAAI;AACrD,SAAK,gBAAgB,KAAK,cAAc,KAAK,IAAI;AACjD,SAAK,iBAAiB,KAAK,eAAe,KAAK,IAAI;AACnD,SAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAC/C,SAAK,cAAc,KAAK,YAAY,KAAK,IAAI;AAC7C,SAAK,qBAAqB,KAAK,mBAAmB,KAAK,IAAI;AAC3D,SAAK,sBAAsB,KAAK,oBAAoB,KAAK,IAAI;AAC7D,SAAK,uBAAuB,KAAK,qBAAqB,KAAK,IAAI;AAAA,EACjE;AAAA,EAEA,QAAQ;AACN,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,kBAAkB;AACvB,SAAK,mBAAmB;AACxB,SAAK,cAAc;AAAA,EACrB;AAAA,EAEA,MAAM,qBAAqB;AACzB,QAAI,CAAC,OAAO,QAAS;AACrB,UAAM,EAAE,YAAY,IAAI,OAAO,QAAQ,UAAU;AACjD,UAAM,SAAS,MAAM,YAAY,OAAO,2BAA2B;AAEnE,UAAM,eAAe,CAAC;AACtB,UAAM,eAAe,CAAC;AAEtB,eAAW,MAAM,QAAQ;AAErB,UAAI,GAAG,SAAS,QAAQ,EAAG;AAE3B,UAAI,OAAO,EAAE,EAAE,UAAU,SAAS,GAAG;AACjC,qBAAa,KAAK,EAAE,IAAI,MAAM,OAAO,EAAE,EAAE,KAAK,CAAC;AAAA,MACnD;AACA,UAAI,OAAO,EAAE,EAAE,UAAU,SAAS,GAAG;AACjC,qBAAa,KAAK,EAAE,IAAI,MAAM,OAAO,EAAE,EAAE,KAAK,CAAC;AAAA,MACnD;AAAA,IACJ;AAEA,SAAK,YAAY,EAAE,KAAK,cAAc,KAAK,aAAa;AAGxD,QAAI,aAAa,SAAS,EAAG,MAAK,cAAc,aAAa,CAAC,EAAE;AAChE,QAAI,aAAa,SAAS,EAAG,MAAK,cAAc,aAAa,CAAC,EAAE;AAEhE,SAAK,cAAc;AAAA,EACvB;AAAA,EAEE,MAAM,gBAAgBC,IAAG;AACvB,QAAIA,GAAE,OAAO,YAAY,WAAWA,GAAE,OAAO,YAAY,YAAYA,GAAE,OAAO,YAAY,UAAU;AAClG;AAAA,IACF;AAEA,IAAAA,GAAE,eAAe;AAEjB,UAAM,EAAE,YAAY,IAAI,OAAO,QAAQ,UAAU;AACjD,UAAM,kBAAkB,MAAM,YAAY,OAAO,qBAAqB;AAEtE,SAAK,YAAY;AAAA,MACf,eAAeA,GAAE;AAAA,MACjB,eAAeA,GAAE;AAAA,MACjB,gBAAgB,gBAAgB;AAAA,MAChC,gBAAgB,gBAAgB;AAAA,MAChC,OAAO;AAAA,IACT;AAEA,WAAO,iBAAiB,aAAa,KAAK,eAAe;AACzD,WAAO,iBAAiB,WAAW,KAAK,eAAe,EAAE,MAAM,KAAK,CAAC;AAAA,EACvE;AAAA,EAEA,gBAAgBA,IAAG;AACjB,QAAI,CAAC,KAAK,UAAW;AAErB,UAAM,SAAS,KAAK,IAAIA,GAAE,UAAU,KAAK,UAAU,aAAa;AAChE,UAAM,SAAS,KAAK,IAAIA,GAAE,UAAU,KAAK,UAAU,aAAa;AAEhE,QAAI,SAAS,KAAK,SAAS,GAAG;AAC5B,WAAK,UAAU,QAAQ;AAAA,IACzB;AAEA,UAAM,aAAa,KAAK,UAAU,kBAAkBA,GAAE,UAAU,KAAK,UAAU;AAC/E,UAAM,aAAa,KAAK,UAAU,kBAAkBA,GAAE,UAAU,KAAK,UAAU;AAE/E,UAAM,EAAE,YAAY,IAAI,OAAO,QAAQ,UAAU;AACjD,gBAAY,OAAO,kBAAkB,YAAY,UAAU;AAAA,EAC7D;AAAA,EAEA,cAAcA,IAAG;AACf,QAAI,CAAC,KAAK,UAAW;AAErB,UAAM,aAAa,KAAK,UAAU;AAElC,WAAO,oBAAoB,aAAa,KAAK,eAAe;AAC5D,SAAK,YAAY;AAEjB,QAAI,YAAY;AACd,WAAK,iBAAiB;AACtB,iBAAW,MAAM;AACf,aAAK,iBAAiB;AAAA,MACxB,GAAG,GAAG;AAAA,IACR;AAAA,EACF;AAAA,EAEA,YAAYA,IAAG;AACb,SAAK,SAASA,GAAE,OAAO;AACvB,SAAK,eAAe;AACpB,YAAQ,IAAI,kBAAkB,KAAK,QAAQ,UAAU,GAAG,OAAO;AAE/D,SAAK,cAAc;AACnB,SAAK,eAAe,KAAK,MAAM;AAC7B,YAAM,aAAa,KAAK,YAAY,cAAc,eAAe;AACjE,UAAI,cAAc,KAAK,gBAAgB;AACrC,mBAAW,MAAM;AAAA,MACnB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,qBAAqBA,IAAG;AACtB,SAAK,mBAAmBA,GAAE,OAAO;AACjC,SAAK,eAAe;AACpB,YAAQ,IAAI,wBAAwB,KAAK,gBAAgB;AACzD,SAAK,cAAc;AAAA,EACrB;AAAA,EAEA,YAAYA,IAAG;AACb,IAAAA,GAAE,eAAe;AACjB,SAAK,eAAe;AACpB,UAAM,iBAAiBA,GAAE,iBAAiB,OAAO,eAAe,QAAQ,MAAM;AAC9E,YAAQ,IAAI,yBAAyB,eAAe,UAAU,GAAG,EAAE,IAAI,KAAK;AAE5E,QAAI,eAAe;AACjB,WAAK,SAAS,cAAc,KAAK;AAEjC,YAAM,eAAeA,GAAE;AACvB,mBAAa,QAAQ,KAAK;AAAA,IAC5B;AAEA,SAAK,cAAc;AACnB,SAAK,eAAe,KAAK,MAAM;AAC7B,YAAM,aAAa,KAAK,YAAY,cAAc,eAAe;AACjE,UAAI,YAAY;AACd,mBAAW,MAAM;AACjB,mBAAW,kBAAkB,WAAW,MAAM,QAAQ,WAAW,MAAM,MAAM;AAAA,MAC/E;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,eAAeA,IAAG;AAChB,QAAIA,GAAE,QAAQ,SAAS;AACrB,MAAAA,GAAE,eAAe;AACjB,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,eAAe;AACnB,YAAQ,IAAI,qDAAqD;AACjE,QAAI,KAAK,aAAa,CAAC,KAAK,UAAU,KAAK,KAAK,CAAC,KAAK,UAAU,KAAK,GAAG;AACpE,WAAK,eAAe;AACpB;AAAA,IACJ;AAEA,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,cAAc;AAEnB,UAAM,EAAE,YAAY,IAAI,OAAO,QAAQ,UAAU;AAEjD,YAAQ,IAAI,oDAAoD;AAChE,UAAM,gBAAgB,YAAY,OAAO,sBAAsB,EAAE,UAAU,KAAK,aAAa,KAAK,KAAK,UAAU,KAAK,EAAE,CAAC;AACzH,UAAM,gBAAgB,YAAY,OAAO,sBAAsB,EAAE,UAAU,KAAK,aAAa,KAAK,KAAK,UAAU,KAAK,EAAE,CAAC;AAEzH,UAAM,CAAC,WAAW,SAAS,IAAI,MAAM,QAAQ,IAAI,CAAC,eAAe,aAAa,CAAC;AAE/E,QAAI,UAAU,WAAW,UAAU,SAAS;AACxC,cAAQ,IAAI,+DAA+D;AAC3E,WAAK,uBAAuB;AAAA,IAChC,OAAO;AACH,cAAQ,IAAI,iDAAiD;AAC7D,UAAI,aAAa,CAAC;AAClB,UAAI,CAAC,UAAU,QAAS,YAAW,KAAK,YAAY,UAAU,SAAS,SAAS,EAAE;AAClF,UAAI,CAAC,UAAU,QAAS,YAAW,KAAK,YAAY,UAAU,SAAS,SAAS,EAAE;AAClF,WAAK,eAAe,WAAW,KAAK,KAAK;AAAA,IAC7C;AAEA,SAAK,YAAY;AACjB,SAAK,cAAc;AAAA,EACvB;AAAA;AAAA,EAIE,yBAAyB;AACvB,YAAQ,IAAI,sEAAsE;AAClF,SAAK,UAAU,IAAI,aAAa;AAAA,EAClC;AAAA,EAEA,oBAAoBA,IAAG;AACrB,IAAAA,GAAE,eAAe;AACjB,QAAI,KAAK,eAAgB;AAEzB,YAAQ,IAAI,yDAAyD;AACrE,QAAI,OAAO,SAAS;AAClB,aAAO,QAAQ,UAAU,EAAE,YAAY,OAAO,qBAAqB;AAAA,IACrE;AAAA,EACF;AAAA,EAEA,cAAc;AACZ,YAAQ,IAAI,sBAAsB;AAClC,QAAI,OAAO,SAAS;AAClB,aAAO,QAAQ,UAAU,EAAE,YAAY,OAAO,kBAAkB;AAAA,IAClE;AAAA,EACF;AAAA;AAAA,EAIA,mBAAmBA,IAAG;AACpB,QAAIA,GAAE,WAAW,QAAQ,CAAC,KAAK,UAAU,SAAS,aAAa,EAAG;AAClE,SAAK,UAAU,OAAO,aAAa;AACnC,SAAK,UAAU,IAAI,QAAQ;AAC3B,WAAO,QAAQ,UAAU,EAAE,YAAY,OAAO,kBAAkB,EAAE,KAAK,eAAa;AAChF,cAAQ,IAAI,0DAA0D,SAAS;AAC/E,WAAK,sBAAsB,SAAS;AAAA,IACxC,CAAC;AAAA,EACH;AAAA;AAAA,EAGA,oBAAoB;AAClB,UAAM,kBAAkB;AACxB,SAAK,iBAAiB,gBAAgB,KAAK,kBAAkB;AAAA,EAC/D;AAAA,EAEA,uBAAuB;AACrB,UAAM,qBAAqB;AAC3B,SAAK,oBAAoB,gBAAgB,KAAK,kBAAkB;AAAA,EAClE;AAAA,EAEA,SAAS;AACP,UAAM,mBAAmB,KAAK,aAAa,CAAC,KAAK,UAAU,KAAK,KAAK,CAAC,KAAK,UAAU,KAAK;AAE1F,WAAO;AAAA,4CACiC,KAAK,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,6DAMH,KAAK,WAAW,YAAY,CAAAA,OAAK,KAAK,cAAcA,GAAE,OAAO,KAAK,cAAc,KAAK,SAAS;AAAA,0BACjI,KAAK,UAAU,IAAI,IAAI,CAAAC,OAAK,kBAAqBA,GAAE,EAAE,IAAIA,GAAE,IAAI,WAAW,CAAC;AAAA;AAAA,yGAEI,KAAK,SAAS,WAAW,CAAAD,OAAK,KAAK,YAAYA,GAAE,OAAO,KAAK,cAAc,KAAK,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,6DAKrI,KAAK,WAAW,YAAY,CAAAA,OAAK,KAAK,cAAcA,GAAE,OAAO,KAAK,cAAc,KAAK,SAAS;AAAA,0BACjI,KAAK,UAAU,IAAI,IAAI,CAAAC,OAAK,kBAAqBA,GAAE,EAAE,IAAIA,GAAE,IAAI,WAAW,CAAC;AAAA;AAAA,yGAEI,KAAK,SAAS,WAAW,CAAAD,OAAK,KAAK,YAAYA,GAAE,OAAO,KAAK,cAAc,KAAK,SAAS;AAAA;AAAA;AAAA;AAAA,yCAIzJ,KAAK,YAAY;AAAA;AAAA,mDAEP,KAAK,YAAY,cAAc,gBAAgB;AAAA,kBAChF,KAAK,YAAY,kBAAkB,SAAS;AAAA;AAAA;AAAA,mDAGX,KAAK,mBAAmB;AAAA;AAAA;AAAA,EAG3E;AACA;AAEA,eAAe,OAAO,iBAAiB,YAAY;;;ACpiB5C,IAAM,mBAAN,cAA+B,GAAW;AAAA,EAC7C,OAAO,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsQhB,OAAO,aAAa;AAAA,IAChB,mBAAmB,EAAE,MAAM,OAAO;AAAA,IAClC,eAAe,EAAE,MAAM,OAAO;AAAA,IAC9B,YAAY,EAAE,MAAM,OAAO;AAAA,IAC3B,kBAAkB,EAAE,MAAM,SAAS;AAAA,EACvC;AAAA,EAEA,cAAc;AACV,UAAM;AACN,SAAK,oBAAoB;AACzB,SAAK,gBAAgB;AACrB,SAAK,aAAa;AAClB,SAAK,mBAAmB;AAExB,SAAK,kBAAkB,KAAK,gBAAgB,KAAK,IAAI;AACrD,SAAK,gBAAgB,KAAK,cAAc,KAAK,IAAI;AAAA,EACrD;AAAA,EAEA,MAAM,oBAAoB;AACtB,UAAM,kBAAkB;AACxB,UAAM,KAAK,iBAAiB;AAG5B,SAAK,0BAA0B,YAAY,MAAM;AAC7C,WAAK,iBAAiB;AAAA,IAC1B,GAAG,GAAI;AAAA,EACX;AAAA,EAEA,uBAAuB;AACnB,UAAM,qBAAqB;AAC3B,QAAI,KAAK,yBAAyB;AAC9B,oBAAc,KAAK,uBAAuB;AAAA,IAC9C;AAAA,EACJ;AAAA,EAEA,MAAM,gBAAgBE,IAAG;AACrB,QAAIA,GAAE,OAAO,YAAY,UAAU;AAC/B;AAAA,IACJ;AAEA,IAAAA,GAAE,eAAe;AAEjB,UAAM,EAAE,YAAY,IAAI,OAAO,QAAQ,UAAU;AACjD,UAAM,kBAAkB,MAAM,YAAY,OAAO,qBAAqB;AAEtE,SAAK,YAAY;AAAA,MACb,eAAeA,GAAE;AAAA,MACjB,eAAeA,GAAE;AAAA,MACjB,gBAAgB,gBAAgB;AAAA,MAChC,gBAAgB,gBAAgB;AAAA,MAChC,OAAO;AAAA,IACX;AAEA,WAAO,iBAAiB,aAAa,KAAK,eAAe;AACzD,WAAO,iBAAiB,WAAW,KAAK,eAAe,EAAE,MAAM,KAAK,CAAC;AAAA,EACzE;AAAA,EAEA,gBAAgBA,IAAG;AACf,QAAI,CAAC,KAAK,UAAW;AAErB,UAAM,SAAS,KAAK,IAAIA,GAAE,UAAU,KAAK,UAAU,aAAa;AAChE,UAAM,SAAS,KAAK,IAAIA,GAAE,UAAU,KAAK,UAAU,aAAa;AAEhE,QAAI,SAAS,KAAK,SAAS,GAAG;AAC1B,WAAK,UAAU,QAAQ;AAAA,IAC3B;AAEA,UAAM,aAAa,KAAK,UAAU,kBAAkBA,GAAE,UAAU,KAAK,UAAU;AAC/E,UAAM,aAAa,KAAK,UAAU,kBAAkBA,GAAE,UAAU,KAAK,UAAU;AAE/E,UAAM,EAAE,YAAY,IAAI,OAAO,QAAQ,UAAU;AACjD,gBAAY,OAAO,kBAAkB,YAAY,UAAU;AAAA,EAC/D;AAAA,EAEA,cAAcA,IAAG;AACb,QAAI,CAAC,KAAK,UAAW;AAErB,UAAM,aAAa,KAAK,UAAU;AAElC,WAAO,oBAAoB,aAAa,KAAK,eAAe;AAC5D,SAAK,YAAY;AAEjB,QAAI,YAAY;AACZ,WAAK,iBAAiB;AACtB,iBAAW,MAAM;AACb,aAAK,iBAAiB;AAAA,MAC1B,GAAG,GAAG;AAAA,IACV;AAAA,EACJ;AAAA,EAEA,MAAM,mBAAmB;AACrB,QAAI,CAAC,OAAO,WAAW,KAAK,WAAY;AAExC,SAAK,aAAa;AAClB,UAAM,EAAE,YAAY,IAAI,OAAO,QAAQ,UAAU;AAEjD,QAAI;AACA,YAAM,cAAc,MAAM,YAAY,OAAO,0BAA0B;AACvE,cAAQ,IAAI,+CAA+C,WAAW;AAEtE,YAAM,UAAU,KAAK;AACrB,YAAM,aAAa,KAAK;AAExB,WAAK,oBAAoB,YAAY;AACrC,WAAK,gBAAgB,YAAY;AAGjC,UAAI,YAAY,KAAK,qBAAqB,eAAe,KAAK,eAAe;AACzE,gBAAQ,IAAI,2DAA2D;AACvE,aAAK,cAAc;AAAA,MACvB;AAGA,UAAI,KAAK,sBAAsB,aAC3B,KAAK,kBAAkB,aACvB,KAAK,kBAAkB;AACvB,gBAAQ,IAAI,sEAAsE;AAClF,mBAAW,MAAM,KAAK,eAAe,GAAG,GAAG;AAAA,MAC/C;AAAA,IACJ,SAAS,OAAO;AACZ,cAAQ,MAAM,kDAAkD,KAAK;AAAA,IACzE,UAAE;AACE,WAAK,aAAa;AAAA,IACtB;AAAA,EACJ;AAAA,EAEA,MAAM,wBAAwB;AAC1B,QAAI,CAAC,OAAO,WAAW,KAAK,sBAAsB,aAAa,KAAK,eAAgB;AAEpF,YAAQ,IAAI,wDAAwD;AACpE,UAAM,EAAE,YAAY,IAAI,OAAO,QAAQ,UAAU;AAEjD,QAAI;AACA,YAAM,SAAS,MAAM,YAAY,OAAO,0BAA0B;AAClE,cAAQ,IAAI,oDAAoD,MAAM;AAEtE,UAAI,OAAO,eAAe,WAAW;AACjC,aAAK,oBAAoB;AACzB,aAAK,cAAc;AACnB;AAAA,MACF;AAEA,UAAI,OAAO,eAAe,oBAAoB,OAAO,eAAe,YAAY,OAAO,eAAe,aAAa,OAAO,eAAe,cAAc;AACrJ,cAAM,MAAM,MAAM,YAAY,OAAO,+BAA+B;AACpE,YAAI,IAAI,WAAW,aAAa,IAAI,YAAY,MAAM;AAClD,eAAK,oBAAoB;AACzB,eAAK,cAAc;AACnB;AAAA,QACJ;AAAA,MACF;AAAA,IAKN,SAAS,OAAO;AACZ,cAAQ,MAAM,8DAA8D,KAAK;AAAA,IACrF;AAAA,EACJ;AAAA,EAEA,MAAM,oBAAoB;AACtB,QAAI,CAAC,OAAO,WAAW,KAAK,kBAAkB,aAAa,KAAK,eAAgB;AAEhF,YAAQ,IAAI,4DAA4D;AACxE,UAAM,EAAE,YAAY,IAAI,OAAO,QAAQ,UAAU;AAEjD,QAAI;AACA,YAAM,cAAc,MAAM,YAAY,OAAO,0BAA0B;AACvE,cAAQ,IAAI,sDAAsD,WAAW;AAE7E,UAAI,YAAY,WAAW,WAAW;AAClC,aAAK,gBAAgB;AACrB,aAAK,cAAc;AACnB;AAAA,MACJ;AACA,UAAI,YAAY,WAAW,oBAAoB,YAAY,WAAW,YAAY,YAAY,WAAW,aAAa,YAAY,WAAW,cAAc;AAC3J,gBAAQ,IAAI,4DAA4D;AACxE,cAAM,YAAY,OAAO,2BAA2B,kBAAkB;AAAA,MACtE;AAAA,IAKJ,SAAS,OAAO;AACZ,cAAQ,MAAM,kEAAkE,KAAK;AAAA,IACzF;AAAA,EACJ;AAAA,EAEA,MAAM,iBAAiB;AACnB,QAAI,KAAK,oBACL,KAAK,sBAAsB,aAC3B,KAAK,kBAAkB,aACvB,CAAC,KAAK,gBAAgB;AAEtB,UAAI,OAAO,SAAS;AAChB,cAAM,EAAE,YAAY,IAAI,OAAO,QAAQ,UAAU;AACjD,YAAI;AACA,gBAAM,YAAY,OAAO,4BAA4B;AACrD,kBAAQ,IAAI,oDAAoD;AAAA,QACpE,SAAS,OAAO;AACZ,kBAAQ,MAAM,8DAA8D,KAAK;AAAA,QACrF;AAAA,MACJ;AAEA,WAAK,iBAAiB;AAAA,IAC1B;AAAA,EACJ;AAAA,EAEA,cAAc;AACV,YAAQ,IAAI,sBAAsB;AAClC,QAAI,OAAO,SAAS;AAChB,aAAO,QAAQ,UAAU,EAAE,YAAY,OAAO,kBAAkB;AAAA,IACpE;AAAA,EACJ;AAAA,EAEA,SAAS;AACL,UAAM,aAAa,KAAK,sBAAsB,aAAa,KAAK,kBAAkB;AAElF,WAAO;AAAA,gDACiC,KAAK,eAAe;AAAA,sDACd,KAAK,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sDAWhB,KAAK,sBAAsB,YAAY,YAAY,EAAE;AAAA,8BAC7E,KAAK,sBAAsB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,gCAKrC;AAAA;AAAA;AAAA;AAAA;AAAA,6BAKH;AAAA;AAAA;AAAA,sDAGyB,KAAK,kBAAkB,YAAY,YAAY,EAAE;AAAA,8BACzE,KAAK,kBAAkB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,gCAKjC;AAAA;AAAA;AAAA;AAAA;AAAA,6BAKH;AAAA;AAAA;AAAA;AAAA,sBAIP,KAAK,sBAAsB,YAAY;AAAA;AAAA;AAAA,qCAGxB,KAAK,qBAAqB;AAAA;AAAA;AAAA;AAAA,wBAIvC,EAAE;AAAA;AAAA,sBAEJ,KAAK,kBAAkB,YAAY;AAAA;AAAA;AAAA,qCAGpB,KAAK,iBAAiB;AAAA;AAAA;AAAA;AAAA,wBAInC,EAAE;AAAA;AAAA,sBAEJ,aAAa;AAAA;AAAA;AAAA,qCAGE,KAAK,cAAc;AAAA;AAAA;AAAA;AAAA,wBAIhC,EAAE;AAAA;AAAA;AAAA;AAAA,EAItB;AACJ;AAEA,eAAe,OAAO,oBAAoB,gBAAgB;;;ACxiB1D,IAAM,0BAAN,MAA8B;AAAA,EAC1B,cAAc;AACV,SAAK,kBAAuB,SAAS,eAAe,kBAAkB;AACtE,SAAK,oBAAuB;AAC5B,SAAK,eAAuB;AAC5B,SAAK,aAAwB;AAC7B,SAAK,mBAAwB;AAM7B,SAAK,eAAe,CAAC,SAAS;AAC1B,cAAQ,IAAI,6DAA6D,IAAI;AAC7E,UAAI,KAAK,sBAAsB,MAAM;AACjC,gBAAQ,IAAI,oDAAoD,MAAM,iBAAiB;AACvF;AAAA,MACJ;AAEA,WAAK,gBAAgB,YAAY;AAEjC,WAAK,eAAe;AACpB,WAAK,aAAa;AAClB,WAAK,mBAAmB;AAGxB,UAAI,SAAS,UAAU;AACnB,aAAK,eAAe,SAAS,cAAc,eAAe;AAC1D,aAAK,aAAa,sBAAsB,CAAC,cAAc,KAAK,kBAAkB,SAAS;AACvF,aAAK,gBAAgB,YAAY,KAAK,YAAY;AAAA,MACtD,WAAW,SAAS,cAAc;AAC9B,aAAK,mBAAmB,SAAS,cAAc,kBAAkB;AACjE,aAAK,iBAAiB,mBAAmB,MAAM,KAAK,uBAAuB;AAC3E,aAAK,gBAAgB,YAAY,KAAK,gBAAgB;AAAA,MAC1D,OAAO;AACH,aAAK,aAAa,SAAS,cAAc,aAAa;AACtD,aAAK,gBAAgB,YAAY,KAAK,UAAU;AAChD,aAAK,WAAW,wBAAwB;AAAA,MAC5C;AAEA,WAAK,oBAAoB;AACzB,WAAK,kBAAkB,SAAS,eAAe,WAAW,IAAI;AAAA,IAClE;AAEA,YAAQ,IAAI,wCAAwC;AAEpD,SAAK,WAAW;AAEhB,QAAI,OAAO,SAAS;AAChB,YAAM,EAAE,YAAY,IAAI,OAAO,QAAQ,UAAU;AAEjD,kBAAY,GAAG,sBAAsB,CAAC,OAAO,cAAc;AACvD,gBAAQ,IAAI,kDAAkD,SAAS;AACvE,aAAK,kBAAkB,SAAS;AAAA,MACpC,CAAC;AAED,kBAAY,GAAG,eAAe,CAAC,OAAO,EAAE,QAAQ,MAAM;AAClD,gBAAQ,MAAM,+DAA+D,OAAO;AACpF,YAAI,KAAK,cAAc;AACnB,eAAK,aAAa,eAAe;AACjC,eAAK,aAAa,YAAY;AAAA,QAClC;AAAA,MACJ,CAAC;AACD,kBAAY,GAAG,4BAA4B,YAAY;AACnD,gBAAQ,IAAI,6EAA6E;AACzF,cAAM,KAAK,iBAAiB;AAC5B,aAAK,aAAa,QAAQ;AAAA,MAC9B,CAAC;AAAA,IACL;AAAA,EACJ;AAAA,EAEA,kBAAkB,eAAe;AAC7B,UAAM,QAAQ,iBAAiB,KAAK,qBAAqB;AACzD,QAAI,OAAO,SAAS;AAChB,aAAO,QAAQ,UAAU,EAAE,YAAY,KAAK,wBAAwB,KAAK;AAAA,IAC7E;AAAA,EACJ;AAAA,EAEA,MAAM,aAAa;AAGf,QAAI,OAAO,SAAS;AAChB,YAAM,YAAY,MAAM,OAAO,QAAQ,UAAU,EAAE,YAAY,OAAO,kBAAkB;AACxF,cAAQ,IAAI,6DAA6D,SAAS;AAClF,WAAK,kBAAkB,SAAS;AAAA,IACpC,OAAO;AAEH,WAAK,aAAa,QAAQ;AAAA,IAC9B;AAAA,EACJ;AAAA;AAAA,EAIA,MAAM,kBAAkB,WAAW;AAC/B,UAAM,EAAE,YAAY,IAAI,OAAO,QAAQ,UAAU;AACjD,UAAM,eAAe,MAAM,YAAY,OAAO,gCAAgC;AAE9E,QAAI,cAAc;AACd,YAAM,EAAE,WAAW,IAAI;AACvB,UAAI,YAAY;AACZ,cAAM,mBAAmB,MAAM,KAAK,iBAAiB;AACrD,YAAI,iBAAiB,SAAS;AAC1B,eAAK,uBAAuB;AAAA,QAChC,OAAO;AACH,eAAK,6BAA6B;AAAA,QACtC;AAAA,MACJ,OAAO;AACH,aAAK,uBAAuB;AAAA,MAChC;AAAA,IACJ,OAAO;AACH,YAAM,KAAK,iBAAiB;AAC5B,WAAK,aAAa,QAAQ;AAAA,IAC9B;AAAA,EACJ;AAAA;AAAA,EAGA,MAAM,+BAA+B;AAEjC,QAAI,KAAK,sBAAsB,cAAc;AACzC,cAAQ,IAAI,0EAA0E;AACtF;AAAA,IACJ;AAGA,QAAI,OAAO,SAAS;AAChB,YAAM,EAAE,YAAY,IAAI,OAAO,QAAQ,UAAU;AACjD,UAAI;AACA,cAAM,uBAAuB,MAAM,YAAY,OAAO,6BAA6B;AACnF,YAAI,sBAAsB;AACtB,kBAAQ,IAAI,sFAAsF;AAGlG,gBAAM,mBAAmB,MAAM,KAAK,iBAAiB;AACrD,cAAI,iBAAiB,SAAS;AAE1B,iBAAK,uBAAuB;AAC5B;AAAA,UACJ;AAEA,kBAAQ,IAAI,kEAAkE;AAAA,QAClF;AAAA,MACJ,SAAS,OAAO;AACZ,gBAAQ,MAAM,mEAAmE,KAAK;AAAA,MAC1F;AAAA,IACJ;AAEA,UAAM,KAAK,2BAA2B;AACtC,SAAK,aAAa,YAAY;AAAA,EAClC;AAAA,EAEA,MAAM,uBAAuB,UAAU,MAAM;AACzC,QAAI,KAAK,sBAAsB,QAAQ;AACnC,aAAO,KAAK,eAAe;AAAA,IAC/B;AAEA,UAAM,KAAK,eAAe;AAC1B,SAAK,aAAa,MAAM;AAAA,EAC5B;AAAA,EAEA,iBAAiB;AACb,QAAI,CAAC,OAAO,QAAS;AACrB,WAAO,OACF,QAAQ,UAAU,EAClB,YAAY,OAAO,wBAAwB,EAAE,OAAO,KAAK,QAAQ,GAAG,CAAC,EACrE,MAAM,MAAM;AAAA,IAAC,CAAC;AAAA,EACvB;AAAA,EAEA,MAAM,mBAAmB;AACrB,QAAI,CAAC,OAAO,QAAS;AACrB,WAAO,OACF,QAAQ,UAAU,EAClB,YAAY,OAAO,wBAAwB,EAAE,OAAO,KAAK,QAAQ,IAAI,CAAC,EACtE,MAAM,MAAM;AAAA,IAAC,CAAC;AAAA,EACvB;AAAA,EAEA,MAAM,6BAA6B;AAC/B,QAAI,CAAC,OAAO,QAAS;AACrB,WAAO,OACF,QAAQ,UAAU,EAClB,YAAY,OAAO,wBAAwB,EAAE,OAAO,KAAK,QAAQ,IAAI,CAAC,EACtE,MAAM,MAAM;AAAA,IAAC,CAAC;AAAA,EACvB;AAAA,EAEA,MAAM,mBAAmB;AACrB,QAAI,CAAC,OAAO,SAAS;AACjB,aAAO,EAAE,SAAS,KAAK;AAAA,IAC3B;AAEA,UAAM,EAAE,YAAY,IAAI,OAAO,QAAQ,UAAU;AAEjD,QAAI;AACA,YAAM,cAAc,MAAM,YAAY,OAAO,0BAA0B;AACvE,cAAQ,IAAI,2CAA2C,WAAW;AAElE,UAAI,CAAC,YAAY,YAAY;AACzB,eAAO,EAAE,SAAS,KAAK;AAAA,MAC3B;AAEA,UAAI,eAAe;AACnB,UAAI,CAAC,YAAY,cAAc,CAAC,YAAY,QAAQ;AAChD,uBAAe;AAAA,MACnB;AAEA,aAAO;AAAA,QACH,SAAS;AAAA,QACT,OAAO;AAAA,MACX;AAAA,IACJ,SAAS,OAAO;AACZ,cAAQ,MAAM,kDAAkD,KAAK;AACrE,aAAO;AAAA,QACH,SAAS;AAAA,QACT,OAAO;AAAA,MACX;AAAA,IACJ;AAAA,EACJ;AACJ;AAEA,OAAO,iBAAiB,oBAAoB,MAAM;AAC9C,MAAI,wBAAwB;AAChC,CAAC;", "names": ["t", "e", "i", "s", "n", "o", "r", "l", "h", "a", "d", "c", "t", "e", "s", "i", "n", "o", "r", "t", "e", "s", "e", "e", "p", "e"]}
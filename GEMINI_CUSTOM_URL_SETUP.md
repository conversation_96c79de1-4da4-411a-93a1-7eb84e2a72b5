# Gemini 自定義 URL 配置說明

## 概述

本文檔說明如何為 Glass 應用程式配置 Gemini API 的自定義端點 URL。

## 已完成的修改

### 1. 擴展 Gemini 提供者支持自定義 baseUrl

**文件**: `src/common/ai/providers/gemini.js`

- 為 `createSTT`、`createLLM` 和 `createStreamingLLM` 函數添加了 `baseUrl` 參數支持
- 當提供 `baseUrl` 時，會將其傳遞給 GoogleGenerativeAI 和 GoogleGenAI 客戶端
- 添加了日誌記錄以確認自定義 URL 的使用

### 2. 擴展模型狀態服務支持提供者配置

**文件**: `src/common/services/modelStateService.js`

- 添加了 `providerConfigs` 字段到用戶狀態
- 新增了 `setProviderConfig` 和 `getProviderConfig` 方法
- 修改了 `getCurrentModelInfo` 方法以包含提供者配置
- 添加了 IPC 處理器來設置和獲取提供者配置

### 3. 修改 AI 工廠函數傳遞配置

**文件**: `src/common/ai/factory.js`

- 修改了 `createSTT`、`createLLM` 和 `createStreamingLLM` 函數
- 添加了配置合併邏輯，將 `opts.config` 展開到選項中

### 4. 更新服務調用以傳遞配置

**修改的文件**:
- `src/features/ask/askService.js`
- `src/features/listen/summary/summaryService.js`
- `src/features/listen/stt/sttService.js`

在所有這些文件中，添加了 `config: modelInfo.config` 到 AI 提供者的調用選項中。

## 配置方法

### 自動配置（推薦）

運行配置腳本：

```bash
node configure-gemini-url.js
```

這將自動設置 Gemini 的自定義 URL 為：`https://iuiiyfzwvbzt.ap-southeast-1.clawcloudrun.com`

### 手動配置

1. 找到配置文件位置：`~/.pickleglass/pickle-glass-model-state.json`
2. 在 `users.default_user.providerConfigs` 中添加：

```json
{
  "users": {
    "default_user": {
      "providerConfigs": {
        "gemini": {
          "baseUrl": "https://iuiiyfzwvbzt.ap-southeast-1.clawcloudrun.com"
        }
      }
    }
  }
}
```

## 驗證配置

運行測試腳本來驗證配置：

```bash
node test-gemini-config.js
```

## 使用方法

1. 配置完成後，重新啟動 Glass 應用程式
2. 在應用程式中設置您的 Gemini API 金鑰
3. 選擇 Gemini 作為 LLM 或 STT 提供者
4. 應用程式將自動使用您配置的自定義端點

## 日誌確認

當使用 Gemini 提供者時，您應該在控制台中看到類似以下的日誌：

```
[Gemini LLM] Using custom baseUrl: https://iuiiyfzwvbzt.ap-southeast-1.clawcloudrun.com
[Gemini STT] Using custom baseUrl: https://iuiiyfzwvbzt.ap-southeast-1.clawcloudrun.com
[Gemini Streaming LLM] Using custom baseUrl: https://iuiiyfzwvbzt.ap-southeast-1.clawcloudrun.com
```

## 注意事項

- 配置更改需要重新啟動應用程式才能生效
- 自定義 URL 必須與 Gemini API 兼容
- 確保您的 API 金鑰在自定義端點上有效

## 故障排除

如果遇到問題：

1. 檢查配置是否正確保存：`node test-gemini-config.js`
2. 確認應用程式已重新啟動
3. 檢查控制台日誌中的自定義 URL 確認信息
4. 驗證 API 金鑰在自定義端點上的有效性

<!DOCTYPE html>
<html>
    <head>
        <meta http-equiv="content-security-policy" content="script-src 'self' 'unsafe-inline'" />
        <title><PERSON><PERSON> Glass Header</title>
        <style>
            html,
            body {
                margin: 0;
                padding: 0;
                overflow: hidden;
                background: transparent;
            }
        </style>
    </head>
    <body>
        <div id="header-container" tabindex="0" style="outline: none;">
        </div>

        <script type="module" src="../../public/build/header.js"></script>
        <script>
            const params = new URLSearchParams(window.location.search);
            if (params.get('glass') === 'true') {
                document.body.classList.add('has-glass');
            }
        </script>
    </body>
</html>

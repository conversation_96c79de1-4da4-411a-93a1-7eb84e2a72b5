#!/usr/bin/env node

/**
 * 測試 Gemini 配置是否正確加載的腳本
 */

const Store = require('electron-store');
const path = require('path');
const os = require('os');

// 設置 Store 配置路徑
const storeOptions = {
    name: 'pickle-glass-model-state',
    cwd: path.join(os.homedir(), '.pickleglass')
};

function testGeminiConfig() {
    try {
        // 初始化 Store
        const store = new Store(storeOptions);
        
        // 獲取當前用戶 ID（使用默認用戶）
        const userId = 'default_user';
        
        // 獲取當前狀態
        const currentState = store.get(`users.${userId}`, {});
        
        console.log('📋 當前用戶狀態:');
        console.log(JSON.stringify(currentState, null, 2));
        
        if (currentState.providerConfigs && currentState.providerConfigs.gemini) {
            console.log('');
            console.log('✅ Gemini 配置已找到:');
            console.log('📍 自定義 URL:', currentState.providerConfigs.gemini.baseUrl);
        } else {
            console.log('');
            console.log('❌ 未找到 Gemini 配置');
        }
        
        return true;
    } catch (error) {
        console.error('❌ 測試失敗:', error.message);
        return false;
    }
}

// 如果直接運行此腳本
if (require.main === module) {
    console.log('🔍 正在檢查 Gemini 配置...');
    console.log('');
    
    const success = testGeminiConfig();
    process.exit(success ? 0 : 1);
}

module.exports = { testGeminiConfig };

# electron-builder.yml

# The unique application ID
appId: com.pickle.glass

# The user-facing application name
productName: Glass

# Publish configuration for GitHub releases
publish:
    provider: github
    owner: pickle-com
    repo: glass
    releaseType: draft

# Protocols configuration for deep linking
protocols:
    name: PickleGlass Protocol
    schemes: 
        - pickleglass

# List of files to be included in the app package
files:
    - src/**/*
    - package.json
    - pickleglass_web/backend_node/**/*
    - '!**/node_modules/electron/**'
    - public/build/**/*

# Additional resources to be copied into the app's resources directory
extraResources:
    - from: pickleglass_web/out
      to: out

asarUnpack:
    - "src/assets/SystemAudioDump"

# Windows configuration
win:
    icon: src/assets/logo.ico
    target:
        - target: nsis
          arch: x64
        - target: portable
          arch: x64
    requestedExecutionLevel: asInvoker
    # Disable code signing to avoid symbolic link issues on Windows
    signAndEditExecutable: false

# NSIS installer configuration for Windows
nsis:
    oneClick: false
    perMachine: false
    allowToChangeInstallationDirectory: true
    deleteAppDataOnUninstall: true
    createDesktopShortcut: always
    createStartMenuShortcut: true
    shortcutName: Glass

# macOS specific configuration
mac:
    # The application category type
    category: public.app-category.utilities
    # Path to the .icns icon file
    icon: src/assets/logo.icns
    # Minimum macOS version (supports both Intel and Apple Silicon)
    minimumSystemVersion: '11.0'
    hardenedRuntime: true
    entitlements: entitlements.plist
    entitlementsInherit: entitlements.plist
    target:
      - target: dmg
        arch: universal
      - target: zip
        arch: universal

#!/usr/bin/env node

/**
 * 配置 Gemini API 自定義 URL 的腳本
 * 使用方法: node configure-gemini-url.js
 */

const Store = require('electron-store');
const path = require('path');
const os = require('os');

// 設置 Store 配置路徑
const storeOptions = {
    name: 'pickle-glass-model-state',
    cwd: path.join(os.homedir(), '.pickleglass')
};

const CUSTOM_GEMINI_URL = 'https://iuiiyfzwvbzt.ap-southeast-1.clawcloudrun.com';

function configureGeminiUrl() {
    try {
        // 初始化 Store
        const store = new Store(storeOptions);
        
        // 獲取當前用戶 ID（使用默認用戶）
        const userId = 'default_user';
        
        // 獲取當前狀態
        const currentState = store.get(`users.${userId}`, {
            apiKeys: {},
            selectedModels: { llm: null, stt: null },
            providerConfigs: {}
        });
        
        // 確保 providerConfigs 存在
        if (!currentState.providerConfigs) {
            currentState.providerConfigs = {};
        }
        
        // 設置 Gemini 的自定義 baseUrl
        currentState.providerConfigs.gemini = {
            baseUrl: CUSTOM_GEMINI_URL
        };
        
        // 保存配置
        store.set(`users.${userId}`, currentState);
        
        console.log('✅ Gemini 自定義 URL 配置成功！');
        console.log(`📍 自定義 URL: ${CUSTOM_GEMINI_URL}`);
        console.log('🔄 請重新啟動 Glass 應用程式以使配置生效。');
        
        return true;
    } catch (error) {
        console.error('❌ 配置失敗:', error.message);
        return false;
    }
}

// 如果直接運行此腳本
if (require.main === module) {
    console.log('🔧 正在配置 Gemini API 自定義 URL...');
    console.log(`🎯 目標 URL: ${CUSTOM_GEMINI_URL}`);
    console.log('');
    
    const success = configureGeminiUrl();
    process.exit(success ? 0 : 1);
}

module.exports = { configureGeminiUrl, CUSTOM_GEMINI_URL };

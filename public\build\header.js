// src/assets/lit-core-2.7.4.min.js
var t = window;
var i = t.ShadowRoot && (void 0 === t.ShadyCSS || t.ShadyCSS.nativeShadow) && "adoptedStyleSheets" in Document.prototype && "replace" in CSSStyleSheet.prototype;
var s = Symbol();
var e = /* @__PURE__ */ new WeakMap();
var o = class {
  constructor(t2, e2, i2) {
    if (this._$cssResult$ = true, i2 !== s) throw Error("CSSResult is not constructable. Use `unsafeCSS` or `css` instead.");
    this.cssText = t2, this.t = e2;
  }
  get styleSheet() {
    let t2 = this.i;
    const s2 = this.t;
    if (i && void 0 === t2) {
      const i2 = void 0 !== s2 && 1 === s2.length;
      i2 && (t2 = e.get(s2)), void 0 === t2 && ((this.i = t2 = new CSSStyleSheet()).replaceSync(this.cssText), i2 && e.set(s2, t2));
    }
    return t2;
  }
  toString() {
    return this.cssText;
  }
};
var n = (t2) => new o("string" == typeof t2 ? t2 : t2 + "", void 0, s);
var r = (t2, ...e2) => {
  const i2 = 1 === t2.length ? t2[0] : e2.reduce((e3, s2, i3) => e3 + ((t3) => {
    if (true === t3._$cssResult$) return t3.cssText;
    if ("number" == typeof t3) return t3;
    throw Error("Value passed to 'css' function must be a 'css' function result: " + t3 + ". Use 'unsafeCSS' to pass non-literal values, but take care to ensure page security.");
  })(s2) + t2[i3 + 1], t2[0]);
  return new o(i2, t2, s);
};
var h = (e2, s2) => {
  i ? e2.adoptedStyleSheets = s2.map((t2) => t2 instanceof CSSStyleSheet ? t2 : t2.styleSheet) : s2.forEach((s3) => {
    const i2 = document.createElement("style"), n2 = t.litNonce;
    void 0 !== n2 && i2.setAttribute("nonce", n2), i2.textContent = s3.cssText, e2.appendChild(i2);
  });
};
var l = i ? (t2) => t2 : (t2) => t2 instanceof CSSStyleSheet ? ((t3) => {
  let e2 = "";
  for (const s2 of t3.cssRules) e2 += s2.cssText;
  return n(e2);
})(t2) : t2;
var a;
var u = window;
var c = u.trustedTypes;
var d = c ? c.emptyScript : "";
var v = u.reactiveElementPolyfillSupport;
var p = { toAttribute(t2, e2) {
  switch (e2) {
    case Boolean:
      t2 = t2 ? d : null;
      break;
    case Object:
    case Array:
      t2 = null == t2 ? t2 : JSON.stringify(t2);
  }
  return t2;
}, fromAttribute(t2, e2) {
  let s2 = t2;
  switch (e2) {
    case Boolean:
      s2 = null !== t2;
      break;
    case Number:
      s2 = null === t2 ? null : Number(t2);
      break;
    case Object:
    case Array:
      try {
        s2 = JSON.parse(t2);
      } catch (t3) {
        s2 = null;
      }
  }
  return s2;
} };
var f = (t2, e2) => e2 !== t2 && (e2 == e2 || t2 == t2);
var m = { attribute: true, type: String, converter: p, reflect: false, hasChanged: f };
var y = "finalized";
var _ = class extends HTMLElement {
  constructor() {
    super(), this.o = /* @__PURE__ */ new Map(), this.isUpdatePending = false, this.hasUpdated = false, this.l = null, this.u();
  }
  static addInitializer(t2) {
    var e2;
    this.finalize(), (null !== (e2 = this.v) && void 0 !== e2 ? e2 : this.v = []).push(t2);
  }
  static get observedAttributes() {
    this.finalize();
    const t2 = [];
    return this.elementProperties.forEach((e2, s2) => {
      const i2 = this.p(s2, e2);
      void 0 !== i2 && (this.m.set(i2, s2), t2.push(i2));
    }), t2;
  }
  static createProperty(t2, e2 = m) {
    if (e2.state && (e2.attribute = false), this.finalize(), this.elementProperties.set(t2, e2), !e2.noAccessor && !this.prototype.hasOwnProperty(t2)) {
      const s2 = "symbol" == typeof t2 ? Symbol() : "__" + t2, i2 = this.getPropertyDescriptor(t2, s2, e2);
      void 0 !== i2 && Object.defineProperty(this.prototype, t2, i2);
    }
  }
  static getPropertyDescriptor(t2, e2, s2) {
    return { get() {
      return this[e2];
    }, set(i2) {
      const n2 = this[t2];
      this[e2] = i2, this.requestUpdate(t2, n2, s2);
    }, configurable: true, enumerable: true };
  }
  static getPropertyOptions(t2) {
    return this.elementProperties.get(t2) || m;
  }
  static finalize() {
    if (this.hasOwnProperty(y)) return false;
    this[y] = true;
    const t2 = Object.getPrototypeOf(this);
    if (t2.finalize(), void 0 !== t2.v && (this.v = [...t2.v]), this.elementProperties = new Map(t2.elementProperties), this.m = /* @__PURE__ */ new Map(), this.hasOwnProperty("properties")) {
      const t3 = this.properties, e2 = [...Object.getOwnPropertyNames(t3), ...Object.getOwnPropertySymbols(t3)];
      for (const s2 of e2) this.createProperty(s2, t3[s2]);
    }
    return this.elementStyles = this.finalizeStyles(this.styles), true;
  }
  static finalizeStyles(t2) {
    const e2 = [];
    if (Array.isArray(t2)) {
      const s2 = new Set(t2.flat(1 / 0).reverse());
      for (const t3 of s2) e2.unshift(l(t3));
    } else void 0 !== t2 && e2.push(l(t2));
    return e2;
  }
  static p(t2, e2) {
    const s2 = e2.attribute;
    return false === s2 ? void 0 : "string" == typeof s2 ? s2 : "string" == typeof t2 ? t2.toLowerCase() : void 0;
  }
  u() {
    var t2;
    this._ = new Promise((t3) => this.enableUpdating = t3), this._$AL = /* @__PURE__ */ new Map(), this.g(), this.requestUpdate(), null === (t2 = this.constructor.v) || void 0 === t2 || t2.forEach((t3) => t3(this));
  }
  addController(t2) {
    var e2, s2;
    (null !== (e2 = this.S) && void 0 !== e2 ? e2 : this.S = []).push(t2), void 0 !== this.renderRoot && this.isConnected && (null === (s2 = t2.hostConnected) || void 0 === s2 || s2.call(t2));
  }
  removeController(t2) {
    var e2;
    null === (e2 = this.S) || void 0 === e2 || e2.splice(this.S.indexOf(t2) >>> 0, 1);
  }
  g() {
    this.constructor.elementProperties.forEach((t2, e2) => {
      this.hasOwnProperty(e2) && (this.o.set(e2, this[e2]), delete this[e2]);
    });
  }
  createRenderRoot() {
    var t2;
    const e2 = null !== (t2 = this.shadowRoot) && void 0 !== t2 ? t2 : this.attachShadow(this.constructor.shadowRootOptions);
    return h(e2, this.constructor.elementStyles), e2;
  }
  connectedCallback() {
    var t2;
    void 0 === this.renderRoot && (this.renderRoot = this.createRenderRoot()), this.enableUpdating(true), null === (t2 = this.S) || void 0 === t2 || t2.forEach((t3) => {
      var e2;
      return null === (e2 = t3.hostConnected) || void 0 === e2 ? void 0 : e2.call(t3);
    });
  }
  enableUpdating(t2) {
  }
  disconnectedCallback() {
    var t2;
    null === (t2 = this.S) || void 0 === t2 || t2.forEach((t3) => {
      var e2;
      return null === (e2 = t3.hostDisconnected) || void 0 === e2 ? void 0 : e2.call(t3);
    });
  }
  attributeChangedCallback(t2, e2, s2) {
    this._$AK(t2, s2);
  }
  $(t2, e2, s2 = m) {
    var i2;
    const n2 = this.constructor.p(t2, s2);
    if (void 0 !== n2 && true === s2.reflect) {
      const o2 = (void 0 !== (null === (i2 = s2.converter) || void 0 === i2 ? void 0 : i2.toAttribute) ? s2.converter : p).toAttribute(e2, s2.type);
      this.l = t2, null == o2 ? this.removeAttribute(n2) : this.setAttribute(n2, o2), this.l = null;
    }
  }
  _$AK(t2, e2) {
    var s2;
    const i2 = this.constructor, n2 = i2.m.get(t2);
    if (void 0 !== n2 && this.l !== n2) {
      const t3 = i2.getPropertyOptions(n2), o2 = "function" == typeof t3.converter ? { fromAttribute: t3.converter } : void 0 !== (null === (s2 = t3.converter) || void 0 === s2 ? void 0 : s2.fromAttribute) ? t3.converter : p;
      this.l = n2, this[n2] = o2.fromAttribute(e2, t3.type), this.l = null;
    }
  }
  requestUpdate(t2, e2, s2) {
    let i2 = true;
    void 0 !== t2 && (((s2 = s2 || this.constructor.getPropertyOptions(t2)).hasChanged || f)(this[t2], e2) ? (this._$AL.has(t2) || this._$AL.set(t2, e2), true === s2.reflect && this.l !== t2 && (void 0 === this.C && (this.C = /* @__PURE__ */ new Map()), this.C.set(t2, s2))) : i2 = false), !this.isUpdatePending && i2 && (this._ = this.T());
  }
  async T() {
    this.isUpdatePending = true;
    try {
      await this._;
    } catch (t3) {
      Promise.reject(t3);
    }
    const t2 = this.scheduleUpdate();
    return null != t2 && await t2, !this.isUpdatePending;
  }
  scheduleUpdate() {
    return this.performUpdate();
  }
  performUpdate() {
    var t2;
    if (!this.isUpdatePending) return;
    this.hasUpdated, this.o && (this.o.forEach((t3, e3) => this[e3] = t3), this.o = void 0);
    let e2 = false;
    const s2 = this._$AL;
    try {
      e2 = this.shouldUpdate(s2), e2 ? (this.willUpdate(s2), null === (t2 = this.S) || void 0 === t2 || t2.forEach((t3) => {
        var e3;
        return null === (e3 = t3.hostUpdate) || void 0 === e3 ? void 0 : e3.call(t3);
      }), this.update(s2)) : this.P();
    } catch (t3) {
      throw e2 = false, this.P(), t3;
    }
    e2 && this._$AE(s2);
  }
  willUpdate(t2) {
  }
  _$AE(t2) {
    var e2;
    null === (e2 = this.S) || void 0 === e2 || e2.forEach((t3) => {
      var e3;
      return null === (e3 = t3.hostUpdated) || void 0 === e3 ? void 0 : e3.call(t3);
    }), this.hasUpdated || (this.hasUpdated = true, this.firstUpdated(t2)), this.updated(t2);
  }
  P() {
    this._$AL = /* @__PURE__ */ new Map(), this.isUpdatePending = false;
  }
  get updateComplete() {
    return this.getUpdateComplete();
  }
  getUpdateComplete() {
    return this._;
  }
  shouldUpdate(t2) {
    return true;
  }
  update(t2) {
    void 0 !== this.C && (this.C.forEach((t3, e2) => this.$(e2, this[e2], t3)), this.C = void 0), this.P();
  }
  updated(t2) {
  }
  firstUpdated(t2) {
  }
};
var b;
_[y] = true, _.elementProperties = /* @__PURE__ */ new Map(), _.elementStyles = [], _.shadowRootOptions = { mode: "open" }, null == v || v({ ReactiveElement: _ }), (null !== (a = u.reactiveElementVersions) && void 0 !== a ? a : u.reactiveElementVersions = []).push("1.6.1");
var g = window;
var w = g.trustedTypes;
var S = w ? w.createPolicy("lit-html", { createHTML: (t2) => t2 }) : void 0;
var $ = "$lit$";
var C = `lit$${(Math.random() + "").slice(9)}$`;
var T = "?" + C;
var P = `<${T}>`;
var x = document;
var A = () => x.createComment("");
var k = (t2) => null === t2 || "object" != typeof t2 && "function" != typeof t2;
var E = Array.isArray;
var M = (t2) => E(t2) || "function" == typeof (null == t2 ? void 0 : t2[Symbol.iterator]);
var U = "[ 	\n\f\r]";
var N = /<(?:(!--|\/[^a-zA-Z])|(\/?[a-zA-Z][^>\s]*)|(\/?$))/g;
var R = /-->/g;
var O = />/g;
var V = RegExp(`>|${U}(?:([^\\s"'>=/]+)(${U}*=${U}*(?:[^ 	
\f\r"'\`<>=]|("|')|))|$)`, "g");
var j = /'/g;
var z = /"/g;
var L = /^(?:script|style|textarea|title)$/i;
var I = (t2) => (e2, ...s2) => ({ _$litType$: t2, strings: e2, values: s2 });
var H = I(1);
var B = I(2);
var D = Symbol.for("lit-noChange");
var q = Symbol.for("lit-nothing");
var J = /* @__PURE__ */ new WeakMap();
var W = x.createTreeWalker(x, 129, null, false);
var Z = (t2, e2) => {
  const s2 = t2.length - 1, i2 = [];
  let n2, o2 = 2 === e2 ? "<svg>" : "", r2 = N;
  for (let e3 = 0; e3 < s2; e3++) {
    const s3 = t2[e3];
    let l3, h2, a2 = -1, d2 = 0;
    for (; d2 < s3.length && (r2.lastIndex = d2, h2 = r2.exec(s3), null !== h2); ) d2 = r2.lastIndex, r2 === N ? "!--" === h2[1] ? r2 = R : void 0 !== h2[1] ? r2 = O : void 0 !== h2[2] ? (L.test(h2[2]) && (n2 = RegExp("</" + h2[2], "g")), r2 = V) : void 0 !== h2[3] && (r2 = V) : r2 === V ? ">" === h2[0] ? (r2 = null != n2 ? n2 : N, a2 = -1) : void 0 === h2[1] ? a2 = -2 : (a2 = r2.lastIndex - h2[2].length, l3 = h2[1], r2 = void 0 === h2[3] ? V : '"' === h2[3] ? z : j) : r2 === z || r2 === j ? r2 = V : r2 === R || r2 === O ? r2 = N : (r2 = V, n2 = void 0);
    const c2 = r2 === V && t2[e3 + 1].startsWith("/>") ? " " : "";
    o2 += r2 === N ? s3 + P : a2 >= 0 ? (i2.push(l3), s3.slice(0, a2) + $ + s3.slice(a2) + C + c2) : s3 + C + (-2 === a2 ? (i2.push(void 0), e3) : c2);
  }
  const l2 = o2 + (t2[s2] || "<?>") + (2 === e2 ? "</svg>" : "");
  if (!Array.isArray(t2) || !t2.hasOwnProperty("raw")) throw Error("invalid template strings array");
  return [void 0 !== S ? S.createHTML(l2) : l2, i2];
};
var F = class _F {
  constructor({ strings: t2, _$litType$: e2 }, s2) {
    let i2;
    this.parts = [];
    let n2 = 0, o2 = 0;
    const r2 = t2.length - 1, l2 = this.parts, [h2, a2] = Z(t2, e2);
    if (this.el = _F.createElement(h2, s2), W.currentNode = this.el.content, 2 === e2) {
      const t3 = this.el.content, e3 = t3.firstChild;
      e3.remove(), t3.append(...e3.childNodes);
    }
    for (; null !== (i2 = W.nextNode()) && l2.length < r2; ) {
      if (1 === i2.nodeType) {
        if (i2.hasAttributes()) {
          const t3 = [];
          for (const e3 of i2.getAttributeNames()) if (e3.endsWith($) || e3.startsWith(C)) {
            const s3 = a2[o2++];
            if (t3.push(e3), void 0 !== s3) {
              const t4 = i2.getAttribute(s3.toLowerCase() + $).split(C), e4 = /([.?@])?(.*)/.exec(s3);
              l2.push({ type: 1, index: n2, name: e4[2], strings: t4, ctor: "." === e4[1] ? Y : "?" === e4[1] ? it : "@" === e4[1] ? st : X });
            } else l2.push({ type: 6, index: n2 });
          }
          for (const e3 of t3) i2.removeAttribute(e3);
        }
        if (L.test(i2.tagName)) {
          const t3 = i2.textContent.split(C), e3 = t3.length - 1;
          if (e3 > 0) {
            i2.textContent = w ? w.emptyScript : "";
            for (let s3 = 0; s3 < e3; s3++) i2.append(t3[s3], A()), W.nextNode(), l2.push({ type: 2, index: ++n2 });
            i2.append(t3[e3], A());
          }
        }
      } else if (8 === i2.nodeType) if (i2.data === T) l2.push({ type: 2, index: n2 });
      else {
        let t3 = -1;
        for (; -1 !== (t3 = i2.data.indexOf(C, t3 + 1)); ) l2.push({ type: 7, index: n2 }), t3 += C.length - 1;
      }
      n2++;
    }
  }
  static createElement(t2, e2) {
    const s2 = x.createElement("template");
    return s2.innerHTML = t2, s2;
  }
};
function G(t2, e2, s2 = t2, i2) {
  var n2, o2, r2, l2;
  if (e2 === D) return e2;
  let h2 = void 0 !== i2 ? null === (n2 = s2.A) || void 0 === n2 ? void 0 : n2[i2] : s2.k;
  const a2 = k(e2) ? void 0 : e2._$litDirective$;
  return (null == h2 ? void 0 : h2.constructor) !== a2 && (null === (o2 = null == h2 ? void 0 : h2._$AO) || void 0 === o2 || o2.call(h2, false), void 0 === a2 ? h2 = void 0 : (h2 = new a2(t2), h2._$AT(t2, s2, i2)), void 0 !== i2 ? (null !== (r2 = (l2 = s2).A) && void 0 !== r2 ? r2 : l2.A = [])[i2] = h2 : s2.k = h2), void 0 !== h2 && (e2 = G(t2, h2._$AS(t2, e2.values), h2, i2)), e2;
}
var K = class {
  constructor(t2, e2) {
    this._$AV = [], this._$AN = void 0, this._$AD = t2, this._$AM = e2;
  }
  get parentNode() {
    return this._$AM.parentNode;
  }
  get _$AU() {
    return this._$AM._$AU;
  }
  M(t2) {
    var e2;
    const { el: { content: s2 }, parts: i2 } = this._$AD, n2 = (null !== (e2 = null == t2 ? void 0 : t2.creationScope) && void 0 !== e2 ? e2 : x).importNode(s2, true);
    W.currentNode = n2;
    let o2 = W.nextNode(), r2 = 0, l2 = 0, h2 = i2[0];
    for (; void 0 !== h2; ) {
      if (r2 === h2.index) {
        let e3;
        2 === h2.type ? e3 = new Q(o2, o2.nextSibling, this, t2) : 1 === h2.type ? e3 = new h2.ctor(o2, h2.name, h2.strings, this, t2) : 6 === h2.type && (e3 = new et(o2, this, t2)), this._$AV.push(e3), h2 = i2[++l2];
      }
      r2 !== (null == h2 ? void 0 : h2.index) && (o2 = W.nextNode(), r2++);
    }
    return n2;
  }
  U(t2) {
    let e2 = 0;
    for (const s2 of this._$AV) void 0 !== s2 && (void 0 !== s2.strings ? (s2._$AI(t2, s2, e2), e2 += s2.strings.length - 2) : s2._$AI(t2[e2])), e2++;
  }
};
var Q = class _Q {
  constructor(t2, e2, s2, i2) {
    var n2;
    this.type = 2, this._$AH = q, this._$AN = void 0, this._$AA = t2, this._$AB = e2, this._$AM = s2, this.options = i2, this.N = null === (n2 = null == i2 ? void 0 : i2.isConnected) || void 0 === n2 || n2;
  }
  get _$AU() {
    var t2, e2;
    return null !== (e2 = null === (t2 = this._$AM) || void 0 === t2 ? void 0 : t2._$AU) && void 0 !== e2 ? e2 : this.N;
  }
  get parentNode() {
    let t2 = this._$AA.parentNode;
    const e2 = this._$AM;
    return void 0 !== e2 && 11 === (null == t2 ? void 0 : t2.nodeType) && (t2 = e2.parentNode), t2;
  }
  get startNode() {
    return this._$AA;
  }
  get endNode() {
    return this._$AB;
  }
  _$AI(t2, e2 = this) {
    t2 = G(this, t2, e2), k(t2) ? t2 === q || null == t2 || "" === t2 ? (this._$AH !== q && this._$AR(), this._$AH = q) : t2 !== this._$AH && t2 !== D && this.R(t2) : void 0 !== t2._$litType$ ? this.O(t2) : void 0 !== t2.nodeType ? this.V(t2) : M(t2) ? this.j(t2) : this.R(t2);
  }
  L(t2) {
    return this._$AA.parentNode.insertBefore(t2, this._$AB);
  }
  V(t2) {
    this._$AH !== t2 && (this._$AR(), this._$AH = this.L(t2));
  }
  R(t2) {
    this._$AH !== q && k(this._$AH) ? this._$AA.nextSibling.data = t2 : this.V(x.createTextNode(t2)), this._$AH = t2;
  }
  O(t2) {
    var e2;
    const { values: s2, _$litType$: i2 } = t2, n2 = "number" == typeof i2 ? this._$AC(t2) : (void 0 === i2.el && (i2.el = F.createElement(i2.h, this.options)), i2);
    if ((null === (e2 = this._$AH) || void 0 === e2 ? void 0 : e2._$AD) === n2) this._$AH.U(s2);
    else {
      const t3 = new K(n2, this), e3 = t3.M(this.options);
      t3.U(s2), this.V(e3), this._$AH = t3;
    }
  }
  _$AC(t2) {
    let e2 = J.get(t2.strings);
    return void 0 === e2 && J.set(t2.strings, e2 = new F(t2)), e2;
  }
  j(t2) {
    E(this._$AH) || (this._$AH = [], this._$AR());
    const e2 = this._$AH;
    let s2, i2 = 0;
    for (const n2 of t2) i2 === e2.length ? e2.push(s2 = new _Q(this.L(A()), this.L(A()), this, this.options)) : s2 = e2[i2], s2._$AI(n2), i2++;
    i2 < e2.length && (this._$AR(s2 && s2._$AB.nextSibling, i2), e2.length = i2);
  }
  _$AR(t2 = this._$AA.nextSibling, e2) {
    var s2;
    for (null === (s2 = this._$AP) || void 0 === s2 || s2.call(this, false, true, e2); t2 && t2 !== this._$AB; ) {
      const e3 = t2.nextSibling;
      t2.remove(), t2 = e3;
    }
  }
  setConnected(t2) {
    var e2;
    void 0 === this._$AM && (this.N = t2, null === (e2 = this._$AP) || void 0 === e2 || e2.call(this, t2));
  }
};
var X = class {
  constructor(t2, e2, s2, i2, n2) {
    this.type = 1, this._$AH = q, this._$AN = void 0, this.element = t2, this.name = e2, this._$AM = i2, this.options = n2, s2.length > 2 || "" !== s2[0] || "" !== s2[1] ? (this._$AH = Array(s2.length - 1).fill(new String()), this.strings = s2) : this._$AH = q;
  }
  get tagName() {
    return this.element.tagName;
  }
  get _$AU() {
    return this._$AM._$AU;
  }
  _$AI(t2, e2 = this, s2, i2) {
    const n2 = this.strings;
    let o2 = false;
    if (void 0 === n2) t2 = G(this, t2, e2, 0), o2 = !k(t2) || t2 !== this._$AH && t2 !== D, o2 && (this._$AH = t2);
    else {
      const i3 = t2;
      let r2, l2;
      for (t2 = n2[0], r2 = 0; r2 < n2.length - 1; r2++) l2 = G(this, i3[s2 + r2], e2, r2), l2 === D && (l2 = this._$AH[r2]), o2 || (o2 = !k(l2) || l2 !== this._$AH[r2]), l2 === q ? t2 = q : t2 !== q && (t2 += (null != l2 ? l2 : "") + n2[r2 + 1]), this._$AH[r2] = l2;
    }
    o2 && !i2 && this.I(t2);
  }
  I(t2) {
    t2 === q ? this.element.removeAttribute(this.name) : this.element.setAttribute(this.name, null != t2 ? t2 : "");
  }
};
var Y = class extends X {
  constructor() {
    super(...arguments), this.type = 3;
  }
  I(t2) {
    this.element[this.name] = t2 === q ? void 0 : t2;
  }
};
var tt = w ? w.emptyScript : "";
var it = class extends X {
  constructor() {
    super(...arguments), this.type = 4;
  }
  I(t2) {
    t2 && t2 !== q ? this.element.setAttribute(this.name, tt) : this.element.removeAttribute(this.name);
  }
};
var st = class extends X {
  constructor(t2, e2, s2, i2, n2) {
    super(t2, e2, s2, i2, n2), this.type = 5;
  }
  _$AI(t2, e2 = this) {
    var s2;
    if ((t2 = null !== (s2 = G(this, t2, e2, 0)) && void 0 !== s2 ? s2 : q) === D) return;
    const i2 = this._$AH, n2 = t2 === q && i2 !== q || t2.capture !== i2.capture || t2.once !== i2.once || t2.passive !== i2.passive, o2 = t2 !== q && (i2 === q || n2);
    n2 && this.element.removeEventListener(this.name, this, i2), o2 && this.element.addEventListener(this.name, this, t2), this._$AH = t2;
  }
  handleEvent(t2) {
    var e2, s2;
    "function" == typeof this._$AH ? this._$AH.call(null !== (s2 = null === (e2 = this.options) || void 0 === e2 ? void 0 : e2.host) && void 0 !== s2 ? s2 : this.element, t2) : this._$AH.handleEvent(t2);
  }
};
var et = class {
  constructor(t2, e2, s2) {
    this.element = t2, this.type = 6, this._$AN = void 0, this._$AM = e2, this.options = s2;
  }
  get _$AU() {
    return this._$AM._$AU;
  }
  _$AI(t2) {
    G(this, t2);
  }
};
var nt = g.litHtmlPolyfillSupport;
null == nt || nt(F, Q), (null !== (b = g.litHtmlVersions) && void 0 !== b ? b : g.litHtmlVersions = []).push("2.7.3");
var rt = (t2, e2, s2) => {
  var i2, n2;
  const o2 = null !== (i2 = null == s2 ? void 0 : s2.renderBefore) && void 0 !== i2 ? i2 : e2;
  let r2 = o2._$litPart$;
  if (void 0 === r2) {
    const t3 = null !== (n2 = null == s2 ? void 0 : s2.renderBefore) && void 0 !== n2 ? n2 : null;
    o2._$litPart$ = r2 = new Q(e2.insertBefore(A(), t3), t3, void 0, null != s2 ? s2 : {});
  }
  return r2._$AI(t2), r2;
};
var ht;
var lt;
var ut = class extends _ {
  constructor() {
    super(...arguments), this.renderOptions = { host: this }, this.st = void 0;
  }
  createRenderRoot() {
    var t2, e2;
    const s2 = super.createRenderRoot();
    return null !== (t2 = (e2 = this.renderOptions).renderBefore) && void 0 !== t2 || (e2.renderBefore = s2.firstChild), s2;
  }
  update(t2) {
    const e2 = this.render();
    this.hasUpdated || (this.renderOptions.isConnected = this.isConnected), super.update(t2), this.st = rt(e2, this.renderRoot, this.renderOptions);
  }
  connectedCallback() {
    var t2;
    super.connectedCallback(), null === (t2 = this.st) || void 0 === t2 || t2.setConnected(true);
  }
  disconnectedCallback() {
    var t2;
    super.disconnectedCallback(), null === (t2 = this.st) || void 0 === t2 || t2.setConnected(false);
  }
  render() {
    return D;
  }
};
ut.finalized = true, ut._$litElement$ = true, null === (ht = globalThis.litElementHydrateSupport) || void 0 === ht || ht.call(globalThis, { LitElement: ut });
var ct = globalThis.litElementPolyfillSupport;
null == ct || ct({ LitElement: ut });
(null !== (lt = globalThis.litElementVersions) && void 0 !== lt ? lt : globalThis.litElementVersions = []).push("3.3.2");

// src/app/MainHeader.js
var MainHeader = class extends ut {
  static properties = {
    isSessionActive: { type: Boolean, state: true },
    shortcuts: { type: Object, state: true }
  };
  static styles = r`
        :host {
            display: flex;
            transform: translate3d(0, 0, 0);
            backface-visibility: hidden;
            transition: transform 0.2s cubic-bezier(0.23, 1, 0.32, 1), opacity 0.2s ease-out;
            will-change: transform, opacity;
        }

        :host(.hiding) {
            animation: slideUp 0.3s cubic-bezier(0.4, 0, 0.6, 1) forwards;
        }

        :host(.showing) {
            animation: slideDown 0.35s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
        }

        :host(.sliding-in) {
            animation: fadeIn 0.2s ease-out forwards;
        }

        :host(.hidden) {
            opacity: 0;
            transform: translateY(-150%) scale(0.85);
            pointer-events: none;
        }

        @keyframes slideUp {
            0% {
                opacity: 1;
                transform: translateY(0) scale(1);
                filter: blur(0px);
            }
            30% {
                opacity: 0.7;
                transform: translateY(-20%) scale(0.98);
                filter: blur(0.5px);
            }
            70% {
                opacity: 0.3;
                transform: translateY(-80%) scale(0.92);
                filter: blur(1.5px);
            }
            100% {
                opacity: 0;
                transform: translateY(-150%) scale(0.85);
                filter: blur(2px);
            }
        }

        @keyframes slideDown {
            0% {
                opacity: 0;
                transform: translateY(-150%) scale(0.85);
                filter: blur(2px);
            }
            30% {
                opacity: 0.5;
                transform: translateY(-50%) scale(0.92);
                filter: blur(1px);
            }
            65% {
                opacity: 0.9;
                transform: translateY(-5%) scale(0.99);
                filter: blur(0.2px);
            }
            85% {
                opacity: 0.98;
                transform: translateY(2%) scale(1.005);
                filter: blur(0px);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
                filter: blur(0px);
            }
        }

        @keyframes fadeIn {
            0% {
                opacity: 0;
            }
            100% {
                opacity: 1;
            }
        }

        * {
            font-family: 'Helvetica Neue', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            cursor: default;
            user-select: none;
        }

        .header {
            width: max-content;
            height: 47px;
            padding: 2px 10px 2px 13px;
            background: transparent;
            overflow: hidden;
            border-radius: 9000px;
            /* backdrop-filter: blur(1px); */
            justify-content: space-between;
            align-items: center;
            display: inline-flex;
            box-sizing: border-box;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0; left: 0; right: 0; bottom: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 9000px;
            z-index: -1;
        }

        .header::after {
            content: '';
            position: absolute;
            top: 0; left: 0; right: 0; bottom: 0;
            border-radius: 9000px;
            padding: 1px;
            background: linear-gradient(169deg, rgba(255, 255, 255, 0.17) 0%, rgba(255, 255, 255, 0.08) 50%, rgba(255, 255, 255, 0.17) 100%); 
            -webkit-mask:
                linear-gradient(#fff 0 0) content-box,
                linear-gradient(#fff 0 0);
            -webkit-mask-composite: destination-out;
            mask-composite: exclude;
            pointer-events: none;
        }

        .listen-button {
            height: 26px;
            padding: 0 13px;
            background: transparent;
            border-radius: 9000px;
            justify-content: center;
            width: 78px;
            align-items: center;
            gap: 6px;
            display: flex;
            border: none;
            cursor: pointer;
            position: relative;
        }

        .listen-button.active::before {
            background: rgba(215, 0, 0, 0.5);
        }

        .listen-button.active:hover::before {
            background: rgba(255, 20, 20, 0.6);
        }

        .listen-button:hover::before {
            background: rgba(255, 255, 255, 0.18);
        }

        .listen-button::before {
            content: '';
            position: absolute;
            top: 0; left: 0; right: 0; bottom: 0;
            background: rgba(255, 255, 255, 0.14);
            border-radius: 9000px;
            z-index: -1;
            transition: background 0.15s ease;
        }

        .listen-button::after {
            content: '';
            position: absolute;
            top: 0; left: 0; right: 0; bottom: 0;
            border-radius: 9000px;
            padding: 1px;
            background: linear-gradient(169deg, rgba(255, 255, 255, 0.17) 0%, rgba(255, 255, 255, 0.08) 50%, rgba(255, 255, 255, 0.17) 100%);
            -webkit-mask:
                linear-gradient(#fff 0 0) content-box,
                linear-gradient(#fff 0 0);
            -webkit-mask-composite: destination-out;
            mask-composite: exclude;
            pointer-events: none;
        }

        .header-actions {
            height: 26px;
            box-sizing: border-box;
            justify-content: flex-start;
            align-items: center;
            gap: 9px;
            display: flex;
            padding: 0 8px;
            border-radius: 6px;
            transition: background 0.15s ease;
        }

        .header-actions:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .ask-action {
            margin-left: 4px;
        }

        .action-button,
        .action-text {
            padding-bottom: 1px;
            justify-content: center;
            align-items: center;
            gap: 10px;
            display: flex;
        }

        .action-text-content {
            color: white;
            font-size: 12px;
            font-family: 'Helvetica Neue', sans-serif;
            font-weight: 500; /* Medium */
            word-wrap: break-word;
        }

        .icon-container {
            justify-content: flex-start;
            align-items: center;
            gap: 4px;
            display: flex;
        }

        .icon-container.ask-icons svg,
        .icon-container.showhide-icons svg {
            width: 12px;
            height: 12px;
        }

        .listen-icon svg {
            width: 12px;
            height: 11px;
            position: relative;
            top: 1px;
        }

        .icon-box {
            color: white;
            font-size: 12px;
            font-family: 'Helvetica Neue', sans-serif;
            font-weight: 500;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 13%;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .settings-button {
            padding: 5px;
            border-radius: 50%;
            background: transparent;
            transition: background 0.15s ease;
            color: white;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .settings-button:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .settings-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 3px;
        }

        .settings-icon svg {
            width: 16px;
            height: 16px;
        }

        /* ────────────────[ GLASS BYPASS ]─────────────── */
        :host-context(body.has-glass) .header,
        :host-context(body.has-glass) .listen-button,
        :host-context(body.has-glass) .header-actions,
        :host-context(body.has-glass) .settings-button {
            background: transparent !important;
            filter: none !important;
            box-shadow: none !important;
            backdrop-filter: none !important;
        }
        :host-context(body.has-glass) .icon-box {
            background: transparent !important;
            border: none !important;
        }

        :host-context(body.has-glass) .header::before,
        :host-context(body.has-glass) .header::after,
        :host-context(body.has-glass) .listen-button::before,
        :host-context(body.has-glass) .listen-button::after {
            display: none !important;
        }

        :host-context(body.has-glass) .header-actions:hover,
        :host-context(body.has-glass) .settings-button:hover,
        :host-context(body.has-glass) .listen-button:hover::before {
            background: transparent !important;
        }
        :host-context(body.has-glass) * {
            animation: none !important;
            transition: none !important;
            transform: none !important;
            filter: none !important;
            backdrop-filter: none !important;
            box-shadow: none !important;
        }

        :host-context(body.has-glass) .header,
        :host-context(body.has-glass) .listen-button,
        :host-context(body.has-glass) .header-actions,
        :host-context(body.has-glass) .settings-button,
        :host-context(body.has-glass) .icon-box {
            border-radius: 0 !important;
        }
        :host-context(body.has-glass) {
            animation: none !important;
            transition: none !important;
            transform: none !important;
            will-change: auto !important;
        }
        `;
  constructor() {
    super();
    this.shortcuts = {};
    this.dragState = null;
    this.wasJustDragged = false;
    this.isVisible = true;
    this.isAnimating = false;
    this.hasSlidIn = false;
    this.settingsHideTimer = null;
    this.isSessionActive = false;
    this.animationEndTimer = null;
    this.handleMouseMove = this.handleMouseMove.bind(this);
    this.handleMouseUp = this.handleMouseUp.bind(this);
    this.handleAnimationEnd = this.handleAnimationEnd.bind(this);
  }
  async handleMouseDown(e2) {
    e2.preventDefault();
    const { ipcRenderer } = window.require("electron");
    const initialPosition = await ipcRenderer.invoke("get-header-position");
    this.dragState = {
      initialMouseX: e2.screenX,
      initialMouseY: e2.screenY,
      initialWindowX: initialPosition.x,
      initialWindowY: initialPosition.y,
      moved: false
    };
    window.addEventListener("mousemove", this.handleMouseMove, { capture: true });
    window.addEventListener("mouseup", this.handleMouseUp, { once: true, capture: true });
  }
  handleMouseMove(e2) {
    if (!this.dragState) return;
    const deltaX = Math.abs(e2.screenX - this.dragState.initialMouseX);
    const deltaY = Math.abs(e2.screenY - this.dragState.initialMouseY);
    if (deltaX > 3 || deltaY > 3) {
      this.dragState.moved = true;
    }
    const newWindowX = this.dragState.initialWindowX + (e2.screenX - this.dragState.initialMouseX);
    const newWindowY = this.dragState.initialWindowY + (e2.screenY - this.dragState.initialMouseY);
    const { ipcRenderer } = window.require("electron");
    ipcRenderer.invoke("move-header-to", newWindowX, newWindowY);
  }
  handleMouseUp(e2) {
    if (!this.dragState) return;
    const wasDragged = this.dragState.moved;
    window.removeEventListener("mousemove", this.handleMouseMove, { capture: true });
    this.dragState = null;
    if (wasDragged) {
      this.wasJustDragged = true;
      setTimeout(() => {
        this.wasJustDragged = false;
      }, 0);
    }
  }
  toggleVisibility() {
    if (this.isAnimating) {
      console.log("[MainHeader] Animation already in progress, ignoring toggle");
      return;
    }
    if (this.animationEndTimer) {
      clearTimeout(this.animationEndTimer);
      this.animationEndTimer = null;
    }
    this.isAnimating = true;
    if (this.isVisible) {
      this.hide();
    } else {
      this.show();
    }
  }
  hide() {
    this.classList.remove("showing", "hidden");
    this.classList.add("hiding");
    this.isVisible = false;
    this.animationEndTimer = setTimeout(() => {
      if (this.classList.contains("hiding")) {
        this.handleAnimationEnd({ target: this });
      }
    }, 350);
  }
  show() {
    this.classList.remove("hiding", "hidden");
    this.classList.add("showing");
    this.isVisible = true;
    this.animationEndTimer = setTimeout(() => {
      if (this.classList.contains("showing")) {
        this.handleAnimationEnd({ target: this });
      }
    }, 400);
  }
  handleAnimationEnd(e2) {
    if (e2.target !== this) return;
    if (this.animationEndTimer) {
      clearTimeout(this.animationEndTimer);
      this.animationEndTimer = null;
    }
    this.isAnimating = false;
    if (this.classList.contains("hiding")) {
      this.classList.remove("hiding");
      this.classList.add("hidden");
      if (window.require) {
        const { ipcRenderer } = window.require("electron");
        ipcRenderer.send("header-animation-complete", "hidden");
      }
    } else if (this.classList.contains("showing")) {
      this.classList.remove("showing");
      if (window.require) {
        const { ipcRenderer } = window.require("electron");
        ipcRenderer.send("header-animation-complete", "visible");
      }
    } else if (this.classList.contains("sliding-in")) {
      this.classList.remove("sliding-in");
      this.hasSlidIn = true;
      console.log("[MainHeader] Slide-in animation completed");
    }
  }
  startSlideInAnimation() {
    if (this.hasSlidIn) return;
    this.classList.add("sliding-in");
  }
  connectedCallback() {
    super.connectedCallback();
    this.addEventListener("animationend", this.handleAnimationEnd);
    if (window.require) {
      const { ipcRenderer } = window.require("electron");
      this._sessionStateListener = (event, { isActive }) => {
        this.isSessionActive = isActive;
      };
      ipcRenderer.on("session-state-changed", this._sessionStateListener);
      this._shortcutListener = (event, keybinds) => {
        console.log("[MainHeader] Received updated shortcuts:", keybinds);
        this.shortcuts = keybinds;
      };
      ipcRenderer.on("shortcuts-updated", this._shortcutListener);
    }
  }
  disconnectedCallback() {
    super.disconnectedCallback();
    this.removeEventListener("animationend", this.handleAnimationEnd);
    if (this.animationEndTimer) {
      clearTimeout(this.animationEndTimer);
      this.animationEndTimer = null;
    }
    if (window.require) {
      const { ipcRenderer } = window.require("electron");
      if (this._sessionStateListener) {
        ipcRenderer.removeListener("session-state-changed", this._sessionStateListener);
      }
      if (this._shortcutListener) {
        ipcRenderer.removeListener("shortcuts-updated", this._shortcutListener);
      }
    }
  }
  invoke(channel, ...args) {
    if (this.wasJustDragged) {
      return;
    }
    if (window.require) {
      window.require("electron").ipcRenderer.invoke(channel, ...args);
    }
  }
  showWindow(name, element) {
    if (this.wasJustDragged) return;
    if (window.require) {
      const { ipcRenderer } = window.require("electron");
      console.log(`[MainHeader] showWindow('${name}') called at ${Date.now()}`);
      ipcRenderer.send("cancel-hide-window", name);
      if (name === "settings" && element) {
        const rect = element.getBoundingClientRect();
        ipcRenderer.send("show-window", {
          name: "settings",
          bounds: {
            x: rect.left,
            y: rect.top,
            width: rect.width,
            height: rect.height
          }
        });
      } else {
        ipcRenderer.send("show-window", name);
      }
    }
  }
  hideWindow(name) {
    if (this.wasJustDragged) return;
    if (window.require) {
      console.log(`[MainHeader] hideWindow('${name}') called at ${Date.now()}`);
      window.require("electron").ipcRenderer.send("hide-window", name);
    }
  }
  cancelHideWindow(name) {
  }
  renderShortcut(accelerator) {
    if (!accelerator) return H``;
    const keyMap = {
      "Cmd": "\u2318",
      "Command": "\u2318",
      "Ctrl": "\u2303",
      "Control": "\u2303",
      "Alt": "\u2325",
      "Option": "\u2325",
      "Shift": "\u21E7",
      "Enter": "\u21B5",
      "Backspace": "\u232B",
      "Delete": "\u2326",
      "Tab": "\u21E5",
      "Escape": "\u238B",
      "Up": "\u2191",
      "Down": "\u2193",
      "Left": "\u2190",
      "Right": "\u2192",
      "\\": H`<svg viewBox="0 0 6 12" fill="none" xmlns="http://www.w3.org/2000/svg" style="width:6px; height:12px;"><path d="M1.5 1.3L5.1 10.6" stroke="white" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/></svg>`
    };
    const keys = accelerator.split("+");
    return H`${keys.map((key) => H`
            <div class="icon-box">${keyMap[key] || key}</div>
        `)}`;
  }
  render() {
    return H`
            <div class="header" @mousedown=${this.handleMouseDown}>
                <button 
                    class="listen-button ${this.isSessionActive ? "active" : ""}"
                    @click=${() => this.invoke(this.isSessionActive ? "close-session" : "toggle-feature", "listen")}
                >
                    <div class="action-text">
                        <div class="action-text-content">${this.isSessionActive ? "Stop" : "Listen"}</div>
                    </div>
                    <div class="listen-icon">
                        ${this.isSessionActive ? H`
                                <svg width="9" height="9" viewBox="0 0 9 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect width="9" height="9" rx="1" fill="white"/>
                                </svg>

                            ` : H`
                                <svg width="12" height="11" viewBox="0 0 12 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M1.69922 2.7515C1.69922 2.37153 2.00725 2.0635 2.38722 2.0635H2.73122C3.11119 2.0635 3.41922 2.37153 3.41922 2.7515V8.2555C3.41922 8.63547 3.11119 8.9435 2.73122 8.9435H2.38722C2.00725 8.9435 1.69922 8.63547 1.69922 8.2555V2.7515Z" fill="white"/>
                                    <path d="M5.13922 1.3755C5.13922 0.995528 5.44725 0.6875 5.82722 0.6875H6.17122C6.55119 0.6875 6.85922 0.995528 6.85922 1.3755V9.6315C6.85922 10.0115 6.55119 10.3195 6.17122 10.3195H5.82722C5.44725 10.3195 5.13922 10.0115 5.13922 9.6315V1.3755Z" fill="white"/>
                                    <path d="M8.57922 3.0955C8.57922 2.71553 8.88725 2.4075 9.26722 2.4075H9.61122C9.99119 2.4075 10.2992 2.71553 10.2992 3.0955V7.9115C10.2992 8.29147 9.99119 8.5995 9.61122 8.5995H9.26722C8.88725 8.5995 8.57922 8.29147 8.57922 7.9115V3.0955Z" fill="white"/>
                                </svg>
                            `}
                    </div>
                </button>

                <div class="header-actions ask-action" @click=${() => this.invoke("toggle-feature", "ask")}>
                    <div class="action-text">
                        <div class="action-text-content">Ask</div>
                    </div>
                    <div class="icon-container">
                        ${this.renderShortcut(this.shortcuts.nextStep)}
                    </div>
                </div>

                <div class="header-actions" @click=${() => this.invoke("toggle-all-windows-visibility")}>
                    <div class="action-text">
                        <div class="action-text-content">Show/Hide</div>
                    </div>
                    <div class="icon-container">
                        ${this.renderShortcut(this.shortcuts.toggleVisibility)}
                    </div>
                </div>

                <button 
                    class="settings-button"
                    @mouseenter=${(e2) => this.showWindow("settings", e2.currentTarget)}
                    @mouseleave=${() => this.hideWindow("settings")}
                >
                    <div class="settings-icon">
                        <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M8.0013 3.16406C7.82449 3.16406 7.65492 3.2343 7.5299 3.35932C7.40487 3.48435 7.33464 3.65392 7.33464 3.83073C7.33464 4.00754 7.40487 4.17711 7.5299 4.30213C7.65492 4.42716 7.82449 4.4974 8.0013 4.4974C8.17811 4.4974 8.34768 4.42716 8.47271 4.30213C8.59773 4.17711 8.66797 4.00754 8.66797 3.83073C8.66797 3.65392 8.59773 3.48435 8.47271 3.35932C8.34768 3.2343 8.17811 3.16406 8.0013 3.16406ZM8.0013 7.83073C7.82449 7.83073 7.65492 7.90097 7.5299 8.02599C7.40487 8.15102 7.33464 8.32058 7.33464 8.4974C7.33464 8.67421 7.40487 8.84378 7.5299 8.9688C7.65492 9.09382 7.82449 9.16406 8.0013 9.16406C8.17811 9.16406 8.34768 9.09382 8.47271 8.9688C8.59773 8.84378 8.66797 8.67421 8.66797 8.4974C8.66797 8.32058 8.59773 8.15102 8.47271 8.02599C8.34768 7.90097 8.17811 7.83073 8.0013 7.83073ZM8.0013 12.4974C7.82449 12.4974 7.65492 12.5676 7.5299 12.6927C7.40487 12.8177 7.33464 12.9873 7.33464 13.1641C7.33464 13.3409 7.40487 13.5104 7.5299 13.6355C7.65492 13.7605 7.82449 13.8307 8.0013 13.8307C8.17811 13.8307 8.34768 13.7605 8.47271 13.6355C8.59773 13.5104 8.66797 13.3409 8.66797 13.1641C8.66797 12.9873 8.59773 12.8177 8.47271 12.6927C8.34768 12.5676 8.17811 12.4974 8.0013 12.4974Z" fill="white" stroke="white" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                </button>
            </div>
        `;
  }
};
customElements.define("main-header", MainHeader);

// src/app/ApiKeyHeader.js
var ApiKeyHeader = class extends ut {
  //////// after_modelStateService ////////
  static properties = {
    llmApiKey: { type: String },
    sttApiKey: { type: String },
    llmProvider: { type: String },
    sttProvider: { type: String },
    isLoading: { type: Boolean },
    errorMessage: { type: String },
    providers: { type: Object, state: true }
  };
  //////// after_modelStateService ////////
  static styles = r`
        :host {
            display: block;
            transform: translate3d(0, 0, 0);
            backface-visibility: hidden;
            transition: opacity 0.25s ease-out;
        }

        :host(.sliding-out) {
            animation: slideOutUp 0.3s ease-in forwards;
            will-change: opacity, transform;
        }

        :host(.hidden) {
            opacity: 0;
            pointer-events: none;
        }

        @keyframes slideOutUp {
            from {
                opacity: 1;
                transform: translateY(0);
            }
            to {
                opacity: 0;
                transform: translateY(-20px);
            }
        }

        * {
            font-family: 'Helvetica Neue', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            cursor: default;
            user-select: none;
            box-sizing: border-box;
        }

        .container {
            width: 350px;
            min-height: 260px;
            padding: 18px 20px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 16px;
            overflow: visible;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .container::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 16px;
            padding: 1px;
            background: linear-gradient(169deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0) 50%, rgba(255, 255, 255, 0.5) 100%);
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: destination-out;
            mask-composite: exclude;
            pointer-events: none;
        }

        .close-button {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 14px;
            height: 14px;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 3px;
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.15s ease;
            z-index: 10;
            font-size: 14px;
            line-height: 1;
            padding: 0;
        }

        .close-button:hover {
            background: rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 0.9);
        }

        .close-button:active {
            transform: scale(0.95);
        }

        .title {
            color: white;
            font-size: 16px;
            font-weight: 500; /* Medium */
            margin: 0;
            text-align: center;
            flex-shrink: 0;
        }

        .form-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            margin-top: auto;
        }

        .error-message {
            color: rgba(239, 68, 68, 0.9);
            font-weight: 500;
            font-size: 11px;
            height: 14px;
            text-align: center;
            margin-bottom: 4px;
        }

        .api-input {
            width: 100%;
            height: 34px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            border: none;
            padding: 0 10px;
            color: white;
            font-size: 12px;
            font-weight: 400; /* Regular */
            margin-bottom: 6px;
            text-align: center;
            user-select: text;
            cursor: text;
        }

        .api-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .api-input:focus {
            outline: none;
        }

        .providers-container { display: flex; gap: 12px; width: 100%; }
        .provider-column { flex: 1; display: flex; flex-direction: column; align-items: center; }
        .provider-label { color: rgba(255, 255, 255, 0.7); font-size: 11px; font-weight: 500; margin-bottom: 6px; }
        .api-input, .provider-select {
            width: 100%;
            height: 34px;
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 0 10px;
            color: white;
            font-size: 12px;
            margin-bottom: 6px;
        }
        .provider-select option { background: #1a1a1a; color: white; }

        .provider-select:hover {
            background-color: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .provider-select:focus {
            outline: none;
            background-color: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.4);
        }


        .action-button {
            width: 100%;
            height: 34px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 10px;
            color: white;
            font-size: 12px;
            font-weight: 500; /* Medium */
            cursor: pointer;
            transition: background 0.15s ease;
            position: relative;
            overflow: visible;
        }

        .action-button::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 10px;
            padding: 1px;
            background: linear-gradient(169deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0) 50%, rgba(255, 255, 255, 0.5) 100%);
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: destination-out;
            mask-composite: exclude;
            pointer-events: none;
        }

        .action-button:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .action-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .or-text {
            color: rgba(255, 255, 255, 0.5);
            font-size: 12px;
            font-weight: 500; /* Medium */
            margin: 10px 0;
        }

        
        /* ────────────────[ GLASS BYPASS ]─────────────── */
        :host-context(body.has-glass) .container,
        :host-context(body.has-glass) .api-input,
        :host-context(body.has-glass) .provider-select,
        :host-context(body.has-glass) .action-button,
        :host-context(body.has-glass) .close-button {
            background: transparent !important;
            border: none !important;
            box-shadow: none !important;
            filter: none !important;
            backdrop-filter: none !important;
        }

        :host-context(body.has-glass) .container::after,
        :host-context(body.has-glass) .action-button::after {
            display: none !important;
        }

        :host-context(body.has-glass) .action-button:hover,
        :host-context(body.has-glass) .provider-select:hover,
        :host-context(body.has-glass) .close-button:hover {
            background: transparent !important;
        }
    `;
  constructor() {
    super();
    this.dragState = null;
    this.wasJustDragged = false;
    this.isLoading = false;
    this.errorMessage = "";
    this.llmApiKey = "";
    this.sttApiKey = "";
    this.llmProvider = "openai";
    this.sttProvider = "openai";
    this.providers = { llm: [], stt: [] };
    this.loadProviderConfig();
    this.handleMouseMove = this.handleMouseMove.bind(this);
    this.handleMouseUp = this.handleMouseUp.bind(this);
    this.handleKeyPress = this.handleKeyPress.bind(this);
    this.handleSubmit = this.handleSubmit.bind(this);
    this.handleInput = this.handleInput.bind(this);
    this.handleAnimationEnd = this.handleAnimationEnd.bind(this);
    this.handleUsePicklesKey = this.handleUsePicklesKey.bind(this);
    this.handleProviderChange = this.handleProviderChange.bind(this);
  }
  reset() {
    this.apiKey = "";
    this.isLoading = false;
    this.errorMessage = "";
    this.validatedApiKey = null;
    this.selectedProvider = "openai";
    this.requestUpdate();
  }
  async loadProviderConfig() {
    if (!window.require) return;
    const { ipcRenderer } = window.require("electron");
    const config = await ipcRenderer.invoke("model:get-provider-config");
    const llmProviders = [];
    const sttProviders = [];
    for (const id in config) {
      if (id.includes("-glass")) continue;
      if (config[id].llmModels.length > 0) {
        llmProviders.push({ id, name: config[id].name });
      }
      if (config[id].sttModels.length > 0) {
        sttProviders.push({ id, name: config[id].name });
      }
    }
    this.providers = { llm: llmProviders, stt: sttProviders };
    if (llmProviders.length > 0) this.llmProvider = llmProviders[0].id;
    if (sttProviders.length > 0) this.sttProvider = sttProviders[0].id;
    this.requestUpdate();
  }
  async handleMouseDown(e2) {
    if (e2.target.tagName === "INPUT" || e2.target.tagName === "BUTTON" || e2.target.tagName === "SELECT") {
      return;
    }
    e2.preventDefault();
    const { ipcRenderer } = window.require("electron");
    const initialPosition = await ipcRenderer.invoke("get-header-position");
    this.dragState = {
      initialMouseX: e2.screenX,
      initialMouseY: e2.screenY,
      initialWindowX: initialPosition.x,
      initialWindowY: initialPosition.y,
      moved: false
    };
    window.addEventListener("mousemove", this.handleMouseMove);
    window.addEventListener("mouseup", this.handleMouseUp, { once: true });
  }
  handleMouseMove(e2) {
    if (!this.dragState) return;
    const deltaX = Math.abs(e2.screenX - this.dragState.initialMouseX);
    const deltaY = Math.abs(e2.screenY - this.dragState.initialMouseY);
    if (deltaX > 3 || deltaY > 3) {
      this.dragState.moved = true;
    }
    const newWindowX = this.dragState.initialWindowX + (e2.screenX - this.dragState.initialMouseX);
    const newWindowY = this.dragState.initialWindowY + (e2.screenY - this.dragState.initialMouseY);
    const { ipcRenderer } = window.require("electron");
    ipcRenderer.invoke("move-header-to", newWindowX, newWindowY);
  }
  handleMouseUp(e2) {
    if (!this.dragState) return;
    const wasDragged = this.dragState.moved;
    window.removeEventListener("mousemove", this.handleMouseMove);
    this.dragState = null;
    if (wasDragged) {
      this.wasJustDragged = true;
      setTimeout(() => {
        this.wasJustDragged = false;
      }, 200);
    }
  }
  handleInput(e2) {
    this.apiKey = e2.target.value;
    this.errorMessage = "";
    console.log("Input changed:", this.apiKey?.length || 0, "chars");
    this.requestUpdate();
    this.updateComplete.then(() => {
      const inputField = this.shadowRoot?.querySelector(".apikey-input");
      if (inputField && this.isInputFocused) {
        inputField.focus();
      }
    });
  }
  handleProviderChange(e2) {
    this.selectedProvider = e2.target.value;
    this.errorMessage = "";
    console.log("Provider changed to:", this.selectedProvider);
    this.requestUpdate();
  }
  handlePaste(e2) {
    e2.preventDefault();
    this.errorMessage = "";
    const clipboardText = (e2.clipboardData || window.clipboardData).getData("text");
    console.log("Paste event detected:", clipboardText?.substring(0, 10) + "...");
    if (clipboardText) {
      this.apiKey = clipboardText.trim();
      const inputElement = e2.target;
      inputElement.value = this.apiKey;
    }
    this.requestUpdate();
    this.updateComplete.then(() => {
      const inputField = this.shadowRoot?.querySelector(".apikey-input");
      if (inputField) {
        inputField.focus();
        inputField.setSelectionRange(inputField.value.length, inputField.value.length);
      }
    });
  }
  handleKeyPress(e2) {
    if (e2.key === "Enter") {
      e2.preventDefault();
      this.handleSubmit();
    }
  }
  //////// after_modelStateService ////////
  async handleSubmit() {
    console.log("[ApiKeyHeader] handleSubmit: Submitting API keys...");
    if (this.isLoading || !this.llmApiKey.trim() || !this.sttApiKey.trim()) {
      this.errorMessage = "Please enter keys for both LLM and STT.";
      return;
    }
    this.isLoading = true;
    this.errorMessage = "";
    this.requestUpdate();
    const { ipcRenderer } = window.require("electron");
    console.log("[ApiKeyHeader] handleSubmit: Validating LLM key...");
    const llmValidation = ipcRenderer.invoke("model:validate-key", { provider: this.llmProvider, key: this.llmApiKey.trim() });
    const sttValidation = ipcRenderer.invoke("model:validate-key", { provider: this.sttProvider, key: this.sttApiKey.trim() });
    const [llmResult, sttResult] = await Promise.all([llmValidation, sttValidation]);
    if (llmResult.success && sttResult.success) {
      console.log("[ApiKeyHeader] handleSubmit: Both LLM and STT keys are valid.");
      this.startSlideOutAnimation();
    } else {
      console.log("[ApiKeyHeader] handleSubmit: Validation failed.");
      let errorParts = [];
      if (!llmResult.success) errorParts.push(`LLM Key: ${llmResult.error || "Invalid"}`);
      if (!sttResult.success) errorParts.push(`STT Key: ${sttResult.error || "Invalid"}`);
      this.errorMessage = errorParts.join(" | ");
    }
    this.isLoading = false;
    this.requestUpdate();
  }
  //////// after_modelStateService ////////
  startSlideOutAnimation() {
    console.log("[ApiKeyHeader] startSlideOutAnimation: Starting slide out animation.");
    this.classList.add("sliding-out");
  }
  handleUsePicklesKey(e2) {
    e2.preventDefault();
    if (this.wasJustDragged) return;
    console.log("Requesting Firebase authentication from main process...");
    if (window.require) {
      window.require("electron").ipcRenderer.invoke("start-firebase-auth");
    }
  }
  handleClose() {
    console.log("Close button clicked");
    if (window.require) {
      window.require("electron").ipcRenderer.invoke("quit-application");
    }
  }
  //////// after_modelStateService ////////
  handleAnimationEnd(e2) {
    if (e2.target !== this || !this.classList.contains("sliding-out")) return;
    this.classList.remove("sliding-out");
    this.classList.add("hidden");
    window.require("electron").ipcRenderer.invoke("get-current-user").then((userState) => {
      console.log("[ApiKeyHeader] handleAnimationEnd: User state updated:", userState);
      this.stateUpdateCallback?.(userState);
    });
  }
  //////// after_modelStateService ////////
  connectedCallback() {
    super.connectedCallback();
    this.addEventListener("animationend", this.handleAnimationEnd);
  }
  disconnectedCallback() {
    super.disconnectedCallback();
    this.removeEventListener("animationend", this.handleAnimationEnd);
  }
  render() {
    const isButtonDisabled = this.isLoading || !this.llmApiKey.trim() || !this.sttApiKey.trim();
    return H`
        <div class="container" @mousedown=${this.handleMouseDown}>
            <h1 class="title">Enter Your API Keys</h1>

            <div class="providers-container">
                <div class="provider-column">
                    <div class="provider-label"></div>
                    <select class="provider-select" .value=${this.llmProvider} @change=${(e2) => this.llmProvider = e2.target.value} ?disabled=${this.isLoading}>
                        ${this.providers.llm.map((p2) => H`<option value=${p2.id}>${p2.name}</option>`)}
                    </select>
                    <input type="password" class="api-input" placeholder="LLM Provider API Key" .value=${this.llmApiKey} @input=${(e2) => this.llmApiKey = e2.target.value} ?disabled=${this.isLoading}>
                </div>

                <div class="provider-column">
                    <div class="provider-label"></div>
                    <select class="provider-select" .value=${this.sttProvider} @change=${(e2) => this.sttProvider = e2.target.value} ?disabled=${this.isLoading}>
                        ${this.providers.stt.map((p2) => H`<option value=${p2.id}>${p2.name}</option>`)}
                    </select>
                    <input type="password" class="api-input" placeholder="STT Provider API Key" .value=${this.sttApiKey} @input=${(e2) => this.sttApiKey = e2.target.value} ?disabled=${this.isLoading}>
                </div>
            </div>
            
            <div class="error-message">${this.errorMessage}</div>

            <button class="action-button" @click=${this.handleSubmit} ?disabled=${isButtonDisabled}>
                ${this.isLoading ? "Validating..." : "Confirm"}
            </button>
            <div class="or-text">or</div>
            <button class="action-button" @click=${this.handleUsePicklesKey}>Use Pickle's Key (Login)</button>
        </div>
    `;
  }
};
customElements.define("apikey-header", ApiKeyHeader);

// src/app/PermissionHeader.js
var PermissionHeader = class extends ut {
  static styles = r`
        :host {
            display: block;
            transform: translate3d(0, 0, 0);
            backface-visibility: hidden;
            transition: opacity 0.25s ease-out;
        }

        :host(.sliding-out) {
            animation: slideOutUp 0.3s ease-in forwards;
            will-change: opacity, transform;
        }

        :host(.hidden) {
            opacity: 0;
            pointer-events: none;
        }

        @keyframes slideOutUp {
            from {
                opacity: 1;
                transform: translateY(0);
            }
            to {
                opacity: 0;
                transform: translateY(-20px);
            }
        }

        * {
            font-family: 'Helvetica Neue', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            cursor: default;
            user-select: none;
            box-sizing: border-box;
        }

        .container {
            width: 285px;
            height: 220px;
            padding: 18px 20px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 16px;
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .container::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 16px;
            padding: 1px;
            background: linear-gradient(169deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0) 50%, rgba(255, 255, 255, 0.5) 100%);
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: destination-out;
            mask-composite: exclude;
            pointer-events: none;
        }

        .close-button {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 14px;
            height: 14px;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 3px;
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.15s ease;
            z-index: 10;
            font-size: 14px;
            line-height: 1;
            padding: 0;
        }

        .close-button:hover {
            background: rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 0.9);
        }

        .close-button:active {
            transform: scale(0.95);
        }

        .title {
            color: white;
            font-size: 16px;
            font-weight: 500;
            margin: 0;
            text-align: center;
            flex-shrink: 0;
        }

        .form-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            margin-top: auto;
        }

        .subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 11px;
            font-weight: 400;
            text-align: center;
            margin-bottom: 12px;
            line-height: 1.3;
        }

        .permission-status {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin-bottom: 12px;
            min-height: 20px;
        }

        .permission-item {
            display: flex;
            align-items: center;
            gap: 6px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 11px;
            font-weight: 400;
        }

        .permission-item.granted {
            color: rgba(34, 197, 94, 0.9);
        }

        .permission-icon {
            width: 12px;
            height: 12px;
            opacity: 0.8;
        }

        .check-icon {
            width: 12px;
            height: 12px;
            color: rgba(34, 197, 94, 0.9);
        }

        .action-button {
            width: 100%;
            height: 34px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 10px;
            color: white;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.15s ease;
            position: relative;
            overflow: hidden;
            margin-bottom: 6px;
        }

        .action-button::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 10px;
            padding: 1px;
            background: linear-gradient(169deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0) 50%, rgba(255, 255, 255, 0.5) 100%);
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: destination-out;
            mask-composite: exclude;
            pointer-events: none;
        }

        .action-button:hover:not(:disabled) {
            background: rgba(255, 255, 255, 0.3);
        }

        .action-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .continue-button {
            width: 100%;
            height: 34px;
            background: rgba(34, 197, 94, 0.8);
            border: none;
            border-radius: 10px;
            color: white;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.15s ease;
            position: relative;
            overflow: hidden;
            margin-top: 4px;
        }

        .continue-button::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 10px;
            padding: 1px;
            background: linear-gradient(169deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0) 50%, rgba(255, 255, 255, 0.5) 100%);
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: destination-out;
            mask-composite: exclude;
            pointer-events: none;
        }

        .continue-button:hover:not(:disabled) {
            background: rgba(34, 197, 94, 0.9);
        }

        .continue-button:disabled {
            background: rgba(255, 255, 255, 0.2);
            cursor: not-allowed;
        }

        /* ────────────────[ GLASS BYPASS ]─────────────── */
        :host-context(body.has-glass) .container,
        :host-context(body.has-glass) .action-button,
        :host-context(body.has-glass) .continue-button,
        :host-context(body.has-glass) .close-button {
            background: transparent !important;
            border: none !important;
            box-shadow: none !important;
            filter: none !important;
            backdrop-filter: none !important;
        }

        :host-context(body.has-glass) .container::after,
        :host-context(body.has-glass) .action-button::after,
        :host-context(body.has-glass) .continue-button::after {
            display: none !important;
        }

        :host-context(body.has-glass) .action-button:hover,
        :host-context(body.has-glass) .continue-button:hover,
        :host-context(body.has-glass) .close-button:hover {
            background: transparent !important;
        }
    `;
  static properties = {
    microphoneGranted: { type: String },
    screenGranted: { type: String },
    isChecking: { type: String },
    continueCallback: { type: Function }
  };
  constructor() {
    super();
    this.microphoneGranted = "unknown";
    this.screenGranted = "unknown";
    this.isChecking = false;
    this.continueCallback = null;
    this.handleMouseMove = this.handleMouseMove.bind(this);
    this.handleMouseUp = this.handleMouseUp.bind(this);
  }
  async connectedCallback() {
    super.connectedCallback();
    await this.checkPermissions();
    this.permissionCheckInterval = setInterval(() => {
      this.checkPermissions();
    }, 1e3);
  }
  disconnectedCallback() {
    super.disconnectedCallback();
    if (this.permissionCheckInterval) {
      clearInterval(this.permissionCheckInterval);
    }
  }
  async handleMouseDown(e2) {
    if (e2.target.tagName === "BUTTON") {
      return;
    }
    e2.preventDefault();
    const { ipcRenderer } = window.require("electron");
    const initialPosition = await ipcRenderer.invoke("get-header-position");
    this.dragState = {
      initialMouseX: e2.screenX,
      initialMouseY: e2.screenY,
      initialWindowX: initialPosition.x,
      initialWindowY: initialPosition.y,
      moved: false
    };
    window.addEventListener("mousemove", this.handleMouseMove);
    window.addEventListener("mouseup", this.handleMouseUp, { once: true });
  }
  handleMouseMove(e2) {
    if (!this.dragState) return;
    const deltaX = Math.abs(e2.screenX - this.dragState.initialMouseX);
    const deltaY = Math.abs(e2.screenY - this.dragState.initialMouseY);
    if (deltaX > 3 || deltaY > 3) {
      this.dragState.moved = true;
    }
    const newWindowX = this.dragState.initialWindowX + (e2.screenX - this.dragState.initialMouseX);
    const newWindowY = this.dragState.initialWindowY + (e2.screenY - this.dragState.initialMouseY);
    const { ipcRenderer } = window.require("electron");
    ipcRenderer.invoke("move-header-to", newWindowX, newWindowY);
  }
  handleMouseUp(e2) {
    if (!this.dragState) return;
    const wasDragged = this.dragState.moved;
    window.removeEventListener("mousemove", this.handleMouseMove);
    this.dragState = null;
    if (wasDragged) {
      this.wasJustDragged = true;
      setTimeout(() => {
        this.wasJustDragged = false;
      }, 200);
    }
  }
  async checkPermissions() {
    if (!window.require || this.isChecking) return;
    this.isChecking = true;
    const { ipcRenderer } = window.require("electron");
    try {
      const permissions = await ipcRenderer.invoke("check-system-permissions");
      console.log("[PermissionHeader] Permission check result:", permissions);
      const prevMic = this.microphoneGranted;
      const prevScreen = this.screenGranted;
      this.microphoneGranted = permissions.microphone;
      this.screenGranted = permissions.screen;
      if (prevMic !== this.microphoneGranted || prevScreen !== this.screenGranted) {
        console.log("[PermissionHeader] Permission status changed, updating UI");
        this.requestUpdate();
      }
      if (this.microphoneGranted === "granted" && this.screenGranted === "granted" && this.continueCallback) {
        console.log("[PermissionHeader] All permissions granted, proceeding automatically");
        setTimeout(() => this.handleContinue(), 500);
      }
    } catch (error) {
      console.error("[PermissionHeader] Error checking permissions:", error);
    } finally {
      this.isChecking = false;
    }
  }
  async handleMicrophoneClick() {
    if (!window.require || this.microphoneGranted === "granted" || this.wasJustDragged) return;
    console.log("[PermissionHeader] Requesting microphone permission...");
    const { ipcRenderer } = window.require("electron");
    try {
      const result = await ipcRenderer.invoke("check-system-permissions");
      console.log("[PermissionHeader] Microphone permission result:", result);
      if (result.microphone === "granted") {
        this.microphoneGranted = "granted";
        this.requestUpdate();
        return;
      }
      if (result.microphone === "not-determined" || result.microphone === "denied" || result.microphone === "unknown" || result.microphone === "restricted") {
        const res = await ipcRenderer.invoke("request-microphone-permission");
        if (res.status === "granted" || res.success === true) {
          this.microphoneGranted = "granted";
          this.requestUpdate();
          return;
        }
      }
    } catch (error) {
      console.error("[PermissionHeader] Error requesting microphone permission:", error);
    }
  }
  async handleScreenClick() {
    if (!window.require || this.screenGranted === "granted" || this.wasJustDragged) return;
    console.log("[PermissionHeader] Checking screen recording permission...");
    const { ipcRenderer } = window.require("electron");
    try {
      const permissions = await ipcRenderer.invoke("check-system-permissions");
      console.log("[PermissionHeader] Screen permission check result:", permissions);
      if (permissions.screen === "granted") {
        this.screenGranted = "granted";
        this.requestUpdate();
        return;
      }
      if (permissions.screen === "not-determined" || permissions.screen === "denied" || permissions.screen === "unknown" || permissions.screen === "restricted") {
        console.log("[PermissionHeader] Opening screen recording preferences...");
        await ipcRenderer.invoke("open-system-preferences", "screen-recording");
      }
    } catch (error) {
      console.error("[PermissionHeader] Error opening screen recording preferences:", error);
    }
  }
  async handleContinue() {
    if (this.continueCallback && this.microphoneGranted === "granted" && this.screenGranted === "granted" && !this.wasJustDragged) {
      if (window.require) {
        const { ipcRenderer } = window.require("electron");
        try {
          await ipcRenderer.invoke("mark-permissions-completed");
          console.log("[PermissionHeader] Marked permissions as completed");
        } catch (error) {
          console.error("[PermissionHeader] Error marking permissions as completed:", error);
        }
      }
      this.continueCallback();
    }
  }
  handleClose() {
    console.log("Close button clicked");
    if (window.require) {
      window.require("electron").ipcRenderer.invoke("quit-application");
    }
  }
  render() {
    const allGranted = this.microphoneGranted === "granted" && this.screenGranted === "granted";
    return H`
            <div class="container" @mousedown=${this.handleMouseDown}>
                <button class="close-button" @click=${this.handleClose} title="Close application">
                    <svg width="8" height="8" viewBox="0 0 10 10" fill="currentColor">
                        <path d="M1 1L9 9M9 1L1 9" stroke="currentColor" stroke-width="1.2" />
                    </svg>
                </button>
                <h1 class="title">Permission Setup Required</h1>

                <div class="form-content">
                    <div class="subtitle">Grant access to microphone and screen recording to continue</div>
                    
                    <div class="permission-status">
                        <div class="permission-item ${this.microphoneGranted === "granted" ? "granted" : ""}">
                            ${this.microphoneGranted === "granted" ? H`
                                <svg class="check-icon" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                                <span>Microphone ✓</span>
                            ` : H`
                                <svg class="permission-icon" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clip-rule="evenodd" />
                                </svg>
                                <span>Microphone</span>
                            `}
                        </div>
                        
                        <div class="permission-item ${this.screenGranted === "granted" ? "granted" : ""}">
                            ${this.screenGranted === "granted" ? H`
                                <svg class="check-icon" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                                <span>Screen ✓</span>
                            ` : H`
                                <svg class="permission-icon" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clip-rule="evenodd" />
                                </svg>
                                <span>Screen Recording</span>
                            `}
                        </div>
                    </div>

                    ${this.microphoneGranted !== "granted" ? H`
                        <button 
                            class="action-button" 
                            @click=${this.handleMicrophoneClick}
                        >
                            Grant Microphone Access
                        </button>
                    ` : ""}

                    ${this.screenGranted !== "granted" ? H`
                        <button 
                            class="action-button" 
                            @click=${this.handleScreenClick}
                        >
                            Grant Screen Recording Access
                        </button>
                    ` : ""}

                    ${allGranted ? H`
                        <button 
                            class="continue-button" 
                            @click=${this.handleContinue}
                        >
                            Continue to Pickle Glass
                        </button>
                    ` : ""}
                </div>
            </div>
        `;
  }
};
customElements.define("permission-setup", PermissionHeader);

// src/app/HeaderController.js
var HeaderTransitionManager = class {
  constructor() {
    this.headerContainer = document.getElementById("header-container");
    this.currentHeaderType = null;
    this.apiKeyHeader = null;
    this.mainHeader = null;
    this.permissionHeader = null;
    this.ensureHeader = (type) => {
      console.log("[HeaderController] ensureHeader: Ensuring header of type:", type);
      if (this.currentHeaderType === type) {
        console.log("[HeaderController] ensureHeader: Header of type:", type, "already exists.");
        return;
      }
      this.headerContainer.innerHTML = "";
      this.apiKeyHeader = null;
      this.mainHeader = null;
      this.permissionHeader = null;
      if (type === "apikey") {
        this.apiKeyHeader = document.createElement("apikey-header");
        this.apiKeyHeader.stateUpdateCallback = (userState) => this.handleStateUpdate(userState);
        this.headerContainer.appendChild(this.apiKeyHeader);
      } else if (type === "permission") {
        this.permissionHeader = document.createElement("permission-setup");
        this.permissionHeader.continueCallback = () => this.transitionToMainHeader();
        this.headerContainer.appendChild(this.permissionHeader);
      } else {
        this.mainHeader = document.createElement("main-header");
        this.headerContainer.appendChild(this.mainHeader);
        this.mainHeader.startSlideInAnimation?.();
      }
      this.currentHeaderType = type;
      this.notifyHeaderState(type === "permission" ? "apikey" : type);
    };
    console.log("[HeaderController] Manager initialized");
    this._bootstrap();
    if (window.require) {
      const { ipcRenderer } = window.require("electron");
      ipcRenderer.on("user-state-changed", (event, userState) => {
        console.log("[HeaderController] Received user state change:", userState);
        this.handleStateUpdate(userState);
      });
      ipcRenderer.on("auth-failed", (event, { message }) => {
        console.error("[HeaderController] Received auth failure from main process:", message);
        if (this.apiKeyHeader) {
          this.apiKeyHeader.errorMessage = "Authentication failed. Please try again.";
          this.apiKeyHeader.isLoading = false;
        }
      });
      ipcRenderer.on("force-show-apikey-header", async () => {
        console.log("[HeaderController] Received broadcast to show apikey header. Switching now.");
        await this._resizeForApiKey();
        this.ensureHeader("apikey");
      });
    }
  }
  notifyHeaderState(stateOverride) {
    const state = stateOverride || this.currentHeaderType || "apikey";
    if (window.require) {
      window.require("electron").ipcRenderer.send("header-state-changed", state);
    }
  }
  async _bootstrap() {
    if (window.require) {
      const userState = await window.require("electron").ipcRenderer.invoke("get-current-user");
      console.log("[HeaderController] Bootstrapping with initial user state:", userState);
      this.handleStateUpdate(userState);
    } else {
      this.ensureHeader("apikey");
    }
  }
  //////// after_modelStateService ////////
  async handleStateUpdate(userState) {
    const { ipcRenderer } = window.require("electron");
    const isConfigured = await ipcRenderer.invoke("model:are-providers-configured");
    if (isConfigured) {
      const { isLoggedIn } = userState;
      if (isLoggedIn) {
        const permissionResult = await this.checkPermissions();
        if (permissionResult.success) {
          this.transitionToMainHeader();
        } else {
          this.transitionToPermissionHeader();
        }
      } else {
        this.transitionToMainHeader();
      }
    } else {
      await this._resizeForApiKey();
      this.ensureHeader("apikey");
    }
  }
  //////// after_modelStateService ////////
  async transitionToPermissionHeader() {
    if (this.currentHeaderType === "permission") {
      console.log("[HeaderController] Already showing permission setup, skipping transition");
      return;
    }
    if (window.require) {
      const { ipcRenderer } = window.require("electron");
      try {
        const permissionsCompleted = await ipcRenderer.invoke("check-permissions-completed");
        if (permissionsCompleted) {
          console.log("[HeaderController] Permissions were previously completed, checking current status...");
          const permissionResult = await this.checkPermissions();
          if (permissionResult.success) {
            this.transitionToMainHeader();
            return;
          }
          console.log("[HeaderController] Permissions were revoked, showing setup again");
        }
      } catch (error) {
        console.error("[HeaderController] Error checking permissions completed status:", error);
      }
    }
    await this._resizeForPermissionHeader();
    this.ensureHeader("permission");
  }
  async transitionToMainHeader(animate = true) {
    if (this.currentHeaderType === "main") {
      return this._resizeForMain();
    }
    await this._resizeForMain();
    this.ensureHeader("main");
  }
  _resizeForMain() {
    if (!window.require) return;
    return window.require("electron").ipcRenderer.invoke("resize-header-window", { width: 353, height: 47 }).catch(() => {
    });
  }
  async _resizeForApiKey() {
    if (!window.require) return;
    return window.require("electron").ipcRenderer.invoke("resize-header-window", { width: 350, height: 300 }).catch(() => {
    });
  }
  async _resizeForPermissionHeader() {
    if (!window.require) return;
    return window.require("electron").ipcRenderer.invoke("resize-header-window", { width: 285, height: 220 }).catch(() => {
    });
  }
  async checkPermissions() {
    if (!window.require) {
      return { success: true };
    }
    const { ipcRenderer } = window.require("electron");
    try {
      const permissions = await ipcRenderer.invoke("check-system-permissions");
      console.log("[HeaderController] Current permissions:", permissions);
      if (!permissions.needsSetup) {
        return { success: true };
      }
      let errorMessage = "";
      if (!permissions.microphone && !permissions.screen) {
        errorMessage = "Microphone and screen recording access required";
      }
      return {
        success: false,
        error: errorMessage
      };
    } catch (error) {
      console.error("[HeaderController] Error checking permissions:", error);
      return {
        success: false,
        error: "Failed to check permissions"
      };
    }
  }
};
window.addEventListener("DOMContentLoaded", () => {
  new HeaderTransitionManager();
});
/**
 * @license
 * Copyright 2019 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */
/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */
/**
 * @license
 * Copyright 2022 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */
//# sourceMappingURL=header.js.map
